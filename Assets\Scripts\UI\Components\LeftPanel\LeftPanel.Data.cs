using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Data;
using BlastingDesign.Utils;
using System.Collections.Generic;
using System.Linq;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// LeftPanel - 数据处理部分
    /// </summary>
    public partial class LeftPanel
    {
        #region Data Conversion

        /// <summary>
        /// 更新树形数据
        /// </summary>
        private void UpdateTreeData()
        {
            treeDataCache.Clear();

            if (currentProject == null)
            {
                SetTreeData(treeDataCache);
                return;
            }

            // 转换项目数据为树形数据
            var projectTreeData = ConvertProjectToTreeData(currentProject);
            treeDataCache.Add(projectTreeData);

            // 设置到树组件
            SetTreeData(treeDataCache);
        }

        /// <summary>
        /// 设置树数据到可用的树组件
        /// </summary>
        private void SetTreeData(List<DaisyTreeData> data)
        {
            if (blastingTree != null)
            {
                // 使用DaisyTree
                blastingTree.SetData(data);
            }
            else if (fallbackTreeView != null)
            {
                // 使用备用TreeView - 需要转换数据格式
                SetFallbackTreeData(data);
            }
        }

        /// <summary>
        /// 为备用TreeView设置数据
        /// </summary>
        private void SetFallbackTreeData(List<DaisyTreeData> data)
        {
            try
            {
                // 转换DaisyTreeData为TreeView格式
                var treeViewData = ConvertToTreeViewData(data);

                // 设置数据到TreeView
                fallbackTreeView.SetRootItems(treeViewData);
                fallbackTreeView.Rebuild();

                Logging.LogInfo("LeftPanel", "备用TreeView数据设置完成");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("LeftPanel", $"设置备用TreeView数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 将DaisyTreeData转换为TreeView数据格式
        /// </summary>
        private List<TreeViewItemData<HierarchyItemData>> ConvertToTreeViewData(List<DaisyTreeData> daisyData)
        {
            var result = new List<TreeViewItemData<HierarchyItemData>>();
            int id = 1;

            foreach (var item in daisyData)
            {
                var treeItem = ConvertDaisyTreeDataToTreeViewData(item, ref id);
                result.Add(treeItem);
            }

            return result;
        }

        /// <summary>
        /// 递归转换单个DaisyTreeData项
        /// </summary>
        private TreeViewItemData<HierarchyItemData> ConvertDaisyTreeDataToTreeViewData(DaisyTreeData daisyItem, ref int id)
        {
            // 创建HierarchyItemData
            var hierarchyItem = new HierarchyItemData(daisyItem.Text, HierarchyItemType.GameObject);

            // 转换子项
            var children = new List<TreeViewItemData<HierarchyItemData>>();
            if (daisyItem.Children != null)
            {
                foreach (var child in daisyItem.Children)
                {
                    var childItem = ConvertDaisyTreeDataToTreeViewData(child, ref id);
                    children.Add(childItem);
                }
            }

            return new TreeViewItemData<HierarchyItemData>(id++, hierarchyItem, children);
        }

        /// <summary>
        /// 将项目数据转换为树形数据
        /// </summary>
        private DaisyTreeData ConvertProjectToTreeData(ProjectData project)
        {
            var projectNode = new DaisyTreeData(project.id, project.name, project.GetIcon());

            // 添加项目级操作
            foreach (var action in project.GetAvailableActions())
            {
                projectNode.AddAction(action, GetActionText(action), GetActionIcon(action));
            }

            // 添加爆区
            foreach (var blastingArea in project.blastingAreas)
            {
                var areaNode = ConvertBlastingAreaToTreeData(blastingArea);
                projectNode.AddChild(areaNode);
            }

            return projectNode;
        }

        /// <summary>
        /// 将爆区数据转换为树形数据
        /// </summary>
        private DaisyTreeData ConvertBlastingAreaToTreeData(BlastingAreaData area)
        {
            var areaNode = new DaisyTreeData(area.id, area.name, area.GetIcon());

            // 添加爆区级操作
            foreach (var action in area.GetAvailableActions())
            {
                areaNode.AddAction(action, GetActionText(action), GetActionIcon(action));
            }

            // 添加测量点系统
            if (area.measurePoints.Any())
            {
                var measurePointsNode = new DaisyTreeData($"{area.id}_measure_points", "测量点系统", "📍");
                measurePointsNode.AddAction("visibility", "显示/隐藏", "👁️");
                measurePointsNode.AddAction("add", "添加测量点", "➕");

                foreach (var measurePoint in area.measurePoints)
                {
                    var pointNode = ConvertMeasurePointToTreeData(measurePoint);
                    measurePointsNode.AddChild(pointNode);
                }
                areaNode.AddChild(measurePointsNode);
            }

            // 添加边界线系统
            if (area.boundaryLines.Any())
            {
                var boundaryNode = new DaisyTreeData($"{area.id}_boundaries", "边界线", "📏");
                boundaryNode.AddAction("visibility", "显示/隐藏", "👁️");
                boundaryNode.AddAction("add", "添加边界线", "➕");

                foreach (var boundary in area.boundaryLines)
                {
                    var lineNode = ConvertBoundaryLineToTreeData(boundary);
                    boundaryNode.AddChild(lineNode);
                }
                areaNode.AddChild(boundaryNode);
            }

            // 添加钻孔系统
            if (area.drillHoles.Any())
            {
                var drillHolesNode = new DaisyTreeData($"{area.id}_drill_holes", "钻孔", "🕳️");
                drillHolesNode.AddAction("visibility", "显示/隐藏", "👁️");
                drillHolesNode.AddAction("add", "添加钻孔", "➕");
                drillHolesNode.AddAction("design", "设计钻孔", "📐");

                foreach (var drillHole in area.drillHoles)
                {
                    var holeNode = ConvertDrillHoleToTreeData(drillHole);
                    drillHolesNode.AddChild(holeNode);
                }
                areaNode.AddChild(drillHolesNode);
            }

            // 添加爆破块体系统
            if (area.blastBlocks.Any())
            {
                var blocksNode = new DaisyTreeData($"{area.id}_blast_blocks", "爆破块体", "🧱");
                blocksNode.AddAction("visibility", "显示/隐藏", "👁️");
                blocksNode.AddAction("sequence", "设置起爆顺序", "🔢");

                foreach (var block in area.blastBlocks)
                {
                    var blockNode = ConvertBlastBlockToTreeData(block);
                    blocksNode.AddChild(blockNode);
                }
                areaNode.AddChild(blocksNode);
            }

            return areaNode;
        }

        /// <summary>
        /// 将测量点转换为树形数据
        /// </summary>
        private DaisyTreeData ConvertMeasurePointToTreeData(MeasurePointData point)
        {
            var pointNode = new DaisyTreeData(point.id, point.name, point.GetIcon());

            foreach (var action in point.GetAvailableActions())
            {
                pointNode.AddAction(action, GetActionText(action), GetActionIcon(action));
            }

            return pointNode;
        }

        /// <summary>
        /// 将边界线转换为树形数据
        /// </summary>
        private DaisyTreeData ConvertBoundaryLineToTreeData(BoundaryLineData boundary)
        {
            var boundaryNode = new DaisyTreeData(boundary.id, boundary.name, boundary.GetIcon());

            foreach (var action in boundary.GetAvailableActions())
            {
                boundaryNode.AddAction(action, GetActionText(action), GetActionIcon(action));
            }

            return boundaryNode;
        }

        /// <summary>
        /// 将钻孔转换为树形数据
        /// </summary>
        private DaisyTreeData ConvertDrillHoleToTreeData(DrillHoleData hole)
        {
            var holeNode = new DaisyTreeData(hole.id, hole.name, hole.GetIcon());

            foreach (var action in hole.GetAvailableActions())
            {
                holeNode.AddAction(action, GetActionText(action), GetActionIcon(action));
            }

            return holeNode;
        }

        /// <summary>
        /// 将爆破块体转换为树形数据
        /// </summary>
        private DaisyTreeData ConvertBlastBlockToTreeData(BlastBlockData block)
        {
            var blockNode = new DaisyTreeData(block.id, block.name, block.GetIcon());

            foreach (var action in block.GetAvailableActions())
            {
                blockNode.AddAction(action, GetActionText(action), GetActionIcon(action));
            }

            return blockNode;
        }

        /// <summary>
        /// 从树形数据获取爆破元素
        /// </summary>
        private BlastingElement GetBlastingElementFromTreeData(DaisyTreeData treeData)
        {
            if (currentProject == null) return null;

            // 递归查找匹配的元素
            return FindElementById(currentProject, treeData.Id);
        }

        /// <summary>
        /// 递归查找元素
        /// </summary>
        private BlastingElement FindElementById(BlastingElement element, string id)
        {
            if (element.id == id) return element;

            switch (element)
            {
                case ProjectData project:
                    foreach (var area in project.blastingAreas)
                    {
                        var found = FindElementById(area, id);
                        if (found != null) return found;
                    }
                    break;

                case BlastingAreaData area:
                    foreach (var point in area.measurePoints)
                    {
                        var found = FindElementById(point, id);
                        if (found != null) return found;
                    }
                    foreach (var boundary in area.boundaryLines)
                    {
                        var found = FindElementById(boundary, id);
                        if (found != null) return found;
                    }
                    foreach (var hole in area.drillHoles)
                    {
                        var found = FindElementById(hole, id);
                        if (found != null) return found;
                    }
                    foreach (var block in area.blastBlocks)
                    {
                        var found = FindElementById(block, id);
                        if (found != null) return found;
                    }
                    break;
            }

            return null;
        }

        /// <summary>
        /// 将爆破元素转换为层次结构项目（向后兼容）
        /// </summary>
        private HierarchyItemData ConvertToHierarchyItem(BlastingElement element)
        {
            var hierarchyType = element.type switch
            {
                BlastingElementType.Project => HierarchyItemType.Scene,
                BlastingElementType.BlastingArea => HierarchyItemType.GameObject,
                BlastingElementType.MeasurePoint => HierarchyItemType.Component,
                BlastingElementType.BoundaryLine => HierarchyItemType.Component,
                BlastingElementType.DrillHole => HierarchyItemType.Component,
                BlastingElementType.BlastBlock => HierarchyItemType.GameObject,
                _ => HierarchyItemType.Asset
            };

            return new HierarchyItemData(element.name, hierarchyType)
            {
                userData = element
            };
        }

        #endregion

        #region Sample Data

        /// <summary>
        /// 加载示例项目数据
        /// </summary>
        private void LoadSampleProject()
        {
            var sampleProject = CreateSampleProject();
            SetProject(sampleProject);
        }

        /// <summary>
        /// 创建示例项目
        /// </summary>
        private ProjectData CreateSampleProject()
        {
            var project = new ProjectData("sample_project", "示例爆破工程")
            {
                description = "这是一个示例爆破设计项目，展示了系统的基本功能",
                createdBy = "系统"
            };

            // 创建爆区1
            var area1 = new BlastingAreaData("area_1", "1号爆区")
            {
                area = 5000f,
                rockType = "花岗岩"
            };

            // 添加测量点
            area1.measurePoints.Add(new MeasurePointData("mp_001", "控制点CP-001")
            {
                elevation = 100f,
                pointType = MeasurePointType.Control
            });
            area1.measurePoints.Add(new MeasurePointData("mp_002", "测量点MP-002")
            {
                elevation = 98f,
                pointType = MeasurePointType.Survey
            });

            // 添加边界线
            area1.boundaryLines.Add(new BoundaryLineData("bl_001", "开挖边界线1")
            {
                boundaryType = BoundaryType.Excavation,
                length = 200f
            });
            area1.boundaryLines.Add(new BoundaryLineData("bl_002", "安全边界线1")
            {
                boundaryType = BoundaryType.Safety,
                length = 150f
            });

            // 添加钻孔
            area1.drillHoles.Add(new DrillHoleData("dh_001", "生产孔PH-001")
            {
                depth = 12f,
                diameter = 0.11f,
                holeType = DrillHoleType.Production
            });
            area1.drillHoles.Add(new DrillHoleData("dh_002", "周边孔BH-001")
            {
                depth = 12f,
                diameter = 0.08f,
                holeType = DrillHoleType.Perimeter
            });

            // 添加爆破块体
            area1.blastBlocks.Add(new BlastBlockData("bb_001", "爆破块体1")
            {
                volume = 500f,
                sequence = 1
            });

            project.blastingAreas.Add(area1);

            return project;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// 获取操作文本
        /// </summary>
        private string GetActionText(string actionId)
        {
            return actionId switch
            {
                "visibility" => "显示/隐藏",
                "edit" => "编辑",
                "rename" => "重命名",
                "delete" => "删除",
                "properties" => "属性",
                "calculate" => "计算",
                "export" => "导出",
                "design" => "设计",
                "charge" => "装药",
                "timing" => "时序",
                "sequence" => "起爆顺序",
                "simulate" => "模拟",
                "add" => "添加",
                _ => actionId
            };
        }

        /// <summary>
        /// 获取操作图标
        /// </summary>
        private string GetActionIcon(string actionId)
        {
            return actionId switch
            {
                "visibility" => "👁️",
                "edit" => "✏️",
                "rename" => "📝",
                "delete" => "🗑️",
                "properties" => "⚙️",
                "calculate" => "🧮",
                "export" => "📤",
                "design" => "📐",
                "charge" => "💥",
                "timing" => "⏱️",
                "sequence" => "🔢",
                "simulate" => "🎮",
                "add" => "➕",
                _ => "🔧"
            };
        }

        #endregion
    }
}