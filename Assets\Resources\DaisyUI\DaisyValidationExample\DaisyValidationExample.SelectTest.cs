using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - DaisySelect组件测试
    /// 测试DaisySelect组件的所有功能
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region DaisySelect组件测试

        /// <summary>
        /// 测试DaisySelect组件
        /// </summary>
        private void TestDaisySelect()
        {
            LogTest("DaisySelect 组件测试");

            try
            {
                // 测试基础选择器创建
                var basicSelect = DaisySelect.Create("选项1", "选项2", "选项3");
                if (basicSelect == null)
                {
                    throw new System.Exception("基础选择器创建失败");
                }

                // 测试带标签的选择器
                var labelSelect = DaisySelect.Create("选择类型", "类型A", "类型B", "类型C");
                if (labelSelect == null || labelSelect.Label != "选择类型")
                {
                    throw new System.Exception("带标签选择器创建失败");
                }

                // 测试选项管理
                var optionSelect = DaisySelect.Create()
                    .SetLabel("动态选项管理")
                    .AddOption("新选项1")
                    .AddOption("新选项2")
                    .SetOptions("替换选项1", "替换选项2", "替换选项3");

                if (optionSelect.Options.Count != 3)
                {
                    throw new System.Exception("选择器选项管理失败");
                }

                // 测试值设置
                var valueSelect = DaisySelect.Create("值1", "值2", "值3")
                    .SetLabel("预设值测试")
                    .SetValue("值2")
                    .OnValueChanged(value => Logging.LogInfo("DaisySelect", $"选择值改变: {value}"));

                if (valueSelect.Value != "值2")
                {
                    throw new System.Exception("选择器值设置失败");
                }

                // 测试选择器状态
                var errorSelect = DaisySelect.Create("错误", "正常", "警告")
                    .SetLabel("错误状态")
                    .SetError()
                    .SetHelperText("这是错误提示信息");

                var successSelect = DaisySelect.Create("成功1", "成功2", "成功3")
                    .SetLabel("成功状态")
                    .SetSuccess()
                    .SetHelperText("这是成功提示信息");

                var warningSelect = DaisySelect.Create("警告1", "警告2", "警告3")
                    .SetLabel("警告状态")
                    .SetWarning()
                    .SetHelperText("这是警告提示信息");

                var infoSelect = DaisySelect.Create("信息1", "信息2", "信息3")
                    .SetLabel("信息状态")
                    .SetInfo()
                    .SetHelperText("这是信息提示");

                if (!errorSelect.ClassListContains("daisy-select-error") ||
                    !successSelect.ClassListContains("daisy-select-success") ||
                    !warningSelect.ClassListContains("daisy-select-warning") ||
                    !infoSelect.ClassListContains("daisy-select-info"))
                {
                    throw new System.Exception("选择器状态设置失败");
                }

                // 测试修饰符
                var borderedSelect = DaisySelect.Create("修饰符1", "修饰符2")
                    .SetBordered()
                    .SetLabel("边框选择器");

                var ghostSelect = DaisySelect.Create("幽灵1", "幽灵2")
                    .SetGhost()
                    .SetLabel("幽灵选择器");

                if (!borderedSelect.ClassListContains("daisy-select-bordered") ||
                    !ghostSelect.ClassListContains("daisy-select-ghost"))
                {
                    throw new System.Exception("选择器修饰符设置失败");
                }

                // 测试多选模式
                var multiSelect = DaisySelect.Create("多选1", "多选2", "多选3", "多选4")
                    .SetMultiple()
                    .SetLabel("多选选择器")
                    .SetHelperText("支持选择多个选项");

                if (!multiSelect.AllowMultiple || !multiSelect.ClassListContains("daisy-select-multiple"))
                {
                    throw new System.Exception("多选选择器设置失败");
                }

                // 测试禁用状态
                var disabledSelect = DaisySelect.Create("禁用1", "禁用2", "禁用3")
                    .SetLabel("禁用选择器")
                    .SetDisabled();

                if (!disabledSelect.ClassListContains("daisy-select-disabled"))
                {
                    throw new System.Exception("禁用选择器设置失败");
                }

                // 测试尺寸变体
                var largeSelect = DaisySelect.Create("大选项1", "大选项2").WithSize("lg");
                var smallSelect = DaisySelect.Create("小选项1", "小选项2").WithSize("sm");
                var extraSmallSelect = DaisySelect.Create("超小1", "超小2").WithSize("xs");

                if (!largeSelect.ClassListContains("daisy-select-lg") ||
                    !smallSelect.ClassListContains("daisy-select-sm") ||
                    !extraSmallSelect.ClassListContains("daisy-select-xs"))
                {
                    throw new System.Exception("选择器尺寸设置失败");
                }

                // 测试索引设置
                var indexSelect = DaisySelect.Create("索引0", "索引1", "索引2")
                    .SetLabel("索引测试")
                    .SetSelectedIndex(1);

                if (indexSelect.SelectedIndex != 1)
                {
                    throw new System.Exception("选择器索引设置失败");
                }

                // 测试选项的动态添加和移除
                var dynamicSelect = DaisySelect.Create("初始选项")
                    .SetLabel("动态选项")
                    .AddOption("添加选项1")
                    .AddOption("添加选项2");

                dynamicSelect.RemoveOption("初始选项");
                if (dynamicSelect.Options.Contains("初始选项"))
                {
                    throw new System.Exception("选项移除失败");
                }

                // 添加到UI中进行视觉验证
                var selectContainer = new VisualElement();
                selectContainer.AddToClassList("daisy-select-test");
                selectContainer.style.paddingTop = 20;
                selectContainer.style.paddingBottom = 20;
                selectContainer.style.paddingLeft = 20;
                selectContainer.style.paddingRight = 20;
                selectContainer.style.marginBottom = 20;
                selectContainer.style.backgroundColor = new Color(0.98f, 0.98f, 0.98f, 1f);
                selectContainer.style.borderTopLeftRadius = 8;
                selectContainer.style.borderTopRightRadius = 8;
                selectContainer.style.borderBottomLeftRadius = 8;
                selectContainer.style.borderBottomRightRadius = 8;

                var heading = new Label("DaisySelect 组件测试");
                heading.style.fontSize = 18;
                heading.style.unityFontStyleAndWeight = FontStyle.Bold;
                heading.style.marginBottom = 10;
                heading.style.color = Color.black;
                selectContainer.Add(heading);

                var description = new Label("测试选择器组件的选项管理、状态、修饰符和多选功能");
                description.style.fontSize = 12;
                description.style.marginBottom = 15;
                description.style.color = new Color(0.5f, 0.5f, 0.5f, 1f);
                selectContainer.Add(description);

                // 基础功能测试
                var basicSection = new Label("基础功能:");
                basicSection.style.fontSize = 14;
                basicSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                basicSection.style.marginBottom = 10;
                selectContainer.Add(basicSection);

                var basicGrid = DaisyBuilder.Grid(2);
                basicGrid.AddToClassList("daisy-gap-4");
                basicGrid.Add(basicSelect.SetLabel("基础选择器"));
                basicGrid.Add(labelSelect);
                basicGrid.Add(optionSelect);
                basicGrid.Add(valueSelect);
                selectContainer.Add(basicGrid);

                // 状态测试
                var stateSection = new Label("状态测试:");
                stateSection.style.fontSize = 14;
                stateSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                stateSection.style.marginBottom = 10;
                stateSection.style.marginTop = 15;
                selectContainer.Add(stateSection);

                var stateGrid = DaisyBuilder.Grid(2);
                stateGrid.AddToClassList("daisy-gap-4");
                stateGrid.Add(errorSelect);
                stateGrid.Add(successSelect);
                stateGrid.Add(warningSelect);
                stateGrid.Add(infoSelect);
                selectContainer.Add(stateGrid);

                // 修饰符和特殊功能
                var modifierSection = new Label("修饰符和特殊功能:");
                modifierSection.style.fontSize = 14;
                modifierSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                modifierSection.style.marginBottom = 10;
                modifierSection.style.marginTop = 15;
                selectContainer.Add(modifierSection);

                var modifierGrid = DaisyBuilder.Grid(2);
                modifierGrid.AddToClassList("daisy-gap-4");
                modifierGrid.Add(borderedSelect);
                modifierGrid.Add(ghostSelect);
                modifierGrid.Add(multiSelect);
                modifierGrid.Add(disabledSelect);
                modifierGrid.Add(indexSelect);
                modifierGrid.Add(dynamicSelect);
                selectContainer.Add(modifierGrid);

                // 尺寸测试
                var sizeSection = new Label("尺寸测试:");
                sizeSection.style.fontSize = 14;
                sizeSection.style.unityFontStyleAndWeight = FontStyle.Bold;
                sizeSection.style.marginBottom = 10;
                sizeSection.style.marginTop = 15;
                selectContainer.Add(sizeSection);

                var sizeContainer = new VisualElement();
                sizeContainer.AddToClassList("daisy-gap-2");
                sizeContainer.Add(extraSmallSelect.SetLabel("超小尺寸"));
                sizeContainer.Add(smallSelect.SetLabel("小尺寸"));
                sizeContainer.Add(DaisySelect.Create("默认1", "默认2").SetLabel("默认尺寸"));
                sizeContainer.Add(largeSelect.SetLabel("大尺寸"));
                selectContainer.Add(sizeContainer);

                root.Add(selectContainer);

                LogTestPass("DaisySelect 组件测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisySelect 组件测试失败: {ex.Message}");
            }
        }

        #endregion
    }
}
