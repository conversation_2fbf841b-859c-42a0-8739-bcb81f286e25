/* DaisyUI 主题变量系统 */

/* ========================================
   基础主题变量 (Light Theme)
   ======================================== */

:root {
    /* 主要颜色 */
    --primary: #3B82F6;
    --primary-content: #FFFFFF;
    --primary-focus: #2563EB;
    
    --secondary: #6B7280;
    --secondary-content: #FFFFFF;
    --secondary-focus: #4B5563;
    
    --accent: #06B6D4;
    --accent-content: #FFFFFF;
    --accent-focus: #0891B2;
    
    /* 中性色 */
    --neutral: #374151;
    --neutral-content: #FFFFFF;
    --neutral-focus: #1F2937;
    
    /* 基础色 */
    --base-100: #FFFFFF;
    --base-200: #F3F4F6;
    --base-300: #E5E7EB;
    --base-content: #1F2937;
    
    /* 状态颜色 */
    --info: #0EA5E9;
    --info-content: #FFFFFF;
    --success: #10B981;
    --success-content: #FFFFFF;
    --warning: #F59E0B;
    --warning-content: #FFFFFF;
    --error: #EF4444;
    --error-content: #FFFFFF;
    
    /* 组件变量 - 按钮 */
    --btn-padding-x: 16px;
    --btn-padding-y: 8px;
    --btn-font-size: 14px;
    --btn-font-weight: 500;
    --btn-line-height: 1.25;
    --btn-min-height: 40px;
    --btn-gap: 8px;
    
    /* 组件变量 - 输入框 */
    --input-padding-x: 12px;
    --input-padding-y: 8px;
    --input-font-size: 14px;
    --input-border-width: 1px;
    --input-border-color: var(--base-300);
    --input-min-height: 40px;
    
    /* 组件变量 - 卡片 */
    --card-padding: 24px;
    --card-border-radius: var(--border-radius);
    --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --card-background: var(--base-100);
    
    /* 组件变量 - 模态框 */
    --modal-backdrop: rgba(0, 0, 0, 0.5);
    --modal-border-radius: var(--border-radius-lg);
    --modal-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    
    /* 布局变量 */
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 9999px;
    
    --border-width: 1px;
    --border-width-2: 2px;
    --border-width-4: 4px;
    
    /* 间距变量 */
    --base-spacing: 4px;
    --spacing-0: 0px;
    --spacing-1: 4px;
    --spacing-2: 8px;
    --spacing-3: 12px;
    --spacing-4: 16px;
    --spacing-5: 20px;
    --spacing-6: 24px;
    --spacing-8: 32px;
    --spacing-10: 40px;
    --spacing-12: 48px;
    --spacing-16: 64px;
    --spacing-20: 80px;
    --spacing-24: 96px;
    --spacing-32: 128px;
    
    /* 字体变量 */
    --base-font-size: 14px;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-3xl: 30px;
    --font-size-4xl: 36px;
    --font-size-5xl: 48px;
    --font-size-6xl: 60px;
    
    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    
    --line-height-none: 1;
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;
    
    /* 阴影变量 */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    
    /* 过渡变量 */
    --transition-duration: 0.2s;
    --transition-duration-fast: 0.1s;
    --transition-duration-slow: 0.3s;
    --transition-easing: ease-in-out;
    --transition-easing-in: ease-in;
    --transition-easing-out: ease-out;
    
    /* Z-index变量 */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;

    /* 浅色主题专用变量 (用于组件内部) */
    --daisy-dark-base-100: var(--base-100);
    --daisy-dark-base-200: var(--base-200);
    --daisy-dark-base-300: var(--base-300);
    --daisy-dark-base-400: #D1D5DB; /* 比base-300更深一级 */
    --daisy-dark-content: var(--base-content);
    --daisy-dark-hover: rgba(209, 213, 219, 0.5); /* 半透明的hover效果 */
}

/* ========================================
   暗色主题变量 (Dark Theme)
   ======================================== */

.theme-dark {
    /* 主要颜色 (保持品牌一致性) */
    --primary: #3B82F6;
    --primary-content: #FFFFFF;
    --primary-focus: #2563EB;
    
    --secondary: #7986A0;
    --secondary-content: #FFFFFF;
    --secondary-focus: #5D6880;
    
    --accent: #1ABB9C;
    --accent-content: #FFFFFF;
    --accent-focus: #0FA681;
    
    /* 中性色 (专业深色) */
    --neutral: #374151;
    --neutral-content: #F1F5F9;
    --neutral-focus: #1E2530;
    
    /* 基础色 (深色背景系统) */
    --base-100: #0F172A;
    --base-200: #1E293B;
    --base-300: #334155;
    --base-content: #F8FAFC;
    
    /* 状态颜色 (保持不变) */
    --info: #0EA5E9;
    --info-content: #FFFFFF;
    --success: #10B981;
    --success-content: #FFFFFF;
    --warning: #F59E0B;
    --warning-content: #FFFFFF;
    --error: #EF4444;
    --error-content: #FFFFFF;
    
    /* 组件变量调整 (深色优化) */
    --input-border-color: var(--base-300);
    --card-background: var(--base-100);
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    --modal-backdrop: rgba(0, 0, 0, 0.8);
    --modal-shadow: 0 25px 50px rgba(0, 0, 0, 0.6);
    
    /* 阴影变量调整 (深色增强) */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.4);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.3);

    /* 深色主题专用变量 (用于组件内部) */
    --daisy-dark-base-100: #1f1f1f;
    --daisy-dark-base-200: #2a2a2a;
    --daisy-dark-base-300: #404040;
    --daisy-dark-base-400: #505050;
    --daisy-dark-content: var(--base-content);
    --daisy-dark-hover: rgba(71, 85, 105, 0.5); /* 半透明的hover效果 */
}



/* ========================================
   自定义主题支持
   ======================================== */

/* Cupcake 主题 (粉色系) */
.theme-cupcake {
    --primary: #E879F9;
    --primary-content: #FFFFFF;
    --primary-focus: #D946EF;
    
    --secondary: #F472B6;
    --secondary-content: #FFFFFF;
    --secondary-focus: #EC4899;
    
    --accent: #FBBF24;
    --accent-content: #000000;
    --accent-focus: #F59E0B;
    
    --base-100: #FDF2F8;
    --base-200: #FCE7F3;
    --base-300: #FBCFE8;
    --base-content: #881337;
}

/* Synthwave 主题 (霓虹色系) */
.theme-synthwave {
    --primary: #FF1493;
    --primary-content: #FFFFFF;
    --primary-focus: #E60E86;
    
    --secondary: #00FFFF;
    --secondary-content: #000000;
    --secondary-focus: #00E6E6;
    
    --accent: #FFFF00;
    --accent-content: #000000;
    --accent-focus: #E6E600;
    
    --base-100: #1A0933;
    --base-200: #2D1B69;
    --base-300: #402D80;
    --base-content: #FFFFFF;
    
    /* 特殊效果 */
    --card-shadow: 0 0 20px rgba(255, 20, 147, 0.3);
    --btn-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

/* Forest 主题 (绿色系) */
.theme-forest {
    --primary: #22C55E;
    --primary-content: #FFFFFF;
    --primary-focus: #16A34A;
    
    --secondary: #84CC16;
    --secondary-content: #FFFFFF;
    --secondary-focus: #65A30D;
    
    --accent: #F59E0B;
    --accent-content: #FFFFFF;
    --accent-focus: #D97706;
    
    --base-100: #F0FDF4;
    --base-200: #DCFCE7;
    --base-300: #BBF7D0;
    --base-content: #14532D;
}

/* ========================================
   主题过渡动画
   ======================================== */

/* 主题切换过渡效果 */


/* 禁用某些元素的过渡 */
.no-transition,
.no-transition * {
    transition: none !important;
}

/* ========================================
   响应式断点变量
   ======================================== */

:root {
    /* 断点变量 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    
    /* 容器最大宽度 */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
}



/* ========================================
   无障碍支持
   ======================================== */



/* 高对比度支持 */
@media (prefers-contrast: high) {
    :root {
        --border-width: 2px;
        --input-border-width: 2px;
    }
    
    .theme-dark {
        --base-content: #FFFFFF;
        --primary: #4F93FF;
        --secondary: #8B9DC3;
        --accent: #1FB3D3;
    }
}

/* 暗色偏好支持 */
@media (prefers-color-scheme: dark) {
    :root:not(.theme-light):not(.theme-cupcake):not(.theme-forest) {
        --base-100: #0F172A;
        --base-200: #1E293B;
        --base-300: #334155;
        --base-content: #F8FAFC;
        --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
        --modal-backdrop: rgba(0, 0, 0, 0.8);
        --secondary: #7986A0;
        --accent: #1ABB9C;
    }
}

