using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;
using BlastingDesign.UI.DaisyUI.Builders;
using System.Collections.Generic;

namespace BlastingDesign.UI.DaisyUI.Examples
{
    /// <summary>
    /// DaisyTree组件使用示例
    /// 展示如何使用DaisyTree组件创建树状菜单
    /// </summary>
    public class DaisyTreeExample : MonoBehaviour
    {
        [Header("UI配置")]
        public UIDocument uiDocument;

        [Header("主题配置")]
        public DaisyTheme theme;

        private VisualElement root;

        void Start()
        {
            SetupUI();
            CreateBasicTreeExample();
            CreateDataBoundTreeExample();
            CreateBuilderTreeExample();
        }

        private void SetupUI()
        {
            if (uiDocument == null)
            {
                Debug.LogError("UIDocument未设置");
                return;
            }

            root = uiDocument.rootVisualElement;
            root.Clear();

            // 应用主题
            if (theme != null)
            {
                theme.Apply(root);
            }

            // 设置根容器样式
            root.style.paddingTop = 20;
            root.style.paddingBottom = 20;
            root.style.paddingLeft = 20;
            root.style.paddingRight = 20;
            root.style.backgroundColor = new Color(0.1f, 0.1f, 0.1f, 1f);

            // 添加标题
            var title = new Label("DaisyTree 组件示例");
            title.style.fontSize = 24;
            title.style.color = Color.white;
            title.style.marginBottom = 20;
            root.Add(title);
        }

        private void CreateBasicTreeExample()
        {
            var sectionTitle = new Label("基础树组件示例");
            sectionTitle.style.fontSize = 18;
            sectionTitle.style.color = Color.white;
            sectionTitle.style.marginBottom = 10;
            root.Add(sectionTitle);

            // 创建基础树组件
            var tree = new DaisyTree("basic-tree");
            tree.AddClass("dark");
            tree.SetAllowSearch(true);
            tree.SetSearchPlaceholder("搜索图层...");
            tree.SetShowActions(true);

            // 创建树数据
            var treeData = new List<DaisyTreeData>();

            // 添加城区节点
            var region1 = new DaisyTreeData("region1", "城区1", "🏙️");
            var region2 = new DaisyTreeData("region2", "城区2", "🏘️");

            // 添加测量点系统（多级结构）
            var measureSystem = region1.AddChild("measure_system", "测量点系统", "📍");
            
            // 一级测量点
            var primaryMeasure = measureSystem.AddChild("primary_measure", "一级测量点", "📐");
            var pm1 = primaryMeasure.AddChild("pm1", "PM-001", "📍");
            pm1.AddAction("visibility", "显示/隐藏", "👁️");
            pm1.AddAction("edit", "编辑", "✏️");
            pm1.AddAction("calibrate", "校准", "⚙️");
            
            var pm2 = primaryMeasure.AddChild("pm2", "PM-002", "📍");
            pm2.AddAction("visibility", "显示/隐藏", "👁️");
            pm2.AddAction("edit", "编辑", "✏️");
            pm2.AddAction("calibrate", "校准", "⚙️");

            // 二级测量点
            var secondaryMeasure = measureSystem.AddChild("secondary_measure", "二级测量点", "📏");
            var sm1 = secondaryMeasure.AddChild("sm1", "SM-001", "📍");
            sm1.AddAction("visibility", "显示/隐藏", "👁️");
            sm1.AddAction("edit", "编辑", "✏️");
            
            var sm2 = secondaryMeasure.AddChild("sm2", "SM-002", "📍");
            sm2.AddAction("visibility", "显示/隐藏", "👁️");
            sm2.AddAction("edit", "编辑", "✏️");
            
            // 三级测量点（详细测量）
            var detailedMeasure = secondaryMeasure.AddChild("detailed_measure", "详细测量", "🔍");
            var dm1 = detailedMeasure.AddChild("dm1", "DM-001", "📍");
            dm1.AddAction("visibility", "显示/隐藏", "👁️");
            dm1.AddAction("edit", "编辑", "✏️");
            dm1.AddAction("export", "导出数据", "📊");

            // 边缘点系统
            var edgeSystem = region1.AddChild("edge_system", "边缘点系统", "🔵");
            var mainEdge = edgeSystem.AddChild("main_edge", "主边缘", "🔵");
            var edge1 = mainEdge.AddChild("edge1", "边缘点-1", "🔵");
            edge1.AddAction("visibility", "显示/隐藏", "👁️");
            edge1.AddAction("edit", "编辑", "✏️");
            
            var subEdge = mainEdge.AddChild("sub_edge", "子边缘", "🔹");
            var subEdge1 = subEdge.AddChild("sub_edge1", "子边缘-1", "🔹");
            subEdge1.AddAction("visibility", "显示/隐藏", "👁️");
            subEdge1.AddAction("edit", "编辑", "✏️");
            
            var subEdge2 = subEdge.AddChild("sub_edge2", "子边缘-2", "🔹");
            subEdge2.AddAction("visibility", "显示/隐藏", "👁️");
            subEdge2.AddAction("edit", "编辑", "✏️");

            // 边界线系统
            var boundarySystem = region1.AddChild("boundary_system", "边界线系统", "📏");
            var outerBoundary = boundarySystem.AddChild("outer_boundary", "外边界", "📏");
            var boundary1 = outerBoundary.AddChild("boundary1", "边界线-1", "📏");
            boundary1.AddAction("visibility", "显示/隐藏", "👁️");
            boundary1.AddAction("edit", "编辑", "✏️");
            
            var innerBoundary = boundarySystem.AddChild("inner_boundary", "内边界", "📐");
            var innerB1 = innerBoundary.AddChild("inner_b1", "内边界-1", "📐");
            innerB1.AddAction("visibility", "显示/隐藏", "👁️");
            innerB1.AddAction("edit", "编辑", "✏️");
            
            var innerB2 = innerBoundary.AddChild("inner_b2", "内边界-2", "📐");
            innerB2.AddAction("visibility", "显示/隐藏", "👁️");
            innerB2.AddAction("edit", "编辑", "✏️");

            // 钻孔系统
            var drillSystem = region1.AddChild("drill_system", "钻孔系统", "🕳️");
            var explorationDrill = drillSystem.AddChild("exploration_drill", "勘探钻孔", "🕳️");
            var drill1 = explorationDrill.AddChild("drill1", "钻孔-1", "🕳️");
            drill1.AddAction("visibility", "显示/隐藏", "👁️");
            drill1.AddAction("edit", "编辑", "✏️");
            drill1.AddAction("core_sample", "取芯样", "🧪");
            
            var productionDrill = drillSystem.AddChild("production_drill", "生产钻孔", "⚡");
            var pDrill1 = productionDrill.AddChild("p_drill1", "生产钻孔-1", "⚡");
            pDrill1.AddAction("visibility", "显示/隐藏", "👁️");
            pDrill1.AddAction("edit", "编辑", "✏️");
            pDrill1.AddAction("blast_plan", "爆破计划", "💥");

            // 添加到region2 - 更复杂的控制系统
            var controlSystem = region2.AddChild("control_system", "控制系统", "🎯");
            var primaryControl = controlSystem.AddChild("primary_control", "主控制点", "🎯");
            var control1 = primaryControl.AddChild("control1", "控制点-1", "🎯");
            control1.AddAction("visibility", "显示/隐藏", "👁️");
            control1.AddAction("edit", "编辑", "✏️");
            control1.AddAction("sync", "同步", "🔄");
            
            var secondaryControl = controlSystem.AddChild("secondary_control", "辅助控制点", "🎲");
            var control2 = secondaryControl.AddChild("control2", "控制点-2", "🎲");
            control2.AddAction("visibility", "显示/隐藏", "👁️");
            control2.AddAction("edit", "编辑", "✏️");
            
            var emergencyControl = secondaryControl.AddChild("emergency_control", "应急控制", "🚨");
            var emergency1 = emergencyControl.AddChild("emergency1", "应急点-1", "🚨");
            emergency1.AddAction("visibility", "显示/隐藏", "👁️");
            emergency1.AddAction("edit", "编辑", "✏️");
            emergency1.AddAction("alert", "警报", "🔔");

            // 参考点系统
            var referenceSystem = region2.AddChild("reference_system", "参考点系统", "📌");
            var gpsReference = referenceSystem.AddChild("gps_reference", "GPS参考", "🛰️");
            var reference1 = gpsReference.AddChild("reference1", "GPS-001", "📌");
            reference1.AddAction("visibility", "显示/隐藏", "👁️");
            reference1.AddAction("edit", "编辑", "✏️");
            reference1.AddAction("calibrate", "校准", "⚙️");
            
            var localReference = referenceSystem.AddChild("local_reference", "本地参考", "📍");
            var localRef1 = localReference.AddChild("local_ref1", "本地-001", "📍");
            localRef1.AddAction("visibility", "显示/隐藏", "👁️");
            localRef1.AddAction("edit", "编辑", "✏️");
            
            var benchmarkRef = localReference.AddChild("benchmark_ref", "基准参考", "⚖️");
            var benchmark1 = benchmarkRef.AddChild("benchmark1", "基准-001", "⚖️");
            benchmark1.AddAction("visibility", "显示/隐藏", "👁️");
            benchmark1.AddAction("edit", "编辑", "✏️");
            benchmark1.AddAction("validate", "验证", "✅");

            // 添加到数据列表
            treeData.AddRange(new[] { region1, region2 });

            // 设置数据到树组件
            tree.SetData(treeData);

            // 设置事件处理
            tree.OnItemClick((data) =>
            {
                Debug.Log($"节点被点击: {data.Id}");
            });

            tree.OnItemSelect((data) =>
            {
                Debug.Log($"项目被选中: {data.Id}");
            });

            tree.OnAction((data, actionId) =>
            {
                Debug.Log($"操作被触发: {data.Id} -> {actionId}");
                if (actionId == "visibility")
                {
                    Debug.Log($"切换 {data.Id} 的可见性");
                }
                else if (actionId == "edit")
                {
                    Debug.Log($"编辑 {data.Id}");
                }
            });

            tree.OnSearch((query) =>
            {
                Debug.Log($"搜索: {query}");
            });

            // 默认展开第一个节点和测量点系统
            tree.ExpandItem("region1");
            tree.ExpandItem("measure_system");
            tree.ExpandItem("primary_measure");

            // 默认选中边界线
            tree.SelectItem("boundary1");

            // 设置树的样式
            tree.style.height = 300;
            tree.style.marginBottom = 20;

            root.Add(tree);
        }

        private void CreateDataBoundTreeExample()
        {
            var sectionTitle = new Label("数据绑定树组件示例");
            sectionTitle.style.fontSize = 18;
            sectionTitle.style.color = Color.white;
            sectionTitle.style.marginBottom = 10;
            root.Add(sectionTitle);

            // 创建数据结构
            var treeData = new List<DaisyTreeData>();

            // 根文件夹
            var rootData = new DaisyTreeData("root", "项目结构", "📁");

            // 源代码文件夹 - 多级结构
            var srcFolder = rootData.AddChild("src", "源代码", "📂");
            
            // 核心模块
            var coreFolder = srcFolder.AddChild("core", "核心模块", "📂");
            var engineFolder = coreFolder.AddChild("engine", "引擎", "📂");
            var mainFile = engineFolder.AddChild("main.cs", "主程序", "📄");
            mainFile.AddAction("open", "打开", "📂");
            mainFile.AddAction("debug", "调试", "🐛");
            mainFile.AddAction("delete", "删除", "🗑️");
            
            var renderFile = engineFolder.AddChild("renderer.cs", "渲染器", "📄");
            renderFile.AddAction("open", "打开", "📂");
            renderFile.AddAction("profile", "性能分析", "⚡");
            renderFile.AddAction("delete", "删除", "🗑️");
            
            var systemFolder = coreFolder.AddChild("system", "系统", "📂");
            var inputFile = systemFolder.AddChild("input.cs", "输入系统", "📄");
            inputFile.AddAction("open", "打开", "📂");
            inputFile.AddAction("configure", "配置", "⚙️");
            inputFile.AddAction("delete", "删除", "🗑️");
            
            var physicsFile = systemFolder.AddChild("physics.cs", "物理系统", "📄");
            physicsFile.AddAction("open", "打开", "📂");
            physicsFile.AddAction("test", "测试", "🧪");
            physicsFile.AddAction("delete", "删除", "🗑️");

            // 工具模块
            var utilsFolder = srcFolder.AddChild("utils", "工具模块", "📂");
            var helpersFolder = utilsFolder.AddChild("helpers", "辅助工具", "📂");
            var mathFile = helpersFolder.AddChild("math.cs", "数学工具", "📄");
            mathFile.AddAction("open", "打开", "📂");
            mathFile.AddAction("test", "测试", "🧪");
            mathFile.AddAction("delete", "删除", "🗑️");
            
            var stringFile = helpersFolder.AddChild("string.cs", "字符串工具", "📄");
            stringFile.AddAction("open", "打开", "📂");
            stringFile.AddAction("delete", "删除", "🗑️");
            
            var extensionsFolder = utilsFolder.AddChild("extensions", "扩展方法", "📂");
            var collectionExt = extensionsFolder.AddChild("collection.cs", "集合扩展", "📄");
            collectionExt.AddAction("open", "打开", "📂");
            collectionExt.AddAction("delete", "删除", "🗑️");

            // 配置模块
            var configFolder = srcFolder.AddChild("config", "配置模块", "📂");
            var settingsFolder = configFolder.AddChild("settings", "设置", "📂");
            var gameSettings = settingsFolder.AddChild("game.json", "游戏设置", "⚙️");
            gameSettings.AddAction("open", "打开", "📂");
            gameSettings.AddAction("validate", "验证", "✅");
            gameSettings.AddAction("delete", "删除", "🗑️");
            
            var graphicsSettings = settingsFolder.AddChild("graphics.json", "图形设置", "🎨");
            graphicsSettings.AddAction("open", "打开", "📂");
            graphicsSettings.AddAction("reset", "重置", "🔄");
            graphicsSettings.AddAction("delete", "删除", "🗑️");

            // 资源文件夹 - 多级结构
            var assetsFolder = rootData.AddChild("assets", "资源文件", "📂");
            
            // 贴图资源
            var texturesFolder = assetsFolder.AddChild("textures", "贴图", "📂");
            var uiTextures = texturesFolder.AddChild("ui", "UI贴图", "📂");
            var iconTexture = uiTextures.AddChild("icons.png", "图标集", "🖼️");
            iconTexture.AddAction("open", "打开", "📂");
            iconTexture.AddAction("optimize", "优化", "⚡");
            iconTexture.AddAction("delete", "删除", "🗑️");
            
            var buttonTexture = uiTextures.AddChild("buttons.png", "按钮贴图", "🖼️");
            buttonTexture.AddAction("open", "打开", "📂");
            buttonTexture.AddAction("slice", "切片", "✂️");
            buttonTexture.AddAction("delete", "删除", "🗑️");
            
            var gameTextures = texturesFolder.AddChild("game", "游戏贴图", "📂");
            var characterTexture = gameTextures.AddChild("character.png", "角色贴图", "🖼️");
            characterTexture.AddAction("open", "打开", "📂");
            characterTexture.AddAction("edit", "编辑", "✏️");
            characterTexture.AddAction("delete", "删除", "🗑️");
            
            var environmentTexture = gameTextures.AddChild("environment.png", "环境贴图", "🖼️");
            environmentTexture.AddAction("open", "打开", "📂");
            environmentTexture.AddAction("compress", "压缩", "📦");
            environmentTexture.AddAction("delete", "删除", "🗑️");

            // 模型资源
            var modelsFolder = assetsFolder.AddChild("models", "模型", "📂");
            var characterModels = modelsFolder.AddChild("characters", "角色模型", "📂");
            var heroModel = characterModels.AddChild("hero.fbx", "英雄模型", "🎭");
            heroModel.AddAction("open", "打开", "📂");
            heroModel.AddAction("rig", "骨骼绑定", "🦴");
            heroModel.AddAction("delete", "删除", "🗑️");
            
            var enemyModel = characterModels.AddChild("enemy.fbx", "敌人模型", "👹");
            enemyModel.AddAction("open", "打开", "📂");
            enemyModel.AddAction("animate", "动画", "🎬");
            enemyModel.AddAction("delete", "删除", "🗑️");
            
            var propModels = modelsFolder.AddChild("props", "道具模型", "📂");
            var weaponModel = propModels.AddChild("weapon.fbx", "武器模型", "⚔️");
            weaponModel.AddAction("open", "打开", "📂");
            weaponModel.AddAction("optimize", "优化", "⚡");
            weaponModel.AddAction("delete", "删除", "🗑️");

            // 音效资源
            var soundsFolder = assetsFolder.AddChild("sounds", "音效", "📂");
            var musicFolder = soundsFolder.AddChild("music", "音乐", "📂");
            var bgMusic = musicFolder.AddChild("background.mp3", "背景音乐", "🎵");
            bgMusic.AddAction("open", "打开", "📂");
            bgMusic.AddAction("loop", "循环设置", "🔄");
            bgMusic.AddAction("delete", "删除", "🗑️");
            
            var sfxFolder = soundsFolder.AddChild("sfx", "音效", "📂");
            var clickSound = sfxFolder.AddChild("click.wav", "点击音效", "🔊");
            clickSound.AddAction("open", "打开", "📂");
            clickSound.AddAction("normalize", "标准化", "📊");
            clickSound.AddAction("delete", "删除", "🗑️");
            
            var explosionSound = sfxFolder.AddChild("explosion.wav", "爆炸音效", "💥");
            explosionSound.AddAction("open", "打开", "📂");
            explosionSound.AddAction("enhance", "增强", "🔊");
            explosionSound.AddAction("delete", "删除", "🗑️");

            // 文档文件夹 - 多级结构
            var docsFolder = rootData.AddChild("docs", "文档", "📂");
            
            // 开发文档
            var devDocs = docsFolder.AddChild("development", "开发文档", "📂");
            var readmeFile = devDocs.AddChild("readme.md", "项目说明", "📝");
            readmeFile.AddAction("open", "打开", "📂");
            readmeFile.AddAction("edit", "编辑", "✏️");
            readmeFile.AddAction("delete", "删除", "🗑️");
            
            var setupFile = devDocs.AddChild("setup.md", "环境配置", "📝");
            setupFile.AddAction("open", "打开", "📂");
            setupFile.AddAction("update", "更新", "🔄");
            setupFile.AddAction("delete", "删除", "🗑️");
            
            var architectureFile = devDocs.AddChild("architecture.md", "架构文档", "📝");
            architectureFile.AddAction("open", "打开", "📂");
            architectureFile.AddAction("diagram", "生成图表", "📊");
            architectureFile.AddAction("delete", "删除", "🗑️");
            
            // API文档
            var apiDocs = docsFolder.AddChild("api", "API文档", "📂");
            var apiFile = apiDocs.AddChild("api.md", "API参考", "📋");
            apiFile.AddAction("open", "打开", "📂");
            apiFile.AddAction("generate", "生成", "🔄");
            apiFile.AddAction("delete", "删除", "🗑️");
            
            var examplesFile = apiDocs.AddChild("examples.md", "使用示例", "📋");
            examplesFile.AddAction("open", "打开", "📂");
            examplesFile.AddAction("test", "测试", "🧪");
            examplesFile.AddAction("delete", "删除", "🗑️");
            
            // 用户文档
            var userDocs = docsFolder.AddChild("user", "用户文档", "📂");
            var manualFile = userDocs.AddChild("manual.md", "用户手册", "📖");
            manualFile.AddAction("open", "打开", "📂");
            manualFile.AddAction("translate", "翻译", "🌐");
            manualFile.AddAction("delete", "删除", "🗑️");
            
            var tutorialFile = userDocs.AddChild("tutorial.md", "教程", "📖");
            tutorialFile.AddAction("open", "打开", "📂");
            tutorialFile.AddAction("video", "录制视频", "🎥");
            tutorialFile.AddAction("delete", "删除", "🗑️");

            // 添加到数据列表
            treeData.Add(rootData);

            // 创建基于数据的树组件
            var dataTree = new DaisyTree("data-tree");
            dataTree.AddClass("dark");
            dataTree.SetAllowSearch(true);
            dataTree.SetSearchPlaceholder("搜索文件...");
            dataTree.SetShowLines(true);
            dataTree.SetShowIcons(true);
            dataTree.SetShowActions(true);

            // 设置数据
            dataTree.SetData(treeData);

            // 展开根节点和主要文件夹
            dataTree.ExpandItem("root");
            dataTree.ExpandItem("src");
            dataTree.ExpandItem("core");
            dataTree.ExpandItem("assets");

            // 事件处理
            dataTree.OnAction((data, actionId) =>
            {
                Debug.Log($"文件操作: {data.Id} -> {actionId}");
            });

            dataTree.style.height = 300;
            dataTree.style.marginBottom = 20;

            root.Add(dataTree);
        }

        private void CreateBuilderTreeExample()
        {
            var sectionTitle = new Label("构建器树组件示例");
            sectionTitle.style.fontSize = 18;
            sectionTitle.style.color = Color.white;
            sectionTitle.style.marginBottom = 10;
            root.Add(sectionTitle);

            // 使用构建器创建不同类型的树
            var container = DaisyBuilder.Row();

            // 文件树
            var fileTree = DaisyBuilder.FileTree("file-browser")
                .OnItemClick((data) => Debug.Log($"File clicked: {data.Id}"))
                .OnAction((data, actionId) => Debug.Log($"File action: {data.Id} -> {actionId}"));

            var fileTreeData = new List<DaisyTreeData>();
            
            // 项目根目录
            var projectRoot = new DaisyTreeData("project_root", "项目根目录", "📁");
            
            // 源代码目录
            var srcDir = projectRoot.AddChild("src_dir", "源代码", "📂");
            var componentsDir = srcDir.AddChild("components", "组件", "📂");
            var uiComponents = componentsDir.AddChild("ui", "UI组件", "📂");
            var button = uiComponents.AddChild("button.tsx", "按钮组件", "📄");
            button.AddAction("open", "打开", "📂");
            button.AddAction("edit", "编辑", "✏️");
            button.AddAction("test", "测试", "🧪");
            
            var modal = uiComponents.AddChild("modal.tsx", "模态框组件", "📄");
            modal.AddAction("open", "打开", "📂");
            modal.AddAction("edit", "编辑", "✏️");
            modal.AddAction("preview", "预览", "👁️");
            
            var gameComponents = componentsDir.AddChild("game", "游戏组件", "📂");
            var player = gameComponents.AddChild("player.tsx", "玩家组件", "📄");
            player.AddAction("open", "打开", "📂");
            player.AddAction("debug", "调试", "🐛");
            player.AddAction("profile", "性能分析", "⚡");
            
            // 工具目录
            var utilsDir = srcDir.AddChild("utils", "工具", "📂");
            var helpers = utilsDir.AddChild("helpers.ts", "辅助函数", "📄");
            helpers.AddAction("open", "打开", "📂");
            helpers.AddAction("edit", "编辑", "✏️");
            
            // 配置目录
            var configDir = projectRoot.AddChild("config", "配置", "📂");
            var webpack = configDir.AddChild("webpack.config.js", "Webpack配置", "⚙️");
            webpack.AddAction("open", "打开", "📂");
            webpack.AddAction("optimize", "优化", "⚡");
            webpack.AddAction("validate", "验证", "✅");
            
            fileTreeData.Add(projectRoot);
            fileTree.SetData(fileTreeData);

            fileTree.style.width = Length.Percent(30);
            fileTree.style.height = 200;
            fileTree.style.marginRight = 10;

            // 项目树
            var projectTree = DaisyBuilder.ProjectTree("project-structure")
                .OnItemSelect((data) => Debug.Log($"Project item selected: {data.Id}"));

            var projectTreeData = new List<DaisyTreeData>();
            
            // 主项目
            var mainProject = new DaisyTreeData("main_project", "主项目", "🏗️");
            
            // 前端模块
            var frontendModule = mainProject.AddChild("frontend", "前端模块", "🎨");
            var uiFramework = frontendModule.AddChild("ui_framework", "UI框架", "🎨");
            var components = uiFramework.AddChild("components", "组件库", "📦");
            var buttonComponent = components.AddChild("button", "按钮组件", "📋");
            buttonComponent.AddAction("develop", "开发", "💻");
            buttonComponent.AddAction("test", "测试", "🧪");
            buttonComponent.AddAction("deploy", "部署", "🚀");
            
            var forms = components.AddChild("forms", "表单组件", "📋");
            forms.AddAction("design", "设计", "🎨");
            forms.AddAction("implement", "实现", "💻");
            forms.AddAction("validate", "验证", "✅");
            
            var routing = frontendModule.AddChild("routing", "路由系统", "🛣️");
            var navigation = routing.AddChild("navigation", "导航", "🧭");
            navigation.AddAction("configure", "配置", "⚙️");
            navigation.AddAction("optimize", "优化", "⚡");
            
            // 后端模块
            var backendModule = mainProject.AddChild("backend", "后端模块", "🖥️");
            var apiLayer = backendModule.AddChild("api", "API层", "🔗");
            var restApi = apiLayer.AddChild("rest_api", "REST API", "🔗");
            var userEndpoints = restApi.AddChild("user_endpoints", "用户接口", "👤");
            userEndpoints.AddAction("implement", "实现", "💻");
            userEndpoints.AddAction("document", "文档", "📝");
            userEndpoints.AddAction("test", "测试", "🧪");
            
            var authEndpoints = restApi.AddChild("auth_endpoints", "认证接口", "🔐");
            authEndpoints.AddAction("secure", "加密", "🔒");
            authEndpoints.AddAction("test", "测试", "🧪");
            
            var database = backendModule.AddChild("database", "数据库", "💾");
            var migrations = database.AddChild("migrations", "迁移", "📋");
            migrations.AddAction("create", "创建", "➕");
            migrations.AddAction("run", "运行", "▶️");
            migrations.AddAction("rollback", "回滚", "⏪");
            
            // 测试模块
            var testingModule = mainProject.AddChild("testing", "测试模块", "🧪");
            var unitTests = testingModule.AddChild("unit_tests", "单元测试", "🧪");
            var frontendTests = unitTests.AddChild("frontend_tests", "前端测试", "🧪");
            frontendTests.AddAction("write", "编写", "✏️");
            frontendTests.AddAction("run", "运行", "▶️");
            frontendTests.AddAction("coverage", "覆盖率", "📊");
            
            var backendTests = unitTests.AddChild("backend_tests", "后端测试", "🧪");
            backendTests.AddAction("write", "编写", "✏️");
            backendTests.AddAction("run", "运行", "▶️");
            backendTests.AddAction("mock", "模拟", "🎭");
            
            var e2eTests = testingModule.AddChild("e2e_tests", "端到端测试", "🔄");
            e2eTests.AddAction("setup", "设置", "⚙️");
            e2eTests.AddAction("run", "运行", "▶️");
            e2eTests.AddAction("report", "报告", "📊");
            
            projectTreeData.Add(mainProject);
            projectTree.SetData(projectTreeData);

            projectTree.style.width = Length.Percent(30);
            projectTree.style.height = 200;
            projectTree.style.marginRight = 10;

            // 设置树
            var settingsTree = DaisyBuilder.SettingsTree("settings")
                .OnItemSelect((data) => Debug.Log($"Setting selected: {data.Id}"));

            var settingsTreeData = new List<DaisyTreeData>();
            
            // 用户设置
            var userSettings = new DaisyTreeData("user_settings", "用户设置", "👤");
            var profileSettings = userSettings.AddChild("profile", "个人资料", "👤");
            var personalInfo = profileSettings.AddChild("personal_info", "个人信息", "📝");
            personalInfo.AddAction("edit", "编辑", "✏️");
            personalInfo.AddAction("save", "保存", "💾");
            
            var avatarSettings = profileSettings.AddChild("avatar", "头像设置", "🖼️");
            avatarSettings.AddAction("upload", "上传", "📤");
            avatarSettings.AddAction("crop", "裁剪", "✂️");
            avatarSettings.AddAction("remove", "移除", "🗑️");
            
            var privacySettings = userSettings.AddChild("privacy", "隐私设置", "🔒");
            var visibility = privacySettings.AddChild("visibility", "可见性", "👁️");
            visibility.AddAction("configure", "配置", "⚙️");
            visibility.AddAction("test", "测试", "🧪");
            
            var dataSharing = privacySettings.AddChild("data_sharing", "数据共享", "🔄");
            dataSharing.AddAction("manage", "管理", "🛠️");
            dataSharing.AddAction("export", "导出", "📤");
            
            // 应用设置
            var appSettings = new DaisyTreeData("app_settings", "应用设置", "⚙️");
            var appearanceSettings = appSettings.AddChild("appearance", "外观", "🎨");
            var themeSettings = appearanceSettings.AddChild("theme", "主题", "🎨");
            var lightTheme = themeSettings.AddChild("light_theme", "浅色主题", "☀️");
            lightTheme.AddAction("apply", "应用", "✅");
            lightTheme.AddAction("customize", "自定义", "🎨");
            
            var darkTheme = themeSettings.AddChild("dark_theme", "深色主题", "🌙");
            darkTheme.AddAction("apply", "应用", "✅");
            darkTheme.AddAction("customize", "自定义", "🎨");
            
            var fontSettings = appearanceSettings.AddChild("font", "字体", "🔤");
            var fontSize = fontSettings.AddChild("font_size", "字体大小", "📏");
            fontSize.AddAction("small", "小", "📏");
            fontSize.AddAction("medium", "中", "📏");
            fontSize.AddAction("large", "大", "📏");
            
            var languageSettings = appSettings.AddChild("language", "语言", "🌐");
            var interfaceLanguage = languageSettings.AddChild("interface", "界面语言", "🌐");
            interfaceLanguage.AddAction("chinese", "中文", "🇨🇳");
            interfaceLanguage.AddAction("english", "English", "🇺🇸");
            interfaceLanguage.AddAction("japanese", "日本語", "🇯🇵");
            
            var contentLanguage = languageSettings.AddChild("content", "内容语言", "📝");
            contentLanguage.AddAction("auto", "自动", "🔄");
            contentLanguage.AddAction("manual", "手动", "✋");
            
            // 通知设置
            var notificationSettings = appSettings.AddChild("notifications", "通知", "🔔");
            var pushNotifications = notificationSettings.AddChild("push", "推送通知", "📱");
            var systemNotifications = pushNotifications.AddChild("system", "系统通知", "🔔");
            systemNotifications.AddAction("enable", "启用", "✅");
            systemNotifications.AddAction("disable", "禁用", "❌");
            systemNotifications.AddAction("schedule", "定时", "⏰");
            
            var appNotifications = pushNotifications.AddChild("app", "应用通知", "📱");
            appNotifications.AddAction("configure", "配置", "⚙️");
            appNotifications.AddAction("test", "测试", "🧪");
            
            var emailNotifications = notificationSettings.AddChild("email", "邮件通知", "📧");
            var dailyDigest = emailNotifications.AddChild("daily", "每日摘要", "📧");
            dailyDigest.AddAction("enable", "启用", "✅");
            dailyDigest.AddAction("schedule", "定时", "⏰");
            
            var weeklyReport = emailNotifications.AddChild("weekly", "周报", "📧");
            weeklyReport.AddAction("enable", "启用", "✅");
            weeklyReport.AddAction("customize", "自定义", "🎨");
            
            // 系统设置
            var systemSettings = new DaisyTreeData("system_settings", "系统设置", "🔧");
            var performanceSettings = systemSettings.AddChild("performance", "性能", "⚡");
            var memorySettings = performanceSettings.AddChild("memory", "内存管理", "🧠");
            var cacheSettings = memorySettings.AddChild("cache", "缓存", "💾");
            cacheSettings.AddAction("clear", "清除", "🗑️");
            cacheSettings.AddAction("optimize", "优化", "⚡");
            cacheSettings.AddAction("configure", "配置", "⚙️");
            
            var storageSettings = memorySettings.AddChild("storage", "存储", "💾");
            storageSettings.AddAction("cleanup", "清理", "🧹");
            storageSettings.AddAction("analyze", "分析", "📊");
            
            var networkSettings = performanceSettings.AddChild("network", "网络", "🌐");
            var connectionSettings = networkSettings.AddChild("connection", "连接", "🔗");
            connectionSettings.AddAction("test", "测试", "🧪");
            connectionSettings.AddAction("optimize", "优化", "⚡");
            connectionSettings.AddAction("reset", "重置", "🔄");
            
            var debugSettings = systemSettings.AddChild("debug", "调试", "🐛");
            var loggingSettings = debugSettings.AddChild("logging", "日志", "📝");
            var errorLogs = loggingSettings.AddChild("errors", "错误日志", "❌");
            errorLogs.AddAction("view", "查看", "👁️");
            errorLogs.AddAction("export", "导出", "📤");
            errorLogs.AddAction("clear", "清除", "🗑️");
            
            var debugLogs = loggingSettings.AddChild("debug", "调试日志", "🐛");
            debugLogs.AddAction("enable", "启用", "✅");
            debugLogs.AddAction("filter", "过滤", "🔍");
            debugLogs.AddAction("export", "导出", "📤");
            
            settingsTreeData.AddRange(new[] { userSettings, appSettings, systemSettings });
            settingsTree.SetData(settingsTreeData);

            settingsTree.style.width = Length.Percent(30);
            settingsTree.style.height = 200;

            container.Add(fileTree);
            container.Add(projectTree);
            container.Add(settingsTree);

            root.Add(container);
        }
    }
}