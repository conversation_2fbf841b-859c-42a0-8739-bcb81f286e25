using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace BlastingDesign.UI.Core
{
    /// <summary>
    /// TextField扩展方法，用于添加键盘事件阻断功能
    /// </summary>
    public static class TextFieldExtensions
    {
        // 用于跟踪已添加键盘事件阻断的TextField，避免重复绑定
        private static readonly ConditionalWeakTable<TextField, object> _processedTextFields = new ConditionalWeakTable<TextField, object>();

        /// <summary>
        /// 为TextField添加焦点时的键盘事件阻断功能
        /// 当TextField获得焦点时，自动阻断键盘事件传播到场景
        /// 当TextField失去焦点时，恢复场景键盘控制
        /// </summary>
        /// <param name="textField">要添加功能的TextField</param>
        /// <param name="enableDebugLogging">是否启用调试日志</param>
        /// <returns>TextField实例，支持链式调用</returns>
        public static TextField AddFocusBasedKeyboardEventBlocker(this TextField textField, bool enableDebugLogging = false)
        {
            if (textField == null)
            {
                if (enableDebugLogging)
                    Logging.LogWarning("TextFieldExtensions", "尝试为null TextField添加键盘事件阻断");
                return null;
            }

            // 检查是否已经处理过这个TextField
            if (_processedTextFields.TryGetValue(textField, out _))
            {
                if (enableDebugLogging)
                {
                    Logging.LogInfo("TextFieldExtensions",
                        $"TextField '{textField.name}' 已经添加过键盘事件阻断，跳过重复处理");
                }
                return textField;
            }

            // 标记这个TextField已经处理过
            _processedTextFields.Add(textField, null);

            // 添加焦点进入事件处理
            textField.RegisterCallback<FocusInEvent>(evt =>
            {
                if (enableDebugLogging)
                {
                    Logging.LogInfo("TextFieldExtensions",
                        $"TextField '{textField.name}' 获得焦点，添加键盘事件阻断");
                }

                // 使用UIEventBlocker为TextField添加键盘事件阻断
                UIEventBlocker.AddKeyboardEventBlocker(textField, enableDebugLogging);
            });

            // 添加焦点离开事件处理
            textField.RegisterCallback<FocusOutEvent>(evt =>
            {
                if (enableDebugLogging)
                {
                    Logging.LogInfo("TextFieldExtensions",
                        $"TextField '{textField.name}' 失去焦点，键盘事件阻断将失效");
                }

                // 注意：UI Toolkit不支持直接移除事件回调
                // 但当TextField失去焦点时，键盘事件不会再传递到这个元素
                // 所以键盘事件会自动恢复传播到场景
            });

            if (enableDebugLogging)
            {
                Logging.LogInfo("TextFieldExtensions",
                    $"已为TextField '{textField.name}' 添加基于焦点的键盘事件阻断功能");
            }

            return textField;
        }

        /// <summary>
        /// 为TextField添加永久的键盘事件阻断
        /// 注意：这会永久阻断键盘事件，使用时需要谨慎
        /// </summary>
        /// <param name="textField">要添加功能的TextField</param>
        /// <param name="enableDebugLogging">是否启用调试日志</param>
        /// <returns>TextField实例，支持链式调用</returns>
        public static TextField AddPermanentKeyboardEventBlocker(this TextField textField, bool enableDebugLogging = false)
        {
            if (textField == null)
            {
                if (enableDebugLogging)
                    Logging.LogWarning("TextFieldExtensions", "尝试为null TextField添加永久键盘事件阻断");
                return null;
            }

            UIEventBlocker.AddKeyboardEventBlocker(textField, enableDebugLogging);

            if (enableDebugLogging)
            {
                Logging.LogInfo("TextFieldExtensions",
                    $"已为TextField '{textField.name}' 添加永久键盘事件阻断");
            }

            return textField;
        }

        /// <summary>
        /// 为TextField添加完整的事件阻断（包括键盘、鼠标、滚轮事件）
        /// </summary>
        /// <param name="textField">要添加功能的TextField</param>
        /// <param name="enableDebugLogging">是否启用调试日志</param>
        /// <param name="respectDragContinuity">是否尊重拖拽持续性</param>
        /// <returns>TextField实例，支持链式调用</returns>
        public static TextField AddCompleteEventBlocker(this TextField textField, bool enableDebugLogging = false, bool respectDragContinuity = true)
        {
            if (textField == null)
            {
                if (enableDebugLogging)
                    Logging.LogWarning("TextFieldExtensions", "尝试为null TextField添加完整事件阻断");
                return null;
            }

            UIEventBlocker.AddCompleteEventBlocker(textField, enableDebugLogging, respectDragContinuity);

            if (enableDebugLogging)
            {
                Logging.LogInfo("TextFieldExtensions",
                    $"已为TextField '{textField.name}' 添加完整事件阻断");
            }

            return textField;
        }
    }
}