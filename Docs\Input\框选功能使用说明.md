# Unity 左键框选物体功能使用说明

## 功能概述

本项目实现了完整的Unity左键框选物体系统，支持单选、多选、框选等功能，并与现有的视角控制系统完美集成。

## 主要功能

### 1. 单选功能
- **操作方式**: 左键点击物体
- **功能**: 选择单个物体
- **特点**: 点击空白区域会清空选择

### 2. 多选功能
- **操作方式**: Ctrl + 左键点击物体
- **功能**: 添加或移除物体到选择列表
- **特点**: 支持累积选择多个物体

### 3. 框选功能
- **操作方式**: 左键拖拽绘制选择框
- **功能**: 选择框内的所有物体
- **特点**: 实时显示选择框，支持可视化反馈

### 4. 视觉反馈
- **选中状态**: 物体变为黄色高亮
- **悬停状态**: 物体变为青色高亮
- **选择框**: 蓝色半透明矩形框

## 核心组件

### SelectionManager
选择管理器，负责管理所有选择逻辑
- 单例模式，全局访问
- 管理选中物体列表
- 处理选择事件
- 提供选择状态查询

### Selectable
可选择物体组件，标记物体为可选择
- 管理物体的选择状态
- 处理视觉效果切换
- 支持自定义材质
- 提供边界检测

### SelectionBox
选择框UI组件，处理框选视觉效果
- 支持Canvas UI和OnGUI两种渲染方式
- 动画边框效果
- 自定义颜色和样式
- 性能优化

### InputManager (已集成)
输入管理器，处理所有输入逻辑
- 集成选择功能
- 与视角控制不冲突
- 支持快捷键切换
- 实时状态显示

## 使用步骤

### 1. 基本设置
1. 确保场景中有 `InputManager` 组件
2. `InputManager` 会自动创建 `SelectionManager`
3. 为需要选择的物体添加 `Selectable` 组件

### 2. 添加可选择物体
```csharp
// 为GameObject添加Selectable组件
GameObject obj = GameObject.CreatePrimitive(PrimitiveType.Cube);
Selectable selectable = obj.AddComponent<Selectable>();
selectable.originalMaterial = obj.GetComponent<Renderer>().material;
```

### 3. 自定义选择材质
```csharp
// 在SelectionManager中设置自定义材质
selectionManager.selectedMaterial = yourSelectedMaterial;
selectionManager.highlightMaterial = yourHighlightMaterial;
```

## 操作说明

### 基本操作
- **左键点击**: 选择单个物体
- **左键拖拽**: 框选多个物体
- **Ctrl + 左键**: 多选模式（添加/移除选择）
- **点击空白**: 清空所有选择

### 视角控制（不受影响）
- **鼠标中键拖动**: 旋转视角
- **Shift + 中键拖动**: 平移视角
- **滚轮**: 缩放视角
- **Ctrl + 中键拖动**: 平滑缩放
- **Tab**: 切换输入模式

### 测试快捷键（SelectionTest组件）
- **C**: 清空所有选择
- **A**: 选择所有物体
- **R**: 随机选择物体
- **Delete**: 删除选中的物体

## 配置选项

### SelectionManager 设置
- `selectedMaterial`: 选中状态材质
- `highlightMaterial`: 悬停状态材质
- `selectableLayerMask`: 可选择物体的层级掩码
- `enableBoxSelection`: 是否启用框选
- `boxSelectionColor`: 框选填充颜色
- `boxBorderColor`: 框选边框颜色

### Selectable 设置
- `canBeSelected`: 是否可以被选择
- `showSelectionOutline`: 是否显示选择轮廓
- `selectedOutlineColor`: 选中轮廓颜色
- `hoveredOutlineColor`: 悬停轮廓颜色
- `outlineWidth`: 轮廓宽度

### InputManager 设置
- `enableSelection`: 是否启用选择功能
- `selectionLayerMask`: 选择检测的层级掩码

## 事件系统

### SelectionManager 事件
```csharp
// 选择改变事件
selectionManager.OnSelectionChanged += (selectedObjects) => {
    Debug.Log($"选中了 {selectedObjects.Count} 个物体");
};

// 物体悬停事件
selectionManager.OnObjectHovered += (hoveredObject) => {
    if (hoveredObject != null)
        Debug.Log($"悬停在 {hoveredObject.name}");
};
```

## 性能优化

### 已实现的优化
1. **单例模式**: 避免重复创建管理器
2. **事件驱动**: 减少不必要的更新
3. **层级过滤**: 只检测指定层级的物体
4. **UI优化**: 支持Canvas和OnGUI两种渲染方式
5. **边界检测**: 使用精确的边界框检测

### 建议的优化
1. 对于大量物体，考虑使用空间分割算法
2. 可以添加LOD系统，远距离物体使用简化检测
3. 考虑使用对象池管理选择框UI

## 扩展功能

### 可以添加的功能
1. **选择组**: 支持物体分组选择
2. **选择过滤**: 按类型、标签等过滤选择
3. **选择历史**: 支持撤销/重做选择操作
4. **批量操作**: 对选中物体进行批量变换
5. **选择保存**: 保存和加载选择状态

### 自定义扩展示例
```csharp
// 扩展Selectable组件
public class CustomSelectable : Selectable
{
    protected override void OnSelected()
    {
        base.OnSelected();
        // 添加自定义选择逻辑
        PlaySelectionSound();
        TriggerSelectionEffect();
    }
}
```

## 故障排除

### 常见问题
1. **物体无法选择**: 检查是否添加了Selectable组件和Collider
2. **框选不工作**: 检查SelectionManager是否正确初始化
3. **视觉效果异常**: 检查材质设置和渲染管线兼容性
4. **性能问题**: 检查物体数量和层级设置

### 调试工具
- 启用 `showDebugInfo` 查看选择状态
- 使用 `SelectionTest` 组件进行功能测试
- 查看Console输出的调试信息

## 版本兼容性

- **Unity版本**: 2021.3 LTS 及以上
- **渲染管线**: 支持Built-in、URP、HDRP
- **输入系统**: 使用新版Input System
- **平台支持**: Windows、Mac、Linux

## 更新日志

### v1.0.0
- 实现基本的单选、多选、框选功能
- 集成到现有输入系统
- 添加视觉反馈和事件系统
- 提供完整的测试工具

---

如有问题或建议，请查看代码注释或联系开发者。
