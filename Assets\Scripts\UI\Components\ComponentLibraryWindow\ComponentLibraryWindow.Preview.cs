using System;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.UI.Core;
using BlastingDesign.UI.Components.Previews;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// ComponentLibraryWindow 预览相关功能部分
    /// </summary>
    public partial class ComponentLibraryWindow : UIComponentBase
    {
        #region 组件预览创建

        /// <summary>
        /// 创建组件预览内容
        /// </summary>
        private VisualElement CreateComponentPreview(string componentId, string componentName)
        {
            var container = new VisualElement();
            container.AddToClassList("component-preview");

            try
            {
                // 预览标题
                var previewHeader = new VisualElement();
                previewHeader.AddToClassList("component-preview-header");

                var titleLabel = new Label(componentName);
                titleLabel.AddToClassList("component-preview-title");
                previewHeader.Add(titleLabel);

                container.Add(previewHeader);

                // 预览体 - 创建实际的DaisyUI组件
                var previewBody = new VisualElement();
                previewBody.AddToClassList("component-preview-body");

                // 根据组件ID创建实际的DaisyUI组件预览
                var actualComponent = CreateActualDaisyComponent(componentId);
                if (actualComponent != null)
                {
                    previewBody.Add(actualComponent);
                }
                else
                {
                    var previewLabel = new Label($"这里将显示 {componentName} 的实时预览效果");
                    previewLabel.AddToClassList("text-center");
                    previewBody.Add(previewLabel);
                }

                container.Add(previewBody);

                // 代码示例
                var codeBlock = CreateCodeBlock(componentId);
                container.Add(codeBlock);
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"创建组件预览时发生错误: {ex.Message}");

                var errorLabel = new Label($"预览创建失败: {ex.Message}");
                errorLabel.style.color = Color.red;
                container.Add(errorLabel);
            }

            return container;
        }

        /// <summary>
        /// 创建实际的DaisyUI组件用于预览
        /// </summary>
        private VisualElement CreateActualDaisyComponent(string componentId)
        {
            try
            {
                // 使用预览提供者管理器获取预览
                var provider = ComponentPreviewManager.Instance.GetProvider(componentId);
                if (provider != null)
                {
                    return provider.CreatePreview();
                }

                // 如果没有找到对应的预览提供者，返回默认内容
                var fallbackContainer = new VisualElement();
                var fallbackLabel = new Label($"暂无 {componentId} 的预览内容");
                fallbackLabel.AddToClassList("text-center");
                fallbackLabel.AddToClassList("text-gray-500");
                fallbackContainer.Add(fallbackLabel);

                return fallbackContainer;
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"创建DaisyUI组件预览时发生错误: {ex.Message}");

                // 返回错误提示
                var errorContainer = new VisualElement();
                var errorLabel = new Label($"预览创建失败: {ex.Message}");
                errorLabel.style.color = Color.red;
                errorContainer.Add(errorLabel);

                return errorContainer;
            }
        }

        #endregion

        #region 具体组件预览创建 - 已迁移到预览提供者系统

        // 注意：所有具体的预览创建方法已迁移到独立的预览提供者类中
        // 请参考 Assets/Scripts/UI/Components/ComponentLibraryWindow/Previews/ 目录下的文件





        /// <summary>
        /// 从预览提供者获取组件代码示例
        /// </summary>
        private string GetComponentCodeFromProvider(string componentId)
        {
            try
            {
                var provider = ComponentPreviewManager.Instance.GetProvider(componentId);
                if (provider != null)
                {
                    return provider.GetCodeExample();
                }

                return $"// 暂无 {componentId} 的代码示例\n// 请检查预览提供者是否已正确注册";
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"获取组件代码示例时发生错误: {ex.Message}");
                return $"// 获取代码示例时发生错误: {ex.Message}";
            }
        }

        #endregion

        #region 代码块创建

        /// <summary>
        /// 创建代码块
        /// </summary>
        private VisualElement CreateCodeBlock(string componentId)
        {
            var codeBlock = new VisualElement();
            codeBlock.AddToClassList("code-block");

            // 代码块头部
            var codeHeader = new VisualElement();
            codeHeader.AddToClassList("code-block-header");

            var codeTitle = new Label("使用示例");
            codeTitle.AddToClassList("code-block-title");
            codeHeader.Add(codeTitle);

            var copyButton = new Button(() => HandleCodeCopyFromProvider(componentId))
            {
                text = "复制"
            };
            copyButton.AddToClassList("copy-button");
            codeHeader.Add(copyButton);

            codeBlock.Add(codeHeader);

            // 代码内容
            var codeContent = new Label(GetComponentCodeFromProvider(componentId));
            codeContent.style.whiteSpace = WhiteSpace.PreWrap;
            codeBlock.Add(codeContent);

            return codeBlock;
        }

        /// <summary>
        /// 从预览提供者处理代码复制
        /// </summary>
        private void HandleCodeCopyFromProvider(string componentId)
        {
            try
            {
                var code = GetComponentCodeFromProvider(componentId);
                // 这里可以添加实际的复制到剪贴板的逻辑
                UIEventSystem.TriggerStatusMessage("代码已复制到剪贴板");
                Logging.LogInfo(componentName, $"代码已复制: {componentId}");
            }
            catch (Exception ex)
            {
                Logging.LogError(componentName, $"复制代码时发生错误: {ex.Message}");
                UIEventSystem.TriggerStatusMessage("复制代码失败");
            }
        }

        #endregion
    }
}