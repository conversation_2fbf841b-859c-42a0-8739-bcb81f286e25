using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI.Components.DataDisplay
{
    /// <summary>
    /// DaisyUI卡片组件
    /// 提供标题、内容、操作区域的卡片布局
    /// </summary>
    public class DaisyCard : DaisyComponent
    {
        #region 私有字段

        private const string TEMPLATE_PATH = "DaisyUI/Components/DataDisplay/Card/DaisyCard";
        
        private VisualElement _cardBody;
        private VisualElement _cardActions;
        private Label _cardTitle;
        private VisualElement _cardContent;
        private VisualElement _cardImage;

        #endregion

        #region 属性

        /// <summary>
        /// 卡片标题
        /// </summary>
        public string Title
        {
            get => _cardTitle?.text ?? string.Empty;
            set
            {
                if (_cardTitle != null)
                {
                    _cardTitle.text = value ?? string.Empty;
                    if (string.IsNullOrEmpty(value))
                    {
                        _cardTitle.AddToClassList("daisy-hidden");
                        _cardTitle.RemoveFromClassList("daisy-visible");
                    }
                    else
                    {
                        _cardTitle.RemoveFromClassList("daisy-hidden");
                        _cardTitle.AddToClassList("daisy-visible");
                    }
                }
            }
        }

        /// <summary>
        /// 卡片主体容器
        /// </summary>
        public VisualElement CardBody => _cardBody;

        /// <summary>
        /// 卡片操作区域
        /// </summary>
        public VisualElement CardActions => _cardActions;

        /// <summary>
        /// 卡片内容区域
        /// </summary>
        public VisualElement CardContent => _cardContent;

        /// <summary>
        /// 模板路径
        /// </summary>
        protected override string TemplatePath => TEMPLATE_PATH;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public DaisyCard() : base("card")
        {
            CreateCardStructure();
        }

        #endregion

        #region 静态工厂方法

        /// <summary>
        /// 创建卡片组件
        /// </summary>
        /// <returns>DaisyCard实例</returns>
        public static DaisyCard Create()
        {
            return new DaisyCard();
        }

        /// <summary>
        /// 创建带标题的卡片
        /// </summary>
        /// <param name="title">卡片标题</param>
        /// <returns>DaisyCard实例</returns>
        public static DaisyCard Create(string title)
        {
            var card = new DaisyCard();
            card.SetTitle(title);
            return card;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建卡片结构
        /// </summary>
        private void CreateCardStructure()
        {
            // 创建卡片主体
            _cardBody = new VisualElement();
            _cardBody.AddToClassList("daisy-card-body");
            Add(_cardBody);

            // 创建标题
            _cardTitle = new Label();
            _cardTitle.AddToClassList("daisy-card-title");
            _cardTitle.AddToClassList("daisy-hidden"); // 默认隐藏
            _cardBody.Add(_cardTitle);

            // 创建内容容器
            _cardContent = new VisualElement();
            _cardContent.AddToClassList("daisy-card-content");
            _cardBody.Add(_cardContent);
        }

        /// <summary>
        /// 确保操作区域存在
        /// </summary>
        private void EnsureActionsContainer()
        {
            if (_cardActions == null)
            {
                _cardActions = new VisualElement();
                _cardActions.AddToClassList("daisy-card-actions");
                Add(_cardActions);
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置卡片标题
        /// </summary>
        /// <param name="title">标题文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard SetTitle(string title)
        {
            Title = title;
            return this;
        }

        /// <summary>
        /// 添加内容元素
        /// </summary>
        /// <param name="content">内容元素</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard AddContent(VisualElement content)
        {
            if (content != null)
            {
                _cardContent.Add(content);
            }
            return this;
        }

        /// <summary>
        /// 设置内容文本
        /// </summary>
        /// <param name="text">内容文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard SetContent(string text)
        {
            var label = new Label(text);
            label.AddToClassList("daisy-card-text");
            return AddContent(label);
        }

        /// <summary>
        /// 添加操作按钮
        /// </summary>
        /// <param name="actions">操作按钮数组</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard AddActions(params VisualElement[] actions)
        {
            EnsureActionsContainer();

            foreach (var action in actions)
            {
                if (action != null)
                {
                    _cardActions.Add(action);
                }
            }
            return this;
        }

        /// <summary>
        /// 设置图片
        /// </summary>
        /// <param name="imageElement">图片元素</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard SetImage(VisualElement imageElement)
        {
            if (_cardImage != null)
            {
                Remove(_cardImage);
            }

            _cardImage = imageElement;
            _cardImage?.AddToClassList("daisy-card-image");

            if (_cardImage != null)
            {
                Insert(0, _cardImage); // 插入到最前面
            }

            return this;
        }

        #endregion

        #region 修饰符方法

        /// <summary>
        /// 设置紧凑样式
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard SetCompact()
        {
            return (DaisyCard)SetModifier("compact");
        }

        /// <summary>
        /// 设置边框样式
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard SetBordered()
        {
            return (DaisyCard)SetModifier("bordered");
        }

        /// <summary>
        /// 设置玻璃效果
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard SetGlass()
        {
            return (DaisyCard)SetModifier("glass");
        }

        /// <summary>
        /// 设置阴影效果
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard SetShadow()
        {
            return (DaisyCard)SetModifier("shadow");
        }

        /// <summary>
        /// 设置侧边样式
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyCard SetSide()
        {
            return (DaisyCard)SetModifier("side");
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 组件特定的初始化
        /// </summary>
        protected override void OnInitialize()
        {
            Logging.LogInfo("DaisyCard", "卡片组件初始化完成");
        }

        #endregion
    }
}
