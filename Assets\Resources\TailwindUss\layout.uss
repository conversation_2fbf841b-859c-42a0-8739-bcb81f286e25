* {
  margin: 0;
  padding: 0;
  white-space: normal;
}

.hidden {
  display: none;
}

.flex {
  display: flex;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.inset--0 {
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.top-0 {
  top: 0px;
}

.bottom-0 {
  bottom: 0px;
}

.left-0 {
  left: 0px;
}

.right-0 {
  right: 0px;
}

.inset--1 {
  top: 1px;
  bottom: 1px;
  left: 1px;
  right: 1px;
}

.inset-x-1 {
  left: 1px;
  right: 1px;
}

.inset-y-1 {
  top: 1px;
  bottom: 1px;
}

.top-1 {
  top: 1px;
}

.bottom-1 {
  bottom: 1px;
}

.left-1 {
  left: 1px;
}

.right-1 {
  right: 1px;
}

.inset--2 {
  top: 2px;
  bottom: 2px;
  left: 2px;
  right: 2px;
}

.inset-x-2 {
  left: 2px;
  right: 2px;
}

.inset-y-2 {
  top: 2px;
  bottom: 2px;
}

.top-2 {
  top: 2px;
}

.bottom-2 {
  bottom: 2px;
}

.left-2 {
  left: 2px;
}

.right-2 {
  right: 2px;
}

.inset--4 {
  top: 4px;
  bottom: 4px;
  left: 4px;
  right: 4px;
}

.inset-x-4 {
  left: 4px;
  right: 4px;
}

.inset-y-4 {
  top: 4px;
  bottom: 4px;
}

.top-4 {
  top: 4px;
}

.bottom-4 {
  bottom: 4px;
}

.left-4 {
  left: 4px;
}

.right-4 {
  right: 4px;
}

.inset--8 {
  top: 8px;
  bottom: 8px;
  left: 8px;
  right: 8px;
}

.inset-x-8 {
  left: 8px;
  right: 8px;
}

.inset-y-8 {
  top: 8px;
  bottom: 8px;
}

.top-8 {
  top: 8px;
}

.bottom-8 {
  bottom: 8px;
}

.left-8 {
  left: 8px;
}

.right-8 {
  right: 8px;
}

.inset--auto {
  top: auto;
  bottom: auto;
  left: auto;
  right: auto;
}

.inset-x-auto {
  left: auto;
  right: auto;
}

.inset-y-auto {
  top: auto;
  bottom: auto;
}

.top-auto {
  top: auto;
}

.bottom-auto {
  bottom: auto;
}

.left-auto {
  left: auto;
}

.right-auto {
  right: auto;
}

.inset--one-fourth {
  top: 25%;
  bottom: 25%;
  left: 25%;
  right: 25%;
}

.inset-x-one-fourth {
  left: 25%;
  right: 25%;
}

.inset-y-one-fourth {
  top: 25%;
  bottom: 25%;
}

.top-one-fourth {
  top: 25%;
}

.bottom-one-fourth {
  bottom: 25%;
}

.left-one-fourth {
  left: 25%;
}

.right-one-fourth {
  right: 25%;
}

.inset--one-third {
  top: 33.333333%;
  bottom: 33.333333%;
  left: 33.333333%;
  right: 33.333333%;
}

.inset-x-one-third {
  left: 33.333333%;
  right: 33.333333%;
}

.inset-y-one-third {
  top: 33.333333%;
  bottom: 33.333333%;
}

.top-one-third {
  top: 33.333333%;
}

.bottom-one-third {
  bottom: 33.333333%;
}

.left-one-third {
  left: 33.333333%;
}

.right-one-third {
  right: 33.333333%;
}

.inset--one-half {
  top: 50%;
  bottom: 50%;
  left: 50%;
  right: 50%;
}

.inset-x-one-half {
  left: 50%;
  right: 50%;
}

.inset-y-one-half {
  top: 50%;
  bottom: 50%;
}

.top-one-half {
  top: 50%;
}

.bottom-one-half {
  bottom: 50%;
}

.left-one-half {
  left: 50%;
}

.right-one-half {
  right: 50%;
}

.inset--two-thirds {
  top: 66.66666%;
  bottom: 66.66666%;
  left: 66.66666%;
  right: 66.66666%;
}

.inset-x-two-thirds {
  left: 66.66666%;
  right: 66.66666%;
}

.inset-y-two-thirds {
  top: 66.66666%;
  bottom: 66.66666%;
}

.top-two-thirds {
  top: 66.66666%;
}

.bottom-two-thirds {
  bottom: 66.66666%;
}

.left-two-thirds {
  left: 66.66666%;
}

.right-two-thirds {
  right: 66.66666%;
}

.inset--three-fourths {
  top: 75%;
  bottom: 75%;
  left: 75%;
  right: 75%;
}

.inset-x-three-fourths {
  left: 75%;
  right: 75%;
}

.inset-y-three-fourths {
  top: 75%;
  bottom: 75%;
}

.top-three-fourths {
  top: 75%;
}

.bottom-three-fourths {
  bottom: 75%;
}

.left-three-fourths {
  left: 75%;
}

.right-three-fourths {
  right: 75%;
}

.inset--full {
  top: 100%;
  bottom: 100%;
  left: 100%;
  right: 100%;
}

.inset-x-full {
  left: 100%;
  right: 100%;
}

.inset-y-full {
  top: 100%;
  bottom: 100%;
}

.top-full {
  top: 100%;
}

.bottom-full {
  bottom: 100%;
}

.left-full {
  left: 100%;
}

.right-full {
  right: 100%;
}

.box-border {
  -unity-overflow-clip-box: padding-box;
}

.box-content {
  -unity-overflow-clip-box: content-box;
}