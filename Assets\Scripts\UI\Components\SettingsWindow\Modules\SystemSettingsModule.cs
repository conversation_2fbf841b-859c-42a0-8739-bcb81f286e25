using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 系统设置模块
    /// </summary>
    public class SystemSettingsModule : SettingsModule
    {
        #region 属性

        public override string ModuleId => "system";
        public override string DisplayName => "⚡ 系统";
        public override string Description => "系统性能优化和存储路径配置";
        public override int Priority => 2;

        #endregion

        #region 私有字段

        private IntegerField maxConnectionsField;
        private SliderInt memoryCacheSizeSlider;
        private Label memoryCacheSizeLabel;
        private TextField workDirectoryField;
        private TextField tempDirectoryField;

        #endregion

        #region 实现抽象方法

        public override VisualElement CreateUI()
        {
            var root = new VisualElement();
            root.AddToClassList("settings-module");

            // 标题
            var title = new Label("系统设置");
            title.AddToClassList("module-title");
            root.Add(title);

            // 性能设置组
            var performanceGroup = new VisualElement();
            performanceGroup.AddToClassList("settings-group");
            
            var performanceGroupTitle = new Label("性能设置");
            performanceGroupTitle.AddToClassList("group-title");
            performanceGroup.Add(performanceGroupTitle);

            // 最大连接数
            var maxConnectionsContainer = new VisualElement();
            maxConnectionsContainer.AddToClassList("setting-item");
            
            var maxConnectionsLabel = new Label("最大连接数");
            maxConnectionsLabel.AddToClassList("setting-label");
            maxConnectionsContainer.Add(maxConnectionsLabel);
            
            maxConnectionsField = new IntegerField();
            maxConnectionsField.AddToClassList("setting-intfield");
            maxConnectionsContainer.Add(maxConnectionsField);
            
            performanceGroup.Add(maxConnectionsContainer);

            // 内存缓存大小
            var memoryCacheContainer = new VisualElement();
            memoryCacheContainer.AddToClassList("setting-item");
            
            var memoryCacheLabel = new Label("内存缓存大小 (MB)");
            memoryCacheLabel.AddToClassList("setting-label");
            memoryCacheContainer.Add(memoryCacheLabel);
            
            var memoryCacheSubContainer = new VisualElement();
            memoryCacheSubContainer.AddToClassList("setting-sub-container");
            
            memoryCacheSizeSlider = new SliderInt(64, 4096);
            memoryCacheSizeSlider.AddToClassList("setting-slider");
            memoryCacheSubContainer.Add(memoryCacheSizeSlider);
            
            memoryCacheSizeLabel = new Label("256");
            memoryCacheSizeLabel.AddToClassList("setting-value-label");
            memoryCacheSubContainer.Add(memoryCacheSizeLabel);
            
            memoryCacheContainer.Add(memoryCacheSubContainer);
            performanceGroup.Add(memoryCacheContainer);

            root.Add(performanceGroup);

            // 目录设置组
            var directoryGroup = new VisualElement();
            directoryGroup.AddToClassList("settings-group");
            
            var directoryGroupTitle = new Label("目录设置");
            directoryGroupTitle.AddToClassList("group-title");
            directoryGroup.Add(directoryGroupTitle);

            // 工作目录
            var workDirectoryContainer = new VisualElement();
            workDirectoryContainer.AddToClassList("setting-item");
            
            var workDirectoryLabel = new Label("工作目录");
            workDirectoryLabel.AddToClassList("setting-label");
            workDirectoryContainer.Add(workDirectoryLabel);
            
            workDirectoryField = new TextField();
            workDirectoryField.AddToClassList("setting-textfield");
            workDirectoryContainer.Add(workDirectoryField);
            
            directoryGroup.Add(workDirectoryContainer);

            // 临时目录
            var tempDirectoryContainer = new VisualElement();
            tempDirectoryContainer.AddToClassList("setting-item");
            
            var tempDirectoryLabel = new Label("临时目录");
            tempDirectoryLabel.AddToClassList("setting-label");
            tempDirectoryContainer.Add(tempDirectoryLabel);
            
            tempDirectoryField = new TextField();
            tempDirectoryField.AddToClassList("setting-textfield");
            tempDirectoryContainer.Add(tempDirectoryField);
            
            directoryGroup.Add(tempDirectoryContainer);

            root.Add(directoryGroup);

            // 绑定事件
            memoryCacheSizeSlider.RegisterValueChangedCallback(evt =>
            {
                memoryCacheSizeLabel.text = evt.newValue.ToString();
            });

            return root;
        }

        public override void LoadSettings(SettingsData settingsData)
        {
            if (settingsData == null) return;

            maxConnectionsField?.SetValueWithoutNotify(settingsData.MaxConnections);
            memoryCacheSizeSlider?.SetValueWithoutNotify(settingsData.MemoryCacheSize);
            workDirectoryField?.SetValueWithoutNotify(settingsData.WorkDirectory);
            tempDirectoryField?.SetValueWithoutNotify(settingsData.TempDirectory);

            if (memoryCacheSizeLabel != null)
                memoryCacheSizeLabel.text = settingsData.MemoryCacheSize.ToString();
        }

        public override void SaveSettings(SettingsData settingsData)
        {
            if (settingsData == null) return;

            settingsData.MaxConnections = maxConnectionsField?.value ?? settingsData.MaxConnections;
            settingsData.MemoryCacheSize = memoryCacheSizeSlider?.value ?? settingsData.MemoryCacheSize;
            settingsData.WorkDirectory = workDirectoryField?.value ?? settingsData.WorkDirectory;
            settingsData.TempDirectory = tempDirectoryField?.value ?? settingsData.TempDirectory;
        }

        public override void ResetToDefaults(SettingsData settingsData)
        {
            if (settingsData == null) return;

            settingsData.MaxConnections = 100;
            settingsData.MemoryCacheSize = 256;
            settingsData.WorkDirectory = "work";
            settingsData.TempDirectory = "temp";

            LoadSettings(settingsData);
        }

        public override bool ValidateSettings(SettingsData settingsData, out string errorMessage)
        {
            errorMessage = "";

            if (settingsData == null)
            {
                errorMessage = "设置数据为空";
                return false;
            }

            if (settingsData.MaxConnections < 1 || settingsData.MaxConnections > 1000)
            {
                errorMessage = "最大连接数必须在1-1000之间";
                return false;
            }

            if (settingsData.MemoryCacheSize < 64 || settingsData.MemoryCacheSize > 4096)
            {
                errorMessage = "内存缓存大小必须在64-4096MB之间";
                return false;
            }

            if (string.IsNullOrWhiteSpace(settingsData.WorkDirectory))
            {
                errorMessage = "工作目录不能为空";
                return false;
            }

            if (string.IsNullOrWhiteSpace(settingsData.TempDirectory))
            {
                errorMessage = "临时目录不能为空";
                return false;
            }

            return true;
        }

        #endregion
    }
}