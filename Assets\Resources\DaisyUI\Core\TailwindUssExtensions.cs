using UnityEngine.UIElements;

namespace BlastingDesign.UI.DaisyUI.Core
{
    /// <summary>
    /// TailwindUss扩展方法
    /// 提供基于TailwindUss样式库的便捷方法
    /// </summary>
    public static class TailwindUssExtensions
    {
        #region 间距工具类
        
        /// <summary>
        /// 设置外边距
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸（0, 1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24, 32, 40, 48, 56, 64, 72, 80, 96）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetMargin<T>(this T element, int size) where T : VisualElement
        {
            element.AddToClassList($"m-{size}");
            return element;
        }
        
        /// <summary>
        /// 设置水平外边距
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetMarginX<T>(this T element, int size) where T : VisualElement
        {
            element.AddToClassList($"mx-{size}");
            return element;
        }
        
        /// <summary>
        /// 设置垂直外边距
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetMarginY<T>(this T element, int size) where T : VisualElement
        {
            element.AddToClassList($"my-{size}");
            return element;
        }
        
        /// <summary>
        /// 设置内边距
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetPadding<T>(this T element, int size) where T : VisualElement
        {
            element.AddToClassList($"p-{size}");
            return element;
        }
        
        /// <summary>
        /// 设置水平内边距
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetPaddingX<T>(this T element, int size) where T : VisualElement
        {
            element.AddToClassList($"px-{size}");
            return element;
        }
        
        /// <summary>
        /// 设置垂直内边距
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetPaddingY<T>(this T element, int size) where T : VisualElement
        {
            element.AddToClassList($"py-{size}");
            return element;
        }
        
        #endregion
        
        #region 尺寸工具类
        
        /// <summary>
        /// 设置宽度
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸或百分比（如 "full", "auto", "one-half"）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetWidth<T>(this T element, string size) where T : VisualElement
        {
            element.AddToClassList($"w-{size}");
            return element;
        }
        
        /// <summary>
        /// 设置宽度（数值）
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸数值</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetWidth<T>(this T element, int size) where T : VisualElement
        {
            element.AddToClassList($"w-{size}");
            return element;
        }
        
        /// <summary>
        /// 设置高度
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸或百分比</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetHeight<T>(this T element, string size) where T : VisualElement
        {
            element.AddToClassList($"h-{size}");
            return element;
        }
        
        /// <summary>
        /// 设置高度（数值）
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">尺寸数值</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetHeight<T>(this T element, int size) where T : VisualElement
        {
            element.AddToClassList($"h-{size}");
            return element;
        }
        
        #endregion
        
        #region 布局工具类
        
        /// <summary>
        /// 设置为Flex布局
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetFlex<T>(this T element) where T : VisualElement
        {
            element.AddToClassList("flex");
            return element;
        }
        
        /// <summary>
        /// 设置Flex方向
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="direction">方向（row, col, row-reverse, col-reverse）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetFlexDirection<T>(this T element, string direction) where T : VisualElement
        {
            element.AddToClassList($"flex-{direction}");
            return element;
        }
        
        /// <summary>
        /// 设置对齐方式
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="alignment">对齐方式（start, center, end, between, around, evenly）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetJustifyContent<T>(this T element, string alignment) where T : VisualElement
        {
            element.AddToClassList($"justify-{alignment}");
            return element;
        }
        
        /// <summary>
        /// 设置交叉轴对齐
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="alignment">对齐方式（start, center, end, stretch, baseline）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetAlignItems<T>(this T element, string alignment) where T : VisualElement
        {
            element.AddToClassList($"items-{alignment}");
            return element;
        }
        
        #endregion
        
        #region 颜色工具类
        
        /// <summary>
        /// 设置文本颜色
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="color">颜色名称（如 "slate-600", "blue-500"）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetTextColor<T>(this T element, string color) where T : VisualElement
        {
            element.AddToClassList($"text-{color}");
            return element;
        }
        
        /// <summary>
        /// 设置背景颜色
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="color">颜色名称</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetBackgroundColor<T>(this T element, string color) where T : VisualElement
        {
            element.AddToClassList($"bg-{color}");
            return element;
        }
        
        /// <summary>
        /// 设置边框颜色
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="color">颜色名称</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetBorderColor<T>(this T element, string color) where T : VisualElement
        {
            element.AddToClassList($"border-{color}");
            return element;
        }
        
        #endregion
        
        #region 边框和圆角工具类
        
        /// <summary>
        /// 设置边框宽度
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="width">边框宽度（0, 2, 4, 8）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetBorderWidth<T>(this T element, int width) where T : VisualElement
        {
            element.AddToClassList($"border-{width}");
            return element;
        }
        
        /// <summary>
        /// 设置圆角
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">圆角大小（none, sm, md, lg, xl, full）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetRounded<T>(this T element, string size = "") where T : VisualElement
        {
            if (string.IsNullOrEmpty(size))
                element.AddToClassList("rounded");
            else
                element.AddToClassList($"rounded-{size}");
            return element;
        }
        
        #endregion
        
        #region 阴影工具类
        
        /// <summary>
        /// 设置阴影
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">阴影大小（sm, md, lg, xl）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T SetShadow<T>(this T element, string size = "") where T : VisualElement
        {
            if (string.IsNullOrEmpty(size))
                element.AddToClassList("shadow");
            else
                element.AddToClassList($"shadow-{size}");
            return element;
        }
        
        #endregion
        
        #region 可见性工具类
        
        /// <summary>
        /// 设置为可见
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T Show<T>(this T element) where T : VisualElement
        {
            element.RemoveFromClassList("daisy-hidden");
            element.AddToClassList("daisy-visible");
            return element;
        }
        
        /// <summary>
        /// 设置为隐藏
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T Hide<T>(this T element) where T : VisualElement
        {
            element.RemoveFromClassList("daisy-visible");
            element.AddToClassList("daisy-hidden");
            return element;
        }
        
        /// <summary>
        /// 切换可见性
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T ToggleVisibility<T>(this T element) where T : VisualElement
        {
            if (element.ClassListContains("daisy-hidden"))
                return element.Show();
            else
                return element.Hide();
        }
        
        #endregion
        
        #region DaisyUI样式工具类
        
        /// <summary>
        /// 应用DaisyUI玻璃效果
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T ApplyGlassEffect<T>(this T element) where T : VisualElement
        {
            element.AddToClassList("daisy-glass");
            return element;
        }
        
        /// <summary>
        /// 应用DaisyUI阴影效果
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">阴影大小（默认、md、lg、xl）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T ApplyDaisyShadow<T>(this T element, string size = "") where T : VisualElement
        {
            if (string.IsNullOrEmpty(size))
                element.AddToClassList("daisy-shadow");
            else
                element.AddToClassList($"daisy-shadow-{size}");
            return element;
        }
        
        /// <summary>
        /// 应用DaisyUI圆角效果
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="size">圆角大小（默认、md、lg、xl、full）</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T ApplyDaisyRounded<T>(this T element, string size = "") where T : VisualElement
        {
            if (string.IsNullOrEmpty(size))
                element.AddToClassList("daisy-rounded");
            else
                element.AddToClassList($"daisy-rounded-{size}");
            return element;
        }
        
        #endregion
        
        #region 链式调用工具类
        
        /// <summary>
        /// 条件应用样式
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="condition">条件</param>
        /// <param name="className">要应用的CSS类名</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T When<T>(this T element, bool condition, string className) where T : VisualElement
        {
            if (condition)
            {
                element.AddToClassList(className);
            }
            return element;
        }
        
        /// <summary>
        /// 条件应用操作
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="condition">条件</param>
        /// <param name="action">要执行的操作</param>
        /// <returns>目标元素，支持链式调用</returns>
        public static T When<T>(this T element, bool condition, System.Action<T> action) where T : VisualElement
        {
            if (condition)
            {
                action(element);
            }
            return element;
        }
        
        #endregion
    }
}