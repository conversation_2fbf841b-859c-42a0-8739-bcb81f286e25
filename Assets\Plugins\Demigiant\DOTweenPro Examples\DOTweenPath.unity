%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientEquatorColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientGroundColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 1
    m_BakeResolution: 50
    m_AtlasSize: 1024
    m_AO: 1
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 0
    m_ReflectionCompression: 2
    m_MixedBakeMode: 1
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 512
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 0
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 1
    m_PVRFilteringGaussRadiusAO: 1
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 20201, guid: 0000000000000000f000000000000000, type: 0}
  m_LightingSettings: {fileID: 4890085278179872738, guid: f16851dbb97f9491fb8bd0937314c98b, type: 2}
--- !u!196 &5
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666666
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &118309267
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 118309271}
  - component: {fileID: 118309270}
  - component: {fileID: 118309268}
  - component: {fileID: 118309269}
  m_Layer: 0
  m_Name: Cube A (w path)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &118309268
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118309267}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10302, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &118309269
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118309267}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -333801856, guid: aa0b1eebb5db27a419fa4564bbe5c9c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  updateType: 0
  isSpeedBased: 0
  hasOnStart: 0
  hasOnPlay: 0
  hasOnUpdate: 0
  hasOnStepComplete: 0
  hasOnComplete: 1
  hasOnTweenCreated: 0
  hasOnRewind: 0
  onStart:
    m_PersistentCalls:
      m_Calls: []
  onPlay:
    m_PersistentCalls:
      m_Calls: []
  onUpdate:
    m_PersistentCalls:
      m_Calls: []
  onStepComplete:
    m_PersistentCalls:
      m_Calls: []
  onComplete:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1087750405}
        m_TargetAssemblyTypeName: 
        m_MethodName: DOPlay
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  onTweenCreated:
    m_PersistentCalls:
      m_Calls: []
  onRewind:
    m_PersistentCalls:
      m_Calls: []
  delay: 0
  duration: 3
  easeType: 6
  easeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  loops: 1
  id: 
  loopType: 0
  orientType: 0
  lookAtTransform: {fileID: 0}
  lookAtPosition: {x: 0, y: 0, z: 0}
  lookAhead: 0.01
  autoPlay: 0
  autoKill: 1
  relative: 0
  isLocal: 0
  isClosedPath: 1
  pathResolution: 10
  pathMode: 1
  lockRotation: 0
  assignForwardAndUp: 0
  forwardDirection: {x: 0, y: 0, z: 1}
  upDirection: {x: 0, y: 1, z: 0}
  tweenRigidbody: 0
  wps:
  - {x: 3.8321667, y: 6.9544535, z: 0}
  - {x: -6.0542445, y: 9.465288, z: 0}
  - {x: -12.069765, y: 6.1698112, z: 0}
  - {x: -12.069786, y: -0.2641964, z: 0}
  - {x: -6.368099, y: -4.187376, z: 0}
  fullWps: []
  path:
    wpLengths:
    - 0
    - 9.359136
    - 10.405018
    - 7.102792
    - 6.6672983
    - 7.1915474
    - 11.289525
    wps:
    - {x: 4.45, y: -1.83, z: 0}
    - {x: 3.8321667, y: 6.9544535, z: 0}
    - {x: -6.0542445, y: 9.465288, z: 0}
    - {x: -12.069765, y: 6.1698112, z: 0}
    - {x: -12.069786, y: -0.2641964, z: 0}
    - {x: -6.368099, y: -4.187376, z: 0}
    - {x: 4.45, y: -1.83, z: 0}
    type: 1
    subdivisionsXSegment: 10
    subdivisions: 70
    controlPoints:
    - a: {x: -6.368099, y: -4.187376, z: 0}
      b: {x: 0, y: 0, z: 0}
    - a: {x: 3.8321667, y: 6.9544535, z: 0}
      b: {x: 0, y: 0, z: 0}
    length: 52.02238
    isFinalized: 1
    timesTable:
    - 0.014285714
    - 0.028571429
    - 0.042857144
    - 0.057142857
    - 0.071428575
    - 0.08571429
    - 0.1
    - 0.114285715
    - 0.12857144
    - 0.14285715
    - 0.15714286
    - 0.17142858
    - 0.18571429
    - 0.2
    - 0.21428572
    - 0.22857143
    - 0.24285714
    - 0.25714287
    - 0.27142859
    - 0.2857143
    - 0.3
    - 0.31428573
    - 0.32857144
    - 0.34285715
    - 0.35714287
    - 0.37142858
    - 0.3857143
    - 0.4
    - 0.41428572
    - 0.42857143
    - 0.44285715
    - 0.45714286
    - 0.47142857
    - 0.4857143
    - 0.5
    - 0.51428574
    - 0.5285714
    - 0.54285717
    - 0.55714285
    - 0.5714286
    - 0.5857143
    - 0.6
    - 0.6142857
    - 0.62857145
    - 0.64285713
    - 0.6571429
    - 0.67142856
    - 0.6857143
    - 0.7
    - 0.71428573
    - 0.7285714
    - 0.74285716
    - 0.75714284
    - 0.7714286
    - 0.78571427
    - 0.8
    - 0.8142857
    - 0.82857144
    - 0.8428571
    - 0.85714287
    - 0.87142855
    - 0.8857143
    - 0.9
    - 0.9142857
    - 0.92857146
    - 0.94285715
    - 0.9571429
    - 0.9714286
    - 0.9857143
    - 1
    lengthsTable:
    - 0.66792893
    - 1.3899281
    - 2.16896
    - 2.9980419
    - 3.8644814
    - 4.752426
    - 5.6444182
    - 6.5224686
    - 7.368998
    - 8.167948
    - 8.906464
    - 9.58573
    - 10.313842
    - 11.125577
    - 12.012393
    - 12.959278
    - 13.947668
    - 14.956963
    - 15.965341
    - 16.950247
    - 17.888689
    - 18.75748
    - 19.533484
    - 20.220243
    - 20.891918
    - 21.555
    - 22.208927
    - 22.852415
    - 23.483595
    - 24.100157
    - 24.699495
    - 25.278862
    - 25.83552
    - 26.366951
    - 26.8711
    - 27.376375
    - 27.912098
    - 28.475262
    - 29.060778
    - 29.66227
    - 30.272577
    - 30.884148
    - 31.48931
    - 32.080574
    - 32.650936
    - 33.194313
    - 33.709743
    - 34.240025
    - 34.79521
    - 35.371506
    - 35.965523
    - 36.574467
    - 37.19625
    - 37.829685
    - 38.474625
    - 39.132145
    - 39.804657
    - 40.496033
    - 41.23852
    - 42.10978
    - 43.088493
    - 44.145332
    - 45.25077
    - 46.375446
    - 47.49042
    - 48.567577
    - 49.58027
    - 50.50447
    - 51.32128
    - 52.02238
  inspectorMode: 0
  pathType: 1
  handlesType: 0
  livePreview: 1
  handlesDrawMode: 0
  perspectiveHandleSize: 0.5
  showIndexes: 1
  showWpLength: 0
  pathColor: {r: 1, g: 1, b: 1, a: 0.5}
  lastSrcPosition: {x: 4.45, y: -1.83, z: 0}
  lastSrcRotation: {x: 0, y: 0, z: 0, w: 0}
  wpsDropdown: 0
  dropToFloorOffset: 0
--- !u!33 &118309270
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118309267}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &118309271
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118309267}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.45, y: -1.83, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &848136767
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 848136772}
  - component: {fileID: 848136771}
  - component: {fileID: 848136769}
  - component: {fileID: 848136768}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &848136768
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848136767}
  m_Enabled: 1
--- !u!124 &848136769
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848136767}
  m_Enabled: 1
--- !u!20 &848136771
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848136767}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.16176468, g: 0.16176468, b: 0.16176468, a: 0.019607844}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &848136772
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848136767}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -30}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &970591913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 970591914}
  - component: {fileID: 970591916}
  - component: {fileID: 970591915}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &970591914
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 970591913}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1356883401}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &970591915
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 970591913}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.196, g: 0.196, b: 0.196, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: PLAY
--- !u!222 &970591916
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 970591913}
  m_CullTransparentMesh: 1
--- !u!1 &1053889438
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1053889442}
  - component: {fileID: 1053889441}
  - component: {fileID: 1053889440}
  - component: {fileID: 1053889439}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1053889439
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1053889438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d49b7c1bcd2e07499844da127be038d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_ForceModuleActive: 0
--- !u!114 &1053889440
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1053889438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &1053889441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1053889438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 5
--- !u!4 &1053889442
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1053889438}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1087750404
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1087750408}
  - component: {fileID: 1087750407}
  - component: {fileID: 1087750406}
  - component: {fileID: 1087750405}
  m_Layer: 0
  m_Name: Cube B (animated after Cube A)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1087750405
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1087750404}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -333801856, guid: aa0b1eebb5db27a419fa4564bbe5c9c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  updateType: 0
  isSpeedBased: 0
  hasOnStart: 0
  hasOnPlay: 0
  hasOnUpdate: 0
  hasOnStepComplete: 0
  hasOnComplete: 0
  hasOnTweenCreated: 0
  hasOnRewind: 0
  onStart:
    m_PersistentCalls:
      m_Calls: []
  onPlay:
    m_PersistentCalls:
      m_Calls: []
  onUpdate:
    m_PersistentCalls:
      m_Calls: []
  onStepComplete:
    m_PersistentCalls:
      m_Calls: []
  onComplete:
    m_PersistentCalls:
      m_Calls: []
  onTweenCreated:
    m_PersistentCalls:
      m_Calls: []
  onRewind:
    m_PersistentCalls:
      m_Calls: []
  delay: 0
  duration: 3
  easeType: 1
  easeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  loops: -1
  id: 
  loopType: 0
  orientType: 0
  lookAtTransform: {fileID: 0}
  lookAtPosition: {x: 0, y: 0, z: 0}
  lookAhead: 0.01
  autoPlay: 0
  autoKill: 1
  relative: 1
  isLocal: 0
  isClosedPath: 1
  pathResolution: 10
  pathMode: 1
  lockRotation: 0
  assignForwardAndUp: 0
  forwardDirection: {x: 0, y: 0, z: 1}
  upDirection: {x: 0, y: 1, z: 0}
  tweenRigidbody: 0
  wps:
  - {x: 13.417454, y: 2.978077, z: 0}
  - {x: 9.493597, y: 8.510473, z: 0}
  - {x: -3.3649445, y: 5.5603714, z: 0}
  - {x: -13.000723, y: 12.019701, z: 0}
  - {x: -19.387083, y: 4.5597005, z: 0}
  - {x: -15.279156, y: -3.646319, z: 0}
  - {x: -4.1231623, y: -1.0233588, z: 0}
  - {x: 3.7979867, y: -4.5040193, z: 0}
  fullWps: []
  path:
    wpLengths:
    - 0
    - 5.8644567
    - 7.2802324
    - 13.301847
    - 11.81175
    - 10.301735
    - 9.680841
    - 11.593836
    - 8.754475
    - 7.309784
    wps:
    - {x: 10.45, y: -1.83, z: 0}
    - {x: 13.417454, y: 2.978077, z: 0}
    - {x: 9.493597, y: 8.510473, z: 0}
    - {x: -3.3649445, y: 5.5603714, z: 0}
    - {x: -13.000723, y: 12.019701, z: 0}
    - {x: -19.387083, y: 4.5597005, z: 0}
    - {x: -15.279156, y: -3.646319, z: 0}
    - {x: -4.1231623, y: -1.0233588, z: 0}
    - {x: 3.7979867, y: -4.5040193, z: 0}
    - {x: 10.45, y: -1.83, z: 0}
    type: 1
    subdivisionsXSegment: 10
    subdivisions: 100
    controlPoints:
    - a: {x: 3.7979867, y: -4.5040193, z: 0}
      b: {x: 0, y: 0, z: 0}
    - a: {x: 13.417454, y: 2.978077, z: 0}
      b: {x: 0, y: 0, z: 0}
    length: 85.91056
    isFinalized: 1
    timesTable:
    - 0.01
    - 0.02
    - 0.03
    - 0.04
    - 0.049999997
    - 0.06
    - 0.07
    - 0.08
    - 0.089999996
    - 0.099999994
    - 0.11
    - 0.12
    - 0.13
    - 0.14
    - 0.14999999
    - 0.16
    - 0.17
    - 0.17999999
    - 0.19
    - 0.19999999
    - 0.21
    - 0.22
    - 0.22999999
    - 0.24
    - 0.25
    - 0.26
    - 0.26999998
    - 0.28
    - 0.29
    - 0.29999998
    - 0.31
    - 0.32
    - 0.32999998
    - 0.34
    - 0.35
    - 0.35999998
    - 0.37
    - 0.38
    - 0.39
    - 0.39999998
    - 0.41
    - 0.42
    - 0.42999998
    - 0.44
    - 0.45
    - 0.45999998
    - 0.47
    - 0.48
    - 0.48999998
    - 0.5
    - 0.51
    - 0.52
    - 0.53
    - 0.53999996
    - 0.55
    - 0.56
    - 0.57
    - 0.58
    - 0.59
    - 0.59999996
    - 0.61
    - 0.62
    - 0.63
    - 0.64
    - 0.65
    - 0.65999997
    - 0.66999996
    - 0.68
    - 0.69
    - 0.7
    - 0.71
    - 0.71999997
    - 0.72999996
    - 0.74
    - 0.75
    - 0.76
    - 0.77
    - 0.78
    - 0.78999996
    - 0.79999995
    - 0.81
    - 0.82
    - 0.83
    - 0.84
    - 0.84999996
    - 0.85999995
    - 0.87
    - 0.88
    - 0.89
    - 0.9
    - 0.90999997
    - 0.91999996
    - 0.93
    - 0.94
    - 0.95
    - 0.96
    - 0.96999997
    - 0.97999996
    - 0.98999995
    - 1
    lengthsTable:
    - 0.5545968
    - 1.1175883
    - 1.6830537
    - 2.245447
    - 2.7997518
    - 3.3416884
    - 3.867998
    - 4.376838
    - 4.868309
    - 5.3450947
    - 5.813135
    - 6.311367
    - 6.877433
    - 7.495066
    - 8.148528
    - 8.823632
    - 9.508459
    - 10.194385
    - 10.877503
    - 11.560418
    - 12.254057
    - 12.978595
    - 13.783311
    - 14.741959
    - 15.848983
    - 17.077965
    - 18.394625
    - 19.761522
    - 21.140495
    - 22.494476
    - 23.78951
    - 24.997858
    - 26.103634
    - 27.136875
    - 28.20907
    - 29.343433
    - 30.531721
    - 31.750658
    - 32.96895
    - 34.151752
    - 35.26419
    - 36.275455
    - 37.165974
    - 37.94238
    - 38.672184
    - 39.461693
    - 40.331173
    - 41.275455
    - 42.278408
    - 43.318016
    - 44.36911
    - 45.404976
    - 46.39841
    - 47.322807
    - 48.15364
    - 48.891544
    - 49.678425
    - 50.534695
    - 51.444313
    - 52.38865
    - 53.348053
    - 54.30292
    - 55.23473
    - 56.12729
    - 56.968613
    - 57.754013
    - 58.495777
    - 59.294735
    - 60.205452
    - 61.226448
    - 62.33766
    - 63.510315
    - 64.71169
    - 65.907745
    - 67.06497
    - 68.15262
    - 69.14602
    - 70.03488
    - 70.87562
    - 71.70776
    - 72.54151
    - 73.377945
    - 74.21172
    - 75.033615
    - 75.83277
    - 76.59903
    - 77.325615
    - 78.01288
    - 78.67338
    - 79.33375
    - 80.00281
    - 80.68108
    - 81.366684
    - 82.05582
    - 82.74328
    - 83.422775
    - 84.087204
    - 84.728836
    - 85.33948
    - 85.91056
  inspectorMode: 0
  pathType: 1
  handlesType: 0
  livePreview: 1
  handlesDrawMode: 0
  perspectiveHandleSize: 0.5
  showIndexes: 1
  showWpLength: 0
  pathColor: {r: 1, g: 1, b: 1, a: 0.5}
  lastSrcPosition: {x: 10.45, y: -1.83, z: 0}
  lastSrcRotation: {x: 0, y: 0, z: 0, w: 0}
  wpsDropdown: 0
  dropToFloorOffset: 0
--- !u!23 &1087750406
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1087750404}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10302, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1087750407
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1087750404}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1087750408
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1087750404}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 10.45, y: -1.83, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1127963255
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1127963256}
  - component: {fileID: 1127963258}
  - component: {fileID: 1127963257}
  m_Layer: 5
  m_Name: Info
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1127963256
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127963255}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1260203941}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -186}
  m_SizeDelta: {x: 300, y: 30}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1127963257
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127963255}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 1
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: The PLAY button starts Cube A path animation. When that is complete, it
    will start the other cube's animation (thanks to the Event created in Cube A's
    DOTweenPath Component)
--- !u!222 &1127963258
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127963255}
  m_CullTransparentMesh: 1
--- !u!1 &1242857378
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1242857380}
  - component: {fileID: 1242857379}
  m_Layer: 0
  m_Name: Directional light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1242857379
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242857378}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.802082
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 1
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &1242857380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242857378}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821794, y: -0.23456973, z: 0.10938166, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1260203940
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1260203941}
  - component: {fileID: 1260203944}
  - component: {fileID: 1260203943}
  - component: {fileID: 1260203942}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1260203941
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260203940}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1356883401}
  - {fileID: 1127963256}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &1260203942
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260203940}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1260203943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260203940}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1260203944
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260203940}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 1
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &1356883400
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1356883401}
  - component: {fileID: 1356883404}
  - component: {fileID: 1356883403}
  - component: {fileID: 1356883402}
  m_Layer: 5
  m_Name: Button
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1356883401
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1356883400}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 970591914}
  m_Father: {fileID: 1260203941}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -147}
  m_SizeDelta: {x: 160, y: 30}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1356883402
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1356883400}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Highlighted
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1356883403}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 118309269}
        m_TargetAssemblyTypeName: 
        m_MethodName: DOPlay
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &1356883403
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1356883400}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1356883404
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1356883400}
  m_CullTransparentMesh: 1
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 848136772}
  - {fileID: 1242857380}
  - {fileID: 118309271}
  - {fileID: 1087750408}
  - {fileID: 1260203941}
  - {fileID: 1053889442}
