# 图标设置快速参考

## 🚀 快速开始

### 1. 添加新图标的步骤
1. 将图标文件放入对应目录：
   - 工具栏图标 → `Assets/UI Toolkit/Icons/Toolbar/`
   - 菜单栏图标 → `Assets/UI Toolkit/Icons/MenuBar/`
   - 通用图标 → `Assets/UI Toolkit/Icons/Common/`

2. 设置导入参数：
   - Texture Type: `Sprite (2D and UI)`
   - Max Size: `32`
   - Format: `RGBA 32 bit`
   - Alpha Is Transparency: `✓`

3. 在CSS中添加样式：
   ```css
   .your-icon-name {
       background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/your-icon.png');
   }
   ```

4. 在配置中使用：
   - 工具栏：在ToolbarConfig中设置 `iconName: "your-icon-name"`
   - 菜单栏：在MenuBarConfig中设置 `iconName: "your-icon-name"`

### 2. 编辑器工具
- **工具栏配置**: `BlastingDesign > UI > Toolbar Config Editor`
- **菜单栏配置**: `BlastingDesign > UI > MenuBar Config Editor`
- **图标管理器**: `BlastingDesign > UI > Icon Manager`
- **简化版图标管理器**: `BlastingDesign > UI > Simple Icon Manager` (推荐用于测试)

### 3. 故障排除工具
如果图标管理器出现GUI错误，请尝试：
1. 使用简化版图标管理器进行测试
2. 检查Unity控制台中的错误信息
3. 重新启动Unity编辑器
4. 使用 `BlastingDesign > UI > Test Icon Manager` 进行诊断

## 📏 尺寸规范

| 用途 | 标准尺寸 | 响应式尺寸 |
|------|----------|------------|
| 工具栏图标 | 16×16px | 12-16px |
| 菜单栏图标 | 16×16px | 12-16px |
| 状态栏图标 | 12×12px | 10-12px |
| 面板按钮图标 | 12×12px | 10-12px |

## 🎨 CSS类名规范

### 命名模式
- 工具栏：`[功能名]-icon` (如：`select-icon`)
- 菜单栏：`[菜单]-[功能]-icon` (如：`file-new-icon`)
- 通用：`[功能]-icon` (如：`close-icon`)

### 常用图标类名
```css
/* 工具栏 */
.select-icon     /* 选择工具 */
.move-icon       /* 移动工具 */
.rotate-icon     /* 旋转工具 */
.scale-icon      /* 缩放工具 */
.pan-icon        /* 平移工具 */
.zoom-icon       /* 缩放工具 */
.play-icon       /* 播放按钮 */
.pause-icon      /* 暂停按钮 */

/* 菜单栏 */
.file-new-icon   /* 新建文件 */
.file-open-icon  /* 打开文件 */
.file-save-icon  /* 保存文件 */
.edit-undo-icon  /* 撤销 */
.edit-redo-icon  /* 重做 */
.edit-copy-icon  /* 复制 */
.edit-paste-icon /* 粘贴 */

/* 通用 */
.close-icon      /* 关闭 */
.settings-icon   /* 设置 */
.refresh-icon    /* 刷新 */
.collapse-icon   /* 折叠 */
.expand-icon     /* 展开 */
```

## 📁 目录结构

```
Assets/UI Toolkit/Icons/
├── Toolbar/              # 工具栏图标
│   ├── select-icon.png
│   ├── move-icon.png
│   ├── rotate-icon.png
│   ├── scale-icon.png
│   ├── pan-icon.png
│   ├── zoom-icon.png
│   └── play-icon.png
├── MenuBar/              # 菜单栏图标
│   ├── file-new.png
│   ├── file-open.png
│   ├── file-save.png
│   ├── edit-undo.png
│   ├── edit-redo.png
│   ├── edit-copy.png
│   └── edit-paste.png
├── Common/               # 通用图标
│   ├── close.png
│   ├── settings.png
│   ├── refresh.png
│   ├── collapse.png
│   └── expand.png
└── README_图标设置指南.md
```

## ⚙️ 导入设置模板

### 标准UI图标设置
```
Texture Type: Sprite (2D and UI)
Sprite Mode: Single
Pixels Per Unit: 100
Mesh Type: Full Rect
Generate Physics Shape: ❌
Read/Write Enabled: ❌
Generate Mip Maps: ❌
Alpha Source: Input Texture Alpha
Alpha Is Transparency: ✅
Filter Mode: Point (no filter)
Max Size: 32
Format: RGBA 32 bit
Compression: None
```

### 移动平台优化设置
```
Max Size: 32
Format: RGBA 16 bit
Compression: High Quality
Use Crunch Compression: ✅
Compressor Quality: 50
```

## 🔧 常用代码片段

### 动态切换图标
```csharp
// 切换播放/暂停图标
private void UpdatePlayIcon(bool isPlaying)
{
    var icon = playButton.Q<VisualElement>("tool-icon");
    icon.RemoveFromClassList("play-icon");
    icon.RemoveFromClassList("pause-icon");
    icon.AddToClassList(isPlaying ? "pause-icon" : "play-icon");
}
```

### 设置图标颜色
```csharp
// 设置图标颜色
private void SetIconColor(VisualElement icon, Color color)
{
    icon.style.unityBackgroundImageTintColor = color;
}
```

### 检查图标是否存在
```csharp
// 检查CSS类对应的图标是否存在
private bool IconExists(string iconClassName)
{
    var testElement = new VisualElement();
    testElement.AddToClassList(iconClassName);
    return testElement.resolvedStyle.backgroundImage.texture != null;
}
```

## 🎯 CSS样式模板

### 基础图标样式
```css
.tool-icon {
    width: 16px;
    height: 16px;
    background-color: rgb(210, 210, 210);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    flex-shrink: 0;
}
```

### 状态样式
```css
/* 选中状态 */
.tool-button.selected .tool-icon {
    background-color: rgb(255, 255, 255);
}

/* 禁用状态 */
.tool-button:disabled .tool-icon {
    background-color: rgb(128, 128, 128);
    opacity: 0.5;
}

/* 悬停效果 */
.tool-button:hover .tool-icon {
    background-color: rgb(230, 230, 230);
}
```

### 响应式样式
```css
/* 中等屏幕 */
@media (max-width: 1200px) {
    .tool-icon {
        width: 14px;
        height: 14px;
    }
}

/* 小屏幕 */
@media (max-width: 800px) {
    .tool-icon {
        width: 12px;
        height: 12px;
    }
}
```

## 🐛 故障排除

### 图标不显示
1. ✅ 检查文件路径是否正确
2. ✅ 确认CSS类名拼写
3. ✅ 验证图片导入设置
4. ✅ 检查文件是否在正确目录

### 图标模糊
1. ✅ 确保图标尺寸匹配CSS设置
2. ✅ 使用Point过滤模式
3. ✅ 检查像素对齐
4. ✅ 调整Max Size设置

### 图标颜色异常
1. ✅ 启用Alpha Is Transparency
2. ✅ 检查背景色设置
3. ✅ 验证图片透明度
4. ✅ 确认颜色空间设置

## 📋 检查清单

### 添加新图标前
- [ ] 图标尺寸符合规范
- [ ] 文件格式为PNG
- [ ] 支持透明背景
- [ ] 文件名清晰描述功能

### 导入设置后
- [ ] Texture Type设为Sprite
- [ ] Alpha Is Transparency已启用
- [ ] Max Size设置合理
- [ ] 平台特定设置已优化

### CSS配置后
- [ ] 类名符合命名规范
- [ ] 路径引用正确
- [ ] 基础样式已设置
- [ ] 状态样式已定义

### 配置文件更新后
- [ ] iconName字段已填写
- [ ] 配置文件已保存
- [ ] 组件已刷新
- [ ] 运行时测试通过

---

*💡 提示：使用 `BlastingDesign > UI > Icon Manager` 可以可视化管理所有图标*
