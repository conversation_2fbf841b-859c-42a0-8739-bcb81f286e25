using System.Collections.Generic;
using UnityEngine;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - DaisyTree组件测试
    /// 测试DaisyTree组件的所有功能
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region DaisyTree组件测试

        /// <summary>
        /// 测试DaisyTree组件
        /// </summary>
        private void TestDaisyTree()
        {
            LogTest("DaisyTree 组件测试");

            try
            {
                TestDaisyTreeBasicCreation();
                TestDaisyTreeDataBinding();
                TestDaisyTreeNodeOperations();
                TestDaisyTreeItemOperations();
                TestDaisyTreeSelection();
                TestDaisyTreeExpansion();
                TestDaisyTreeSearch();
                TestDaisyTreeActions();
                TestDaisyTreeStyling();
                TestDaisyTreeEvents();
                TestDaisyTreeChaining();
                TestDaisyTreeComplexScenarios();

                LogTestPass("DaisyTree 组件测试 - 所有测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisyTree 组件测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试DaisyTree基础创建
        /// </summary>
        private void TestDaisyTreeBasicCreation()
        {
            LogTest("DaisyTree 基础创建测试");

            // 测试基础构造函数
            var tree = new DaisyTree("test-tree");
            if (tree == null)
            {
                throw new System.Exception("基础Tree创建失败");
            }

            // 测试静态工厂方法
            var factoryTree = DaisyTree.Create("factory-tree");
            if (factoryTree == null)
            {
                throw new System.Exception("静态工厂方法创建失败");
            }

            // 验证TreeId设置
            if (tree.TreeId != "test-tree")
            {
                throw new System.Exception("TreeId设置失败");
            }

            // 验证类名设置
            if (!tree.ClassListContains("daisy-tree"))
            {
                throw new System.Exception("基础类名设置失败");
            }

            LogTestPass("DaisyTree 基础创建测试 - 所有基础创建测试通过");
        }

        /// <summary>
        /// 测试DaisyTree数据绑定
        /// </summary>
        private void TestDaisyTreeDataBinding()
        {
            LogTest("DaisyTree 数据绑定测试");

            // 创建测试数据
            var rootData = new DaisyTreeData("root", "根节点", "🌳");
            var nodeData1 = rootData.AddChild("node1", "城区1", "🏙️");
            var nodeData2 = rootData.AddChild("node2", "城区2", "🏘️");

            nodeData1.AddChild("item1", "测量点", "📍");
            nodeData1.AddChild("item2", "边缘点", "🔵");
            nodeData1.AddChild("item3", "边界线", "📏");
            nodeData1.AddChild("item4", "钻孔", "🕳️");

            nodeData2.AddChild("item5", "控制点", "🎯");
            nodeData2.AddChild("item6", "参考点", "📌");

            // 测试数据绑定
            var tree = DaisyTree.Create(new List<DaisyTreeData> { rootData });
            if (tree == null)
            {
                throw new System.Exception("数据绑定创建失败");
            }

            // 验证数据绑定
            if (tree.TreeData == null || !tree.TreeData.Contains(rootData))
            {
                throw new System.Exception("根数据绑定失败");
            }

            // 测试数据清除
            tree.SetData(new List<DaisyTreeData>());
            if (tree.TreeData.Count != 0)
            {
                throw new System.Exception("数据清除失败");
            }

            LogTestPass("DaisyTree 数据绑定测试 - 数据绑定测试通过");
        }

        /// <summary>
        /// 测试DaisyTree节点操作
        /// </summary>
        private void TestDaisyTreeNodeOperations()
        {
            LogTest("DaisyTree 节点操作测试");

            var tree = DaisyTree.Create("node-test");

            // 测试数据添加
            var node1 = new DaisyTreeData("node1", "节点1", "📁");
            var node2 = new DaisyTreeData("node2", "节点2", "📂");
            tree.AddItem(node1);
            tree.AddItem(node2);

            if (node1 == null || node2 == null)
            {
                throw new System.Exception("节点添加失败");
            }

            // 验证节点数量
            if (tree.TreeData.Count != 2)
            {
                throw new System.Exception("节点数量验证失败");
            }

            // 测试节点查找
            var foundNode = tree.FindItem("node1");
            if (foundNode != node1)
            {
                throw new System.Exception("节点查找失败");
            }

            // 测试子节点添加
            var childNode = node1.AddChild("child1", "子节点1", "📄");
            if (childNode == null)
            {
                throw new System.Exception("子节点添加失败");
            }

            // 测试后代节点查找
            var descendantNode = tree.FindItem("child1");
            if (descendantNode != childNode)
            {
                throw new System.Exception("后代节点查找失败");
            }

            // 测试节点移除
            tree.RemoveItem("node2");
            if (tree.TreeData.Count != 1)
            {
                throw new System.Exception("节点移除失败");
            }

            LogTestPass("DaisyTree 节点操作测试 - 节点操作测试通过");
        }

        /// <summary>
        /// 测试DaisyTree项目操作
        /// </summary>
        private void TestDaisyTreeItemOperations()
        {
            LogTest("DaisyTree 项目操作测试");

            var tree = DaisyTree.Create("item-test");

            // 测试项目添加
            var item1 = new DaisyTreeData("item1", "项目1", "📌");
            var item2 = new DaisyTreeData("item2", "项目2", "📍");
            tree.AddItem(item1);
            tree.AddItem(item2);

            if (item1 == null || item2 == null)
            {
                throw new System.Exception("项目添加失败");
            }

            // 验证项目数量
            if (tree.TreeData.Count != 2)
            {
                throw new System.Exception("项目数量验证失败");
            }

            // 测试项目查找
            var foundItem = tree.FindItem("item1");
            if (foundItem != item1)
            {
                throw new System.Exception("项目查找失败");
            }

            // 测试节点中的项目
            var node = new DaisyTreeData("node1", "节点1", "📁");
            tree.AddItem(node);
            var nodeItem = node.AddChild("nodeItem1", "节点项目1", "📄");

            if (nodeItem == null)
            {
                throw new System.Exception("节点项目添加失败");
            }

            // 测试后代项目查找
            var descendantItem = tree.FindItem("nodeItem1");
            if (descendantItem != nodeItem)
            {
                throw new System.Exception("后代项目查找失败");
            }

            // 测试项目移除
            tree.RemoveItem("item2");
            if (tree.TreeData.Count != 2)
            {
                throw new System.Exception("项目移除失败");
            }

            LogTestPass("DaisyTree 项目操作测试 - 项目操作测试通过");
        }

        /// <summary>
        /// 测试DaisyTree选择功能
        /// </summary>
        private void TestDaisyTreeSelection()
        {
            LogTest("DaisyTree 选择功能测试");

            var tree = DaisyTree.Create("selection-test");

            // 添加测试元素
            var node1 = new DaisyTreeData("node1", "节点1", "📁");
            var item1 = new DaisyTreeData("item1", "项目1", "📌");
            var item2 = new DaisyTreeData("item2", "项目2", "📍");
            tree.AddItem(node1);
            tree.AddItem(item1);
            tree.AddItem(item2);

            // 测试单选模式（默认）
            tree.SelectItem("node1");
            if (!node1.IsSelected)
            {
                throw new System.Exception("单选模式选择失败");
            }

            // 测试多选模式
            tree.SetMultiSelect(true);
            tree.SelectItem("item1");
            tree.SelectItem("item2");

            if (!node1.IsSelected || !item1.IsSelected || !item2.IsSelected)
            {
                throw new System.Exception("多选模式选择失败");
            }

            // 测试取消选择
            tree.DeselectItem("item1");
            if (item1.IsSelected)
            {
                throw new System.Exception("取消选择失败");
            }

            // 测试全选
            tree.SelectAll();
            var selectedItems = tree.GetSelectedItems();
            if (selectedItems.Count < 3)
            {
                throw new System.Exception("全选功能失败");
            }

            // 测试全部取消选择
            tree.DeselectAll();
            if (tree.GetSelectedItems().Count != 0)
            {
                throw new System.Exception("全部取消选择失败");
            }

            LogTestPass("DaisyTree 选择功能测试 - 选择功能测试通过");
        }

        /// <summary>
        /// 测试DaisyTree展开功能
        /// </summary>
        private void TestDaisyTreeExpansion()
        {
            LogTest("DaisyTree 展开功能测试");

            var tree = DaisyTree.Create("expansion-test");

            // 添加测试节点
            var node1 = new DaisyTreeData("node1", "节点1", "📁");
            var childNode = node1.AddChild("child1", "子节点1", "📄");
            var grandChildNode = childNode.AddChild("grandchild1", "孙节点1", "📄");
            tree.AddItem(node1);

            // 测试节点展开
            tree.ExpandItem("node1");
            if (!node1.IsExpanded)
            {
                throw new System.Exception("节点展开失败");
            }

            // 测试节点收起
            tree.CollapseItem("node1");
            if (node1.IsExpanded)
            {
                throw new System.Exception("节点收起失败");
            }

            // 测试节点切换
            tree.ToggleItem("node1");
            if (!node1.IsExpanded)
            {
                throw new System.Exception("节点切换失败");
            }

            // 测试全部展开
            tree.ExpandAll();
            if (!node1.IsExpanded || !childNode.IsExpanded)
            {
                throw new System.Exception("全部展开失败");
            }

            // 测试全部收起
            tree.CollapseAll();
            if (node1.IsExpanded || childNode.IsExpanded)
            {
                throw new System.Exception("全部收起失败");
            }

            LogTestPass("DaisyTree 展开功能测试 - 展开功能测试通过");
        }

        /// <summary>
        /// 测试DaisyTree搜索功能
        /// </summary>
        private void TestDaisyTreeSearch()
        {
            LogTest("DaisyTree 搜索功能测试");

            var tree = DaisyTree.Create("search-test");
            tree.SetAllowSearch(true);

            // 添加测试数据
            var node1 = new DaisyTreeData("node1", "城区1", "🏙️");
            var item1 = node1.AddChild("item1", "测量点", "📍");
            var item2 = node1.AddChild("item2", "边缘点", "🔵");
            var item3 = new DaisyTreeData("item3", "控制点", "🎯");
            tree.AddItem(node1);
            tree.AddItem(item3);

            // 测试搜索设置
            tree.Search("测量");
            if (tree.CurrentSearchQuery != "测量")
            {
                throw new System.Exception("搜索查询设置失败");
            }

            // 测试清除搜索
            tree.ClearSearch();
            if (!string.IsNullOrEmpty(tree.CurrentSearchQuery))
            {
                throw new System.Exception("清除搜索失败");
            }

            // 测试搜索占位符
            tree.SetSearchPlaceholder("请输入搜索内容...");
            if (tree.SearchPlaceholder != "请输入搜索内容...")
            {
                throw new System.Exception("搜索占位符设置失败");
            }

            LogTestPass("DaisyTree 搜索功能测试 - 搜索功能测试通过");
        }

        /// <summary>
        /// 测试DaisyTree操作按钮
        /// </summary>
        private void TestDaisyTreeActions()
        {
            LogTest("DaisyTree 操作按钮测试");

            var tree = DaisyTree.Create("actions-test");

            // 添加测试项目
            var item = new DaisyTreeData("item1", "项目1", "📌");
            tree.AddItem(item);

            // 测试添加操作按钮
            item.AddAction("edit", "编辑", "✏️");
            item.AddAction("delete", "删除", "🗑️");
            item.AddAction("view", "查看", "👁️");

            // 验证操作按钮数量
            if (item.Actions.Count != 3)
            {
                throw new System.Exception("操作按钮添加失败");
            }

            // 测试移除操作按钮
            item.RemoveAction("edit");
            if (item.Actions.Count != 2)
            {
                throw new System.Exception("操作按钮移除失败");
            }

            // 测试清除所有操作按钮
            item.ClearActions();
            if (item.Actions.Count != 0)
            {
                throw new System.Exception("清除操作按钮失败");
            }

            LogTestPass("DaisyTree 操作按钮测试 - 操作按钮测试通过");
        }

        /// <summary>
        /// 测试DaisyTree样式设置
        /// </summary>
        private void TestDaisyTreeStyling()
        {
            LogTest("DaisyTree 样式设置测试");

            var tree = DaisyTree.Create("styling-test");

            // 测试显示选项
            tree.SetShowLines(false);
            if (tree.ShowLines)
            {
                throw new System.Exception("隐藏连接线设置失败");
            }

            tree.SetShowIcons(false);
            if (tree.ShowIcons)
            {
                throw new System.Exception("隐藏图标设置失败");
            }

            tree.SetShowActions(false);
            if (tree.ShowActions)
            {
                throw new System.Exception("隐藏操作按钮设置失败");
            }

            // 测试重置设置
            tree.SetShowLines(true);
            tree.SetShowIcons(true);
            tree.SetShowActions(true);
            if (!tree.ShowLines || !tree.ShowIcons || !tree.ShowActions)
            {
                throw new System.Exception("显示选项重置失败");
            }

            LogTestPass("DaisyTree 样式设置测试 - 样式设置测试通过");
        }

        /// <summary>
        /// 测试DaisyTree事件处理
        /// </summary>
        private void TestDaisyTreeEvents()
        {
            LogTest("DaisyTree 事件处理测试");

            var tree = DaisyTree.Create("events-test");

            bool nodeClickedCalled = false;
            bool itemSelectedCalled = false;
            bool nodeExpandedCalled = false;
            bool actionTriggeredCalled = false;
            bool searchChangedCalled = false;

            // 测试事件绑定
            tree.OnItemClick((item) => { nodeClickedCalled = true; });
            tree.OnItemSelect((item) => { itemSelectedCalled = true; });
            tree.OnItemExpand((item) => { nodeExpandedCalled = true; });
            tree.OnAction((item, actionId) => { actionTriggeredCalled = true; });
            tree.OnSearch((query) => { searchChangedCalled = true; });

            // 验证事件绑定 - 通过尝试触发事件来验证
            bool eventsWorking = true;
            try
            {
                // 添加测试节点和项目
                var node = new DaisyTreeData("test-node", "测试节点", "📁");
                var item = new DaisyTreeData("test-item", "测试项目", "📌");
                tree.AddItem(node);
                tree.AddItem(item);

                // 这里我们只验证事件绑定方法没有抛出异常
                // 实际的事件触发需要用户交互或特定的内部方法调用
                if (tree == null)
                {
                    eventsWorking = false;
                }
            }
            catch (System.Exception)
            {
                eventsWorking = false;
            }

            if (!eventsWorking)
            {
                throw new System.Exception("事件绑定失败");
            }

            LogTestPass("DaisyTree 事件处理测试 - 事件处理测试通过");
        }

        /// <summary>
        /// 测试DaisyTree链式调用
        /// </summary>
        private void TestDaisyTreeChaining()
        {
            LogTest("DaisyTree 链式调用测试");

            // 测试复杂链式调用
            var tree = DaisyTree.Create("chain-test")
                .SetMultiSelect(true)
                .SetAllowSearch(true)
                .SetShowLines(true)
                .SetShowIcons(true)
                .SetShowActions(true)
                .SetSearchPlaceholder("搜索...")
                .OnItemClick((item) => { })
                .OnItemSelect((item) => { })
                .OnItemExpand((item) => { })
                .OnAction((item, actionId) => { })
                .OnSearch((query) => { });

            if (tree == null)
            {
                throw new System.Exception("链式调用失败");
            }

            // 验证链式调用结果
            if (!tree.MultiSelect || !tree.AllowSearch || !tree.ShowLines ||
                !tree.ShowIcons || !tree.ShowActions)
            {
                throw new System.Exception("链式调用属性设置失败");
            }

            LogTestPass("DaisyTree 链式调用测试 - 链式调用测试通过");
        }

        /// <summary>
        /// 测试DaisyTree复杂场景
        /// </summary>
        private void TestDaisyTreeComplexScenarios()
        {
            LogTest("DaisyTree 复杂场景测试");

            // 创建复杂树结构
            var tree = DaisyTree.Create("complex-test")
                .SetMultiSelect(true)
                .SetAllowSearch(true);

            // 构建复杂层级结构
            var region1 = new DaisyTreeData("region1", "华北地区", "🏙️");
            var region2 = new DaisyTreeData("region2", "华南地区", "🏘️");
            tree.AddItem(region1);
            tree.AddItem(region2);

            var city1 = region1.AddChild("city1", "北京市", "🏛️");
            var city2 = region1.AddChild("city2", "天津市", "🏢");
            var city3 = region2.AddChild("city3", "广州市", "🌆");
            var city4 = region2.AddChild("city4", "深圳市", "🏙️");

            // 添加测量点
            var point1 = city1.AddChild("point1", "测量点1", "📍");
            point1.AddAction("edit", "编辑", "✏️");
            point1.AddAction("delete", "删除", "🗑️");
            
            var point2 = city1.AddChild("point2", "测量点2", "📍");
            point2.AddAction("edit", "编辑", "✏️");
            point2.AddAction("view", "查看", "👁️");
            
            var point3 = city2.AddChild("point3", "测量点3", "📍");
            point3.AddAction("edit", "编辑", "✏️");
            
            var point4 = city3.AddChild("point4", "测量点4", "📍");
            point4.AddAction("delete", "删除", "🗑️");
            
            var point5 = city4.AddChild("point5", "测量点5", "📍");
            point5.AddAction("view", "查看", "👁️");

            // 添加边界线
            var line1 = city1.AddChild("line1", "边界线1", "📏");
            line1.AddAction("edit", "编辑", "✏️");
            
            var line2 = city2.AddChild("line2", "边界线2", "📏");
            line2.AddAction("view", "查看", "👁️");
            
            var line3 = city3.AddChild("line3", "边界线3", "📏");
            line3.AddAction("delete", "删除", "🗑️");

            // 添加钻孔
            var hole1 = city1.AddChild("hole1", "钻孔1", "🕳️");
            hole1.AddAction("edit", "编辑", "✏️");
            hole1.AddAction("delete", "删除", "🗑️");
            
            var hole2 = city4.AddChild("hole2", "钻孔2", "🕳️");
            hole2.AddAction("view", "查看", "👁️");

            // 验证复杂结构
            if (tree.TreeData.Count != 2)
            {
                throw new System.Exception("根节点数量不正确");
            }

            if (region1.Children.Count != 2 || region2.Children.Count != 2)
            {
                throw new System.Exception("子节点数量不正确");
            }

            if (city1.Children.Count != 4 || city2.Children.Count != 2 ||
                city3.Children.Count != 2 || city4.Children.Count != 2)
            {
                throw new System.Exception("子项目数量不正确");
            }

            // 测试复杂操作
            tree.ExpandAll();
            tree.SelectItem("city1");
            tree.SelectItem("point1");
            tree.SelectItem("line1");

            var selectedItems = tree.GetSelectedItems();
            if (selectedItems.Count != 3)
            {
                throw new System.Exception("复杂选择操作失败");
            }

            // 测试搜索功能
            tree.Search("测量");
            if (tree.CurrentSearchQuery != "测量")
            {
                throw new System.Exception("复杂搜索功能失败");
            }

            LogTestPass("DaisyTree 复杂场景测试 - 复杂场景测试通过");
        }

        #endregion
    }
}