/* DaisyUI Cards - 卡片组件样式 */

/* 基础卡片样式 */
.daisy-card {
    background-color: var(--base-100);
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease-in-out;
}

/* 卡片主体 */
.daisy-card-body {
    padding: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 卡片标题 */
.daisy-card-title {
    font-size: 20px;
    -unity-font-style: bold;
    color: var(--base-content);
    margin: 0;
    padding: 0;
}

/* 卡片内容区域 */
.daisy-card-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* 卡片文本 */
.daisy-card-text {
    color: var(--base-content);
    font-size: 14px;
    opacity: 0.8;
}

/* 卡片操作区域 */
.daisy-card-actions {
    padding: 0 24px 24px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
}

/* 卡片图片 */
.daisy-card-image {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* 卡片尺寸变体 */
.daisy-card-xs .daisy-card-body {
    padding: 12px;
}

.daisy-card-xs .daisy-card-title {
    font-size: 16px;
}

.daisy-card-xs .daisy-card-actions {
    padding: 0 12px 12px;
}

.daisy-card-sm .daisy-card-body {
    padding: 16px;
}

.daisy-card-sm .daisy-card-title {
    font-size: 18px;
}

.daisy-card-sm .daisy-card-actions {
    padding: 0 16px 16px;
}

.daisy-card-lg .daisy-card-body {
    padding: 32px;
}

.daisy-card-lg .daisy-card-title {
    font-size: 24px;
}

.daisy-card-lg .daisy-card-actions {
    padding: 0 32px 32px;
}

/* 卡片修饰符 */

/* 紧凑样式 */
.daisy-card-compact .daisy-card-body {
    padding: 16px;
}

.daisy-card-compact .daisy-card-actions {
    padding: 0 16px 16px;
}

/* 边框样式 */
.daisy-card-bordered {
    border-width: 1px;
}

/* 玻璃效果 */
.daisy-card-glass {
    background-color: rgba(255, 255, 255, 0.05);
    border-width: 1px;
}

.theme-dark .daisy-card-glass {
    background-color: rgba(0, 0, 0, 0.05);
    border-width: 1px;
}

/* 阴影效果 */
.daisy-card-shadow {
}

.daisy-card-shadow:hover {
    translate: translateY(-2px);
}

/* 侧边样式 */
.daisy-card-side {
    flex-direction: row;
    align-items: stretch;
}

.daisy-card-side .daisy-card-image {
    width: 200px;
    height: auto;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    flex-shrink: 0;
}

.daisy-card-side .daisy-card-body {
    flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .daisy-card-side {
        flex-direction: column;
    }
    
    .daisy-card-side .daisy-card-image {
        width: 100%;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    
    .daisy-card-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .daisy-card-actions > * {
        width: 100%;
    }
}

/* 卡片组合 */
.daisy-card-group {
    display: flex;
    flex-wrap: wrap;
}

.daisy-card-group .daisy-card {
    flex: 1;
    min-width: 280px;
}

/* 卡片网格 */
.daisy-card-grid {
    display: grid;
}

/* 悬停效果 */
.daisy-card:hover {
    translate: translateY(-1px);
}

.daisy-card-bordered:hover {
    border-color: var(--primary);
}

/* 焦点状态 */
.daisy-card:focus {
}

/* 禁用状态 */
.daisy-card-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 加载状态 */
.daisy-card-loading {
    position: relative;
}



/* 暗色主题适配 */
.theme-dark .daisy-card {
    background-color: var(--base-200);
}

.theme-dark .daisy-card-bordered {
    border-color: var(--base-300);
}

.theme-dark .daisy-card:hover {
}

/* 动画效果 */
.daisy-card-animate-in {
}

@keyframes cardFadeIn {
    from {
        opacity: 0;
        translate: translateY(20px);
    }
    to {
        opacity: 1;
        translate: translateY(0);
    }
}

.daisy-card-animate-out {
}

@keyframes cardFadeOut {
    from {
        opacity: 1;
        translate: translateY(0);
    }
    to {
        opacity: 0;
        translate: translateY(-20px);
    }
}
