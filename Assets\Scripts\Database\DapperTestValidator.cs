using System;
using System.IO;
using System.Linq;
using UnityEngine;
using Microsoft.Data.Sqlite;
using Dapper;
using BlastingDesign.Utils;

namespace BlastingDesign.Database
{
    /// <summary>
    /// Dapper 功能验证脚本
    /// 用于验证 Dapper 安装和基本功能是否正常
    /// </summary>
    public class DapperTestValidator : MonoBehaviour
    {
        [Header("验证配置")]
        [SerializeField] private KeyCode validateKey = KeyCode.F12;
        [SerializeField] private bool runOnStart = true;

        void Start()
        {
            if (runOnStart)
            {
                ValidateDapperInstallation();
            }
        }

        void Update()
        {
            if (Input.GetKeyDown(validateKey))
            {
                ValidateDapperInstallation();
            }
        }

        /// <summary>
        /// 验证 Dapper 安装和功能
        /// </summary>
        [ContextMenu("验证 Dapper 安装")]
        public void ValidateDapperInstallation()
        {
            Logging.LogInfo("DapperTestValidator", "=== Dapper 功能验证开始 ===");

            try
            {
                // 1. 验证基本连接
                TestBasicConnection();

                // 2. 验证基本查询
                TestBasicQuery();

                // 3. 验证参数化查询
                TestParameterizedQuery();

                // 4. 验证 CRUD 操作
                TestCrudOperations();

                // 5. 验证 DynamicParameters
                TestDynamicParameters();

                Logging.LogInfo("DapperTestValidator", "✅ 所有验证测试通过！");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperTestValidator", $"❌ 验证失败: {ex.Message}");
                Logging.LogError("DapperTestValidator", $"详细错误: {ex}");
            }

            Logging.LogInfo("DapperTestValidator", "=== Dapper 功能验证完成 ===");
        }

        /// <summary>
        /// 测试基本连接
        /// </summary>
        private void TestBasicConnection()
        {
            Logging.LogInfo("DapperTestValidator", "测试 1: 基本连接...");

            var connectionString = "Data Source=:memory:";
            using var connection = new SqliteConnection(connectionString);
            connection.Open();

            if (connection.State == System.Data.ConnectionState.Open)
            {
                Logging.LogInfo("DapperTestValidator", "✅ 基本连接测试通过");
            }
            else
            {
                throw new Exception("无法建立数据库连接");
            }
        }

        /// <summary>
        /// 测试基本查询
        /// </summary>
        private void TestBasicQuery()
        {
            Logging.LogInfo("DapperTestValidator", "测试 2: 基本查询...");

            var connectionString = "Data Source=:memory:";
            using var connection = new SqliteConnection(connectionString);
            connection.Open();

            // 使用 Dapper 执行查询
            var version = connection.QuerySingle<string>("SELECT sqlite_version()");
            
            if (!string.IsNullOrEmpty(version))
            {
                Logging.LogInfo("DapperTestValidator", $"✅ 基本查询测试通过，SQLite 版本: {version}");
            }
            else
            {
                throw new Exception("查询返回空结果");
            }
        }

        /// <summary>
        /// 测试参数化查询
        /// </summary>
        private void TestParameterizedQuery()
        {
            Logging.LogInfo("DapperTestValidator", "测试 3: 参数化查询...");

            var connectionString = "Data Source=:memory:";
            using var connection = new SqliteConnection(connectionString);
            connection.Open();

            // 创建测试表
            connection.Execute(@"
                CREATE TABLE TestUsers (
                    Id INTEGER PRIMARY KEY,
                    Name TEXT,
                    Age INTEGER
                )");

            // 插入测试数据
            var insertSql = "INSERT INTO TestUsers (Name, Age) VALUES (@Name, @Age)";
            var rowsAffected = connection.Execute(insertSql, new { Name = "测试用户", Age = 25 });

            if (rowsAffected == 1)
            {
                // 查询数据
                var user = connection.QuerySingle("SELECT Name, Age FROM TestUsers WHERE Name = @Name", new { Name = "测试用户" });
                
                if (user != null)
                {
                    Logging.LogInfo("DapperTestValidator", $"✅ 参数化查询测试通过，用户: {user.Name}, 年龄: {user.Age}");
                }
                else
                {
                    throw new Exception("参数化查询返回空结果");
                }
            }
            else
            {
                throw new Exception("参数化插入失败");
            }
        }

        /// <summary>
        /// 测试 CRUD 操作
        /// </summary>
        private void TestCrudOperations()
        {
            Logging.LogInfo("DapperTestValidator", "测试 4: CRUD 操作...");

            var connectionString = "Data Source=:memory:";
            using var connection = new SqliteConnection(connectionString);
            connection.Open();

            // 创建测试表
            connection.Execute(@"
                CREATE TABLE CrudTest (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT,
                    Value INTEGER
                )");

            // Create - 插入
            var insertCount = connection.Execute(
                "INSERT INTO CrudTest (Name, Value) VALUES (@Name, @Value)",
                new[] {
                    new { Name = "项目1", Value = 100 },
                    new { Name = "项目2", Value = 200 },
                    new { Name = "项目3", Value = 300 }
                });

            // Read - 查询
            var items = connection.Query("SELECT * FROM CrudTest ORDER BY Id").ToList();

            // Update - 更新
            var updateCount = connection.Execute(
                "UPDATE CrudTest SET Value = @Value WHERE Name = @Name",
                new { Value = 150, Name = "项目1" });

            // Delete - 删除
            var deleteCount = connection.Execute(
                "DELETE FROM CrudTest WHERE Name = @Name",
                new { Name = "项目3" });

            // 验证结果
            var finalCount = connection.QuerySingle<int>("SELECT COUNT(*) FROM CrudTest");

            if (insertCount == 3 && items.Count == 3 && updateCount == 1 && deleteCount == 1 && finalCount == 2)
            {
                Logging.LogInfo("DapperTestValidator", "✅ CRUD 操作测试通过");
            }
            else
            {
                throw new Exception($"CRUD 操作结果不符合预期: Insert={insertCount}, Read={items.Count}, Update={updateCount}, Delete={deleteCount}, Final={finalCount}");
            }
        }

        /// <summary>
        /// 测试 DynamicParameters
        /// </summary>
        private void TestDynamicParameters()
        {
            Logging.LogInfo("DapperTestValidator", "测试 5: DynamicParameters...");

            var connectionString = "Data Source=:memory:";
            using var connection = new SqliteConnection(connectionString);
            connection.Open();

            // 创建测试表
            connection.Execute(@"
                CREATE TABLE DynamicTest (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT,
                    Age INTEGER
                )");

            // 使用 DynamicParameters
            var parameters = new DynamicParameters();
            parameters.Add("@Name", "动态用户");
            parameters.Add("@Age", 30);

            var insertSql = "INSERT INTO DynamicTest (Name, Age) VALUES (@Name, @Age)";
            var rowsAffected = connection.Execute(insertSql, parameters);

            if (rowsAffected == 1)
            {
                // 查询验证
                var result = connection.QuerySingle("SELECT Name, Age FROM DynamicTest WHERE Name = @Name", new { Name = "动态用户" });
                
                if (result != null)
                {
                    Logging.LogInfo("DapperTestValidator", $"✅ DynamicParameters 测试通过，用户: {result.Name}, 年龄: {result.Age}");
                }
                else
                {
                    throw new Exception("DynamicParameters 查询返回空结果");
                }
            }
            else
            {
                throw new Exception("DynamicParameters 插入失败");
            }
        }

        /// <summary>
        /// 检查依赖项
        /// </summary>
        [ContextMenu("检查依赖项")]
        public void CheckDependencies()
        {
            Logging.LogInfo("DapperTestValidator", "=== 依赖项检查 ===");

            // 检查 Microsoft.Data.Sqlite
            try
            {
                var sqliteType = typeof(SqliteConnection);
                Logging.LogInfo("DapperTestValidator", $"✅ Microsoft.Data.Sqlite: {sqliteType.Assembly.GetName().Version}");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperTestValidator", $"❌ Microsoft.Data.Sqlite: {ex.Message}");
            }

            // 检查 Dapper
            try
            {
                var dapperType = typeof(SqlMapper);
                Logging.LogInfo("DapperTestValidator", $"✅ Dapper: {dapperType.Assembly.GetName().Version}");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperTestValidator", $"❌ Dapper: {ex.Message}");
            }

            // 检查 SQLitePCL
            try
            {
                var pclType = System.Type.GetType("SQLitePCL.raw, SQLitePCLRaw.core");
                if (pclType != null)
                {
                    Logging.LogInfo("DapperTestValidator", $"✅ SQLitePCL.raw: {pclType.Assembly.GetName().Version}");
                }
                else
                {
                    Logging.LogWarning("DapperTestValidator", "⚠ SQLitePCL.raw: 类型未找到");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperTestValidator", $"❌ SQLitePCL.raw: {ex.Message}");
            }

            Logging.LogInfo("DapperTestValidator", "依赖项检查完成");
        }

        void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 540, 400, 100));
            GUILayout.BeginVertical("box");

            GUILayout.Label("Dapper 验证工具", GUI.skin.label);

            GUILayout.BeginHorizontal();
            if (GUILayout.Button($"验证功能 ({validateKey})"))
            {
                ValidateDapperInstallation();
            }
            if (GUILayout.Button("检查依赖"))
            {
                CheckDependencies();
            }
            GUILayout.EndHorizontal();

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
    }
}
