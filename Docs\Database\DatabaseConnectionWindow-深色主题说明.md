# DatabaseConnectionWindow 深色主题配色说明

## 主题转换概述

我已经将 DatabaseConnectionWindow 从浅色主题转换为深色主题，使用 Utility.uss 中的 slate 色系作为主要配色方案，创造了一个现代、专业的深色界面。

## 深色主题配色方案

### 🎨 **主要配色**

#### 背景色系
- **主背景**: `bg-slate-900` (#0f172a) - 深色主背景
- **卡片背景**: `bg-slate-800` (#1e293b) - 连接信息卡片
- **次级背景**: `bg-slate-700` (#334155) - 高级设置区域
- **输入框背景**: `bg-slate-700` (#334155) - 输入框背景

#### 文本色系
- **主标题**: `text-slate-100` (#f1f5f9) - 高对比度白色文本
- **副标题**: `text-slate-400` (#94a3b8) - 中等对比度灰色
- **标签文本**: `text-slate-300` (#cbd5e1) - 表单标签
- **分组标题**: `text-slate-200` (#e2e8f0) - 分组标题

#### 边框色系
- **主边框**: `border-slate-600` (#475569) - 卡片边框
- **分隔线**: `border-slate-700` (#334155) - 分隔线
- **输入框边框**: `border-slate-600` (#475569) - 默认状态
- **聚焦边框**: `border-blue-400` (#60a5fa) - 输入框聚焦状态

#### 按钮色系
- **主按钮**: `bg-blue-600` (#2563eb) - 测试连接按钮
- **次按钮**: `bg-slate-600` (#475569) - 取消按钮
- **按钮文本**: `text-white` / `text-slate-200` - 按钮文字

## 详细配色对比

### 浅色主题 → 深色主题

| 元素 | 浅色主题 | 深色主题 | 说明 |
|------|----------|----------|------|
| 主背景 | `bg-slate-50` | `bg-slate-900` | 从浅灰变为深色 |
| 卡片背景 | `bg-white` | `bg-slate-800` | 从白色变为深灰 |
| 主标题 | `text-slate-900` | `text-slate-100` | 文本颜色反转 |
| 副标题 | `text-slate-600` | `text-slate-400` | 保持中等对比度 |
| 标签文本 | `text-slate-700` | `text-slate-300` | 适中的对比度 |
| 输入框 | `bg-white` | `bg-slate-700` | 深色输入框背景 |
| 边框 | `border-slate-200/300` | `border-slate-600/700` | 更深的边框色 |
| 状态容器 | `bg-blue-50` | `bg-slate-800` | 统一深色背景 |

## 组件配色详解

### 1. 头部区域 (Header)
```xml
<ui:VisualElement class="mb-6 pb-4 border-b-slate-700">
    <ui:Label text="数据库连接配置" class="text-slate-100 text-lg font-bold mb-1" />
    <ui:Label text="配置并测试数据库连接参数" class="text-slate-400 text-sm" />
</ui:VisualElement>
```

**配色特点**:
- 底部分隔线使用 `border-b-slate-700`
- 主标题使用高对比度的 `text-slate-100`
- 副标题使用中等对比度的 `text-slate-400`

### 2. 连接信息卡片
```xml
<ui:VisualElement class="bg-slate-800 border-slate-600 rounded p-4 mb-5">
    <ui:Label text="连接信息" class="text-slate-200 text-sm font-bold mb-3" />
    <!-- 输入字段 -->
</ui:VisualElement>
```

**配色特点**:
- 卡片背景使用 `bg-slate-800`
- 边框使用 `border-slate-600`
- 分组标题使用 `text-slate-200`

### 3. 输入框样式
```css
.field-input-enhanced-dark {
    background-color: var(--slate-700);
    border-color: var(--slate-600);
    color: var(--slate-100);
}

.field-input-enhanced-dark:focus {
    border-color: var(--blue-400);
}

.field-input-enhanced-dark:hover {
    border-color: var(--slate-500);
}
```

**交互状态**:
- **默认**: 深灰背景，中等边框
- **悬停**: 边框变亮 (`slate-500`)
- **聚焦**: 蓝色边框 (`blue-400`)

### 4. 高级设置区域
```xml
<ui:VisualElement class="bg-slate-700 border-slate-600 rounded p-4 mb-5">
    <ui:Label text="高级设置" class="text-slate-200 text-sm font-bold mb-3" />
    <!-- 开关控件 -->
</ui:VisualElement>
```

**配色特点**:
- 使用比连接信息稍深的 `bg-slate-700`
- 保持统一的边框色 `border-slate-600`
- 标签文本使用 `text-slate-300`

### 5. 状态指示器
```xml
<ui:VisualElement class="bg-slate-800 border-slate-600 rounded p-3 mb-4">
    <ui:VisualElement class="bg-slate-500 rounded-full" />
    <ui:Label class="text-slate-300" />
    <ui:VisualElement class="bg-blue-400 rounded-full" />
</ui:VisualElement>
```

**配色特点**:
- 状态容器使用 `bg-slate-800`
- 状态图标使用 `bg-slate-500`
- 加载指示器使用 `bg-blue-400`

### 6. 按钮组
```xml
<ui:Button class="bg-blue-600 text-white border-blue-600" />
<ui:Button class="bg-slate-600 text-slate-200 border-slate-500" />
```

**按钮配色**:
- **主按钮**: 蓝色主题 (`bg-blue-600`)
- **次按钮**: 灰色主题 (`bg-slate-600`)
- **悬停效果**: 颜色变化提供视觉反馈

## 可访问性考虑

### 1. 对比度标准
- **主文本**: `text-slate-100` 在 `bg-slate-900` 上，对比度 > 7:1
- **副文本**: `text-slate-400` 在 `bg-slate-900` 上，对比度 > 4.5:1
- **标签文本**: `text-slate-300` 在 `bg-slate-800` 上，对比度 > 4.5:1

### 2. 交互反馈
- 输入框聚焦时使用蓝色边框，提供清晰的视觉反馈
- 按钮悬停状态有明显的颜色变化
- 状态指示器使用不同颜色表示不同状态

### 3. 色彩层次
- 使用 slate 色系的不同深度创建清晰的视觉层次
- 从 `slate-900` (最深) 到 `slate-100` (最浅) 的渐进式设计

## 技术实现

### 1. 新增样式类
- `.field-input-enhanced-dark` - 深色主题输入框
- 新增深色背景色类 (`bg-slate-600` 到 `bg-slate-900`)
- 新增深色文本色类 (`text-slate-100` 到 `text-slate-500`)
- 新增深色边框色类 (`border-slate-500` 到 `border-slate-700`)

### 2. 按钮交互增强
```css
ui:Button:hover.bg-blue-600 {
    background-color: var(--blue-500);
}

ui:Button:hover.bg-slate-600 {
    background-color: var(--slate-500);
}
```

### 3. 输入框内部元素优化
```css
.field-input-enhanced-dark > #unity-text-input {
    background-color: transparent;
    color: var(--slate-100);
}
```

## 主题一致性

### 1. 色彩系统
- 主要使用 slate 色系 (50-950)
- 强调色使用 blue 色系 (400-700)
- 保持与 Utility.uss 的完全兼容

### 2. 设计原则
- **层次分明**: 使用不同深度的 slate 色创建层次
- **对比适中**: 确保文本可读性
- **交互友好**: 提供清晰的交互反馈
- **专业感**: 深色主题营造专业的开发环境氛围

### 3. 扩展性
- 所有颜色都使用 CSS 变量，易于主题切换
- 模块化的样式类，可复用于其他组件
- 为未来的主题系统做好准备

## 使用建议

### 1. 主题切换
如果需要在浅色和深色主题之间切换，可以通过替换样式类实现：

```csharp
// 切换到深色主题
element.RemoveFromClassList("field-input-enhanced");
element.AddToClassList("field-input-enhanced-dark");

// 切换背景色
container.RemoveFromClassList("bg-slate-50");
container.AddToClassList("bg-slate-900");
```

### 2. 状态管理
深色主题下的状态指示器颜色：

```csharp
// 成功状态
statusIcon.RemoveFromClassList("bg-slate-500");
statusIcon.AddToClassList("bg-green-400");

// 错误状态
statusIcon.AddToClassList("bg-red-400");

// 警告状态
statusIcon.AddToClassList("bg-yellow-400");
```

### 3. 自定义扩展
可以基于现有的深色主题创建变体：

```css
/* 更深的深色主题 */
.ultra-dark {
    background-color: var(--slate-950);
}

/* 蓝色强调的深色主题 */
.blue-dark {
    background-color: var(--blue-900);
}
```

这个深色主题不仅提供了现代化的视觉体验，还保持了良好的可用性和可访问性，为用户创造了专业、舒适的使用环境。
