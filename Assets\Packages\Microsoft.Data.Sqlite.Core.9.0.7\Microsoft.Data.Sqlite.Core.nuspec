﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="3.6">
    <id>Microsoft.Data.Sqlite.Core</id>
    <version>9.0.7</version>
    <authors>Microsoft</authors>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <readme>PACKAGE.md</readme>
    <projectUrl>https://docs.microsoft.com/dotnet/standard/data/sqlite/</projectUrl>
    <description>Microsoft.Data.Sqlite is a lightweight ADO.NET provider for SQLite. This package does not include a copy of the native SQLite library.

Commonly Used Types:
Microsoft.Data.Sqlite.SqliteCommand
Microsoft.Data.Sqlite.SqliteConnection
Microsoft.Data.Sqlite.SqliteConnectionStringBuilder
Microsoft.Data.Sqlite.SqliteDataReader
Microsoft.Data.Sqlite.SqliteException
Microsoft.Data.Sqlite.SqliteFactory
Microsoft.Data.Sqlite.SqliteParameter
Microsoft.Data.Sqlite.SqliteTransaction</description>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <tags>SQLite Data ADO.NET</tags>
    <serviceable>true</serviceable>
    <repository type="git" url="https://github.com/dotnet/efcore" commit="67d253c17619e6ba325e5390905ea2a13cc7f532" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="SQLitePCLRaw.core" version="2.1.10" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="SQLitePCLRaw.core" version="2.1.10" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="SQLitePCLRaw.core" version="2.1.10" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>