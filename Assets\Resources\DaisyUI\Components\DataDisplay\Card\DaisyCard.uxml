<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <ui:Template name="DaisyCard" src="project://database/Assets/Resources/DaisyUI/Components/DataDisplay/Card/DaisyCard.uxml">
        <Style src="project://database/Assets/Resources/DaisyUI/Components/DataDisplay/Card/DaisyCard.uss" />
        <ui:VisualElement name="daisy-card" class="daisy-card">
            <ui:VisualElement name="card-body" class="card-body">
                <ui:Label text="Card Title" name="card-title" class="card-title" />
                <ui:Label text="Card content goes here..." name="card-content" class="card-content" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:Template>
</ui:UXML>