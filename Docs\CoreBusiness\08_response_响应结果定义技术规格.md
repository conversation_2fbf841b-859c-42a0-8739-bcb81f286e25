# src/response/ 响应结果定义模块技术规格文档

## 模块概述

响应结果定义模块是系统的数据输出层组件，负责定义各种业务操作的返回结果结构。该模块采用数据类(dataclass)模式，提供类型安全的响应定义，支持API接口返回、业务服务结果封装和数据序列化，确保系统输出数据的标准化和一致性。

## 文件清单及功能

### 核心响应类文件

| 文件名 | 功能描述 | 业务场景 | 主要用途 |
|--------|----------|----------|----------|
| `hole_Info_response.py` | 钻孔信息响应 | 钻孔数据返回 | 钻孔查询结果、布孔算法输出 |
| `punch_response_entity.py` | 布孔响应实体 | 自动布孔结果 | 布孔算法结果封装、批量钻孔数据 |

## 核心数据结构

### 1. 钻孔信息响应 (HoleInfoResponse)

```python
@dataclass
class HoleInfoResponse:
    """钻孔信息响应类"""
    
    # 基础标识
    HoleID: str              # 钻孔唯一标识符
    HoleName: str            # 钻孔名称
    
    # 地理坐标
    lng: float               # 经度 (WGS84坐标系)
    lat: float               # 纬度 (WGS84坐标系)
    alt: float               # 海拔高度 (米)
    
    # 钻孔物理参数
    aperture: float          # 孔径 (毫米)
    hole_depth: float        # 孔深 (米)
    step_elevation_z: float  # 台阶海拔 (米)
    hole_bottom_z: float     # 孔底海拔 (米)
    dril_hole_deep: float    # 钻孔超深 (米)
    angle: float             # 钻孔角度 (度)
    hole_length: float       # 孔长 (米)
```

**字段详解**:

#### 标识字段
- `HoleID`: 钻孔的全局唯一标识符，用于数据库关联和引用
- `HoleName`: 钻孔的显示名称，用于用户界面展示和报表生成

#### 位置字段
- `lng/lat/alt`: 钻孔口的地理坐标，使用WGS84坐标系
- 坐标精度满足工程测量要求，支持厘米级定位

#### 工程参数字段
- `aperture`: 钻孔直径，影响爆破效果和成本计算
- `hole_depth`: 从地表到设计孔底的垂直深度
- `step_elevation_z`: 爆破台阶的顶部标高
- `hole_bottom_z`: 钻孔底部的绝对海拔高度
- `dril_hole_deep`: 超出设计深度的额外钻进距离
- `angle`: 钻孔与垂直方向的夹角，0度为垂直
- `hole_length`: 考虑角度后的实际钻进长度

### 2. 布孔响应实体 (PunchResponseEntity)

```python
@dataclass
class PunchResponseEntity:
    """自动布孔算法响应实体类"""
    
    # 基础标识
    project_id: str                    # 项目ID
    blast_area_id: str                 # 爆破区域ID
    
    # 钻孔结果集合
    hole_list: List[HoleInfoResponse]  # 生成的钻孔列表
```

**字段详解**:

#### 关联标识
- `project_id`: 项目唯一标识，确保结果归属正确项目
- `blast_area_id`: 爆破区域标识，指定具体的爆破作业区域

#### 结果数据
- `hole_list`: 布孔算法生成的所有钻孔信息列表
- 列表中每个元素都是完整的钻孔信息响应对象
- 支持空列表，表示布孔算法未生成任何钻孔

## 接口规范

### HoleInfoResponse接口

#### 数据验证接口
```python
def validate(self) -> Tuple[bool, str]
    """
    验证钻孔信息响应的有效性
    
    返回:
        (是否有效, 错误信息)
        
    验证内容:
        - 坐标范围检查
        - 工程参数合理性
        - 必填字段完整性
        - 数值逻辑一致性
    """
```

#### 序列化接口
```python
def to_dict(self) -> Dict[str, Any]
    """
    将钻孔响应转换为字典
    
    返回:
        包含所有字段的字典
        
    用途:
        - JSON序列化
        - 数据库存储
        - API接口返回
    """

def to_json(self) -> str
    """
    将钻孔响应转换为JSON字符串
    
    返回:
        JSON格式的字符串
    """
```

#### 工程计算接口
```python
def calculate_volume(self) -> float
    """
    计算钻孔体积
    
    返回:
        钻孔体积 (立方米)
        
    计算公式:
        体积 = π × (孔径/2)² × 孔长
    """

def calculate_drilling_cost(self, unit_cost: float) -> float
    """
    计算钻孔成本
    
    参数:
        unit_cost: 单位长度钻孔成本
        
    返回:
        钻孔总成本
    """
```

### PunchResponseEntity接口

#### 统计分析接口
```python
def get_hole_count(self) -> int
    """
    获取钻孔总数
    
    返回:
        钻孔数量
    """

def get_total_length(self) -> float
    """
    计算总钻孔长度
    
    返回:
        所有钻孔长度之和 (米)
    """

def get_total_volume(self) -> float
    """
    计算总钻孔体积
    
    返回:
        所有钻孔体积之和 (立方米)
    """

def get_statistics(self) -> Dict[str, Any]
    """
    获取布孔统计信息
    
    返回:
        统计信息字典，包含:
        - 钻孔总数
        - 总长度
        - 总体积
        - 平均孔深
        - 孔径分布
        - 角度分布
    """
```

#### 数据过滤接口
```python
def filter_by_depth_range(self, min_depth: float, max_depth: float) -> List[HoleInfoResponse]
    """
    按孔深范围过滤钻孔
    
    参数:
        min_depth: 最小孔深
        max_depth: 最大孔深
        
    返回:
        符合条件的钻孔列表
    """

def filter_by_area(self, bounds: Tuple[float, float, float, float]) -> List[HoleInfoResponse]
    """
    按地理区域过滤钻孔
    
    参数:
        bounds: 边界框 (min_lng, min_lat, max_lng, max_lat)
        
    返回:
        区域内的钻孔列表
    """
```

#### 数据转换接口
```python
def to_entity_list(self) -> List[HoleInfoEntity]
    """
    转换为实体对象列表
    
    返回:
        HoleInfoEntity对象列表
        
    用途:
        - 数据库存储
        - 业务逻辑处理
        - 3D场景渲染
    """

def to_csv_data(self) -> List[List[str]]
    """
    转换为CSV格式数据
    
    返回:
        CSV行数据列表
        
    用途:
        - 数据导出
        - 报表生成
        - 第三方系统集成
    """
```

## 业务规则

### 钻孔信息响应规则
- **坐标精度**: 经纬度保留6位小数，海拔保留2位小数
- **参数单位**: 严格按照字段注释中的单位标准
- **数值范围**: 所有物理参数必须在合理工程范围内
- **逻辑一致性**: 孔底海拔不能高于台阶海拔

### 布孔响应实体规则
- **关联完整性**: 项目ID和爆区ID必须有效
- **数据一致性**: 所有钻孔的项目ID应与响应实体一致
- **排序规则**: 钻孔列表按行列索引排序
- **空结果处理**: 支持空钻孔列表的有效响应

### 数据质量规则
- **必填字段**: 所有字段都不能为None或空值
- **数值有效性**: 数值字段不能为NaN或无穷大
- **字符串规范**: 字符串字段去除前后空格
- **编码标准**: 所有文本使用UTF-8编码

### 性能优化规则
- **批量处理**: 大量钻孔数据采用批量处理
- **内存管理**: 及时释放大型响应对象
- **序列化优化**: 使用高效的序列化方法
- **缓存策略**: 重复查询结果进行缓存

## 关键约束条件

### 数据类型约束
```python
# 坐标精度约束
COORDINATE_PRECISION = 6      # 经纬度小数位数
ELEVATION_PRECISION = 2       # 海拔小数位数

# 工程参数约束
MIN_APERTURE = 50.0          # 最小孔径 (mm)
MAX_APERTURE = 500.0         # 最大孔径 (mm)
MIN_HOLE_DEPTH = 0.5         # 最小孔深 (m)
MAX_HOLE_DEPTH = 50.0        # 最大孔深 (m)
MIN_ANGLE = 0.0              # 最小角度 (度)
MAX_ANGLE = 90.0             # 最大角度 (度)
```

### 业务逻辑约束
- 孔底海拔 = 台阶海拔 - 孔深 - 超深
- 孔长 = 孔深 / cos(角度)
- 超深不能超过孔深的30%
- 钻孔角度应考虑地质稳定性

### 数据完整性约束
- 钻孔ID必须全局唯一
- 地理坐标必须在项目范围内
- 工程参数必须符合设计标准
- 关联ID必须在数据库中存在

### 性能约束
- 单次响应钻孔数量不超过10,000个
- 响应数据大小不超过50MB
- 序列化时间不超过5秒
- 内存使用不超过500MB

## 扩展点设计

### 新响应类型扩展
```python
@dataclass
class NewResponse:
    """新响应类型"""
    # 基础字段
    id: str
    name: str
    # 特有字段
    specific_data: Any
    
    def validate(self) -> Tuple[bool, str]:
        """参数验证"""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化"""
        return asdict(self)
```

### 统计功能扩展
```python
class ResponseAnalyzer:
    """响应数据分析器"""
    
    @staticmethod
    def analyze_distribution(holes: List[HoleInfoResponse]) -> Dict[str, Any]
        """分析钻孔分布"""
        pass
    
    @staticmethod
    def generate_report(response: PunchResponseEntity) -> str
        """生成分析报告"""
        pass
```

### 导出功能扩展
```python
class ResponseExporter:
    """响应数据导出器"""
    
    @staticmethod
    def to_excel(response: PunchResponseEntity, filename: str) -> bool
        """导出到Excel"""
        pass
    
    @staticmethod
    def to_cad_format(holes: List[HoleInfoResponse]) -> str
        """导出CAD格式"""
        pass
```

## 使用模式

### 基本使用模式
```python
# 创建钻孔响应
hole_response = HoleInfoResponse(
    HoleID="H001",
    HoleName="钻孔1",
    lng=116.404,
    lat=39.915,
    alt=50.0,
    aperture=150.0,
    hole_depth=12.0,
    step_elevation_z=60.0,
    hole_bottom_z=48.0,
    dril_hole_deep=0.5,
    angle=0.0,
    hole_length=12.0
)

# 验证和使用
is_valid, error = hole_response.validate()
if is_valid:
    data = hole_response.to_dict()
```

### 布孔响应处理模式
```python
# 创建布孔响应
punch_response = PunchResponseEntity(
    project_id="P001",
    blast_area_id="BA001",
    hole_list=[hole_response1, hole_response2, ...]
)

# 统计分析
stats = punch_response.get_statistics()
total_length = punch_response.get_total_length()

# 数据过滤
deep_holes = punch_response.filter_by_depth_range(10.0, 20.0)
```

### 批量处理模式
```python
# 批量创建钻孔响应
holes = []
for hole_data in hole_data_list:
    hole = HoleInfoResponse(**hole_data)
    if hole.validate()[0]:
        holes.append(hole)

# 批量转换
entities = [hole.to_entity() for hole in holes]
csv_data = [hole.to_csv_row() for hole in holes]
```

## 质量保证

### 数据验证
- 完整的字段验证机制
- 业务规则检查
- 数据类型安全保证
- 边界条件处理

### 性能优化
- 高效的序列化方法
- 内存使用优化
- 批量处理支持
- 缓存机制集成

### 错误处理
- 详细的错误信息
- 异常情况处理
- 数据恢复机制
- 日志记录完整

### 可维护性
- 清晰的字段定义
- 统一的命名规范
- 完整的文档注释
- 标准化的接口设计
