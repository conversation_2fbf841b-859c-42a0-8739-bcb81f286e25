namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyUI输入框组件 - 静态工厂方法部分
    /// 包含各种输入类型的静态创建方法
    /// </summary>
    public partial class DaisyInput
    {
        #region 静态工厂方法

        /// <summary>
        /// 创建输入框组件
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>DaisyInput实例</returns>
        public static DaisyInput Create(string placeholder = "")
        {
            return new DaisyInput(placeholder);
        }

        /// <summary>
        /// 创建带标签的输入框
        /// </summary>
        /// <param name="label">标签文本</param>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>DaisyInput实例</returns>
        public static DaisyInput Create(string label, string placeholder)
        {
            var input = new DaisyInput(placeholder);
            input.SetLabel(label);
            return input;
        }

        /// <summary>
        /// 创建文本输入框
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>DaisyInput实例</returns>
        public static DaisyInput Text(string placeholder = "")
        {
            return new DaisyInput(placeholder).SetType("text");
        }

        /// <summary>
        /// 创建密码输入框
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>DaisyInput实例</returns>
        public static DaisyInput Password(string placeholder = "")
        {
            return new DaisyInput(placeholder).SetType("password");
        }

        /// <summary>
        /// 创建邮箱输入框
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>DaisyInput实例</returns>
        public static DaisyInput Email(string placeholder = "")
        {
            return new DaisyInput(placeholder).SetType("email");
        }

        /// <summary>
        /// 创建数字输入框
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>DaisyInput实例</returns>
        public static DaisyInput Number(string placeholder = "")
        {
            return new DaisyInput(placeholder).SetType("number");
        }

        /// <summary>
        /// 创建搜索输入框
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>DaisyInput实例</returns>
        public static DaisyInput Search(string placeholder = "")
        {
            return new DaisyInput(placeholder).SetType("search");
        }

        #endregion
    }
}