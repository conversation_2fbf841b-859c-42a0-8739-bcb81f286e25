<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="#f8fafc"/>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1a202c">
    Unity 3D Blasting Design System Architecture
  </text>
  
  <!-- Core Layer -->
  <g id="core-layer">
    <rect x="50" y="80" width="1100" height="120" fill="#e2e8f0" stroke="#cbd5e0" stroke-width="2" rx="10" filter="url(#shadow)"/>
    <text x="70" y="105" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">Core Systems Layer</text>
    
    <!-- Main Controller -->
    <rect x="80" y="120" width="150" height="60" fill="#3182ce" stroke="#2b6cb0" stroke-width="2" rx="5"/>
    <text x="155" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Main Controller</text>
    <text x="155" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Scene Setup</text>
    
    <!-- Import Manager -->
    <rect x="250" y="120" width="150" height="60" fill="#3182ce" stroke="#2b6cb0" stroke-width="2" rx="5"/>
    <text x="325" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Import Manager</text>
    <text x="325" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Cesium 3D Tilesets</text>
    
    <!-- Grid Plane -->
    <rect x="420" y="120" width="150" height="60" fill="#3182ce" stroke="#2b6cb0" stroke-width="2" rx="5"/>
    <text x="495" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Grid Plane</text>
    <text x="495" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Infinite Grid</text>
    
    <!-- Material Manager -->
    <rect x="590" y="120" width="150" height="60" fill="#3182ce" stroke="#2b6cb0" stroke-width="2" rx="5"/>
    <text x="665" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Material Manager</text>
    <text x="665" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">URP Materials</text>
    
    <!-- Database Manager -->
    <rect x="760" y="120" width="150" height="60" fill="#3182ce" stroke="#2b6cb0" stroke-width="2" rx="5"/>
    <text x="835" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Database Manager</text>
    <text x="835" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">SQL Server/SQLite</text>
    
    <!-- Selection Manager -->
    <rect x="930" y="120" width="150" height="60" fill="#3182ce" stroke="#2b6cb0" stroke-width="2" rx="5"/>
    <text x="1005" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Selection Manager</text>
    <text x="1005" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Multi-Selection</text>
  </g>
  
  <!-- Input Layer -->
  <g id="input-layer">
    <rect x="50" y="230" width="550" height="180" fill="#f7fafc" stroke="#e2e8f0" stroke-width="2" rx="10" filter="url(#shadow)"/>
    <text x="70" y="255" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">Input Management Layer</text>
    
    <!-- Input Manager -->
    <rect x="80" y="275" width="200" height="80" fill="#38a169" stroke="#2f855a" stroke-width="2" rx="5"/>
    <text x="180" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Input Manager</text>
    <text x="180" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Viewport Control</text>
    <text x="180" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">First-Person Mode</text>
    <text x="180" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Selection System</text>
    
    <!-- Input Event Priority Manager -->
    <rect x="300" y="275" width="140" height="60" fill="#38a169" stroke="#2f855a" stroke-width="2" rx="5"/>
    <text x="370" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Input Event</text>
    <text x="370" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Priority Manager</text>
    <text x="370" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Conflict Resolution</text>
    
    <!-- Event System Manager -->
    <rect x="460" y="275" width="120" height="60" fill="#38a169" stroke="#2f855a" stroke-width="2" rx="5"/>
    <text x="520" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Event System</text>
    <text x="520" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">Manager</text>
    <text x="520" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Unity Integration</text>
    
    <!-- Input Flow -->
    <rect x="80" y="365" width="500" height="30" fill="#e6fffa" stroke="#38a169" stroke-width="1" rx="5"/>
    <text x="330" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#2d3748">Hardware Input → Processing → Priority Resolution → UI/Scene Components</text>
  </g>
  
  <!-- UI Layer -->
  <g id="ui-layer">
    <rect x="620" y="230" width="530" height="260" fill="#fef5e7" stroke="#f6e05e" stroke-width="2" rx="10" filter="url(#shadow)"/>
    <text x="640" y="255" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">UI Management Layer</text>
    
    <!-- UI Manager -->
    <rect x="650" y="275" width="140" height="60" fill="#d69e2e" stroke="#b7791f" stroke-width="2" rx="5"/>
    <text x="720" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">UI Manager</text>
    <text x="720" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">UI Toolkit</text>
    
    <!-- UI Event System -->
    <rect x="810" y="275" width="140" height="60" fill="#d69e2e" stroke="#b7791f" stroke-width="2" rx="5"/>
    <text x="880" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">UI Event System</text>
    <text x="880" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Inter-Component</text>
    
    <!-- DaisyUI Framework -->
    <rect x="970" y="275" width="140" height="60" fill="#d69e2e" stroke="#b7791f" stroke-width="2" rx="5"/>
    <text x="1040" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">DaisyUI</text>
    <text x="1040" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Component System</text>
    
    <!-- UI Components -->
    <rect x="650" y="355" width="80" height="40" fill="#fed7aa" stroke="#f97316" stroke-width="1" rx="3"/>
    <text x="690" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">MenuBar</text>
    
    <rect x="740" y="355" width="80" height="40" fill="#fed7aa" stroke="#f97316" stroke-width="1" rx="3"/>
    <text x="780" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">LeftPanel</text>
    
    <rect x="830" y="355" width="80" height="40" fill="#fed7aa" stroke="#f97316" stroke-width="1" rx="3"/>
    <text x="870" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">RightPanel</text>
    
    <rect x="920" y="355" width="80" height="40" fill="#fed7aa" stroke="#f97316" stroke-width="1" rx="3"/>
    <text x="960" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">StatusBar</text>
    
    <rect x="1010" y="355" width="80" height="40" fill="#fed7aa" stroke="#f97316" stroke-width="1" rx="3"/>
    <text x="1050" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">TopToolbar</text>
    
    <!-- Configuration -->
    <rect x="650" y="415" width="200" height="30" fill="#fde68a" stroke="#f59e0b" stroke-width="1" rx="5"/>
    <text x="750" y="435" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">Configuration-Driven (ScriptableObjects)</text>
    
    <!-- Styling -->
    <rect x="870" y="415" width="220" height="30" fill="#fde68a" stroke="#f59e0b" stroke-width="1" rx="5"/>
    <text x="980" y="435" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#2d3748">TailwindCSS-like Styling System</text>
  </g>
  
  <!-- Business Logic Layer (Planned) -->
  <g id="business-layer">
    <rect x="50" y="520" width="1100" height="100" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="2" rx="10" filter="url(#shadow)" stroke-dasharray="5,5"/>
    <text x="70" y="545" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">Business Logic Layer (Planned)</text>
    
    <rect x="80" y="565" width="120" height="40" fill="#e0f2fe" stroke="#0ea5e9" stroke-width="1" rx="3" stroke-dasharray="3,3"/>
    <text x="140" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Data Manager</text>
    
    <rect x="220" y="565" width="140" height="40" fill="#e0f2fe" stroke="#0ea5e9" stroke-width="1" rx="3" stroke-dasharray="3,3"/>
    <text x="290" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Project Manager</text>
    
    <rect x="380" y="565" width="160" height="40" fill="#e0f2fe" stroke="#0ea5e9" stroke-width="1" rx="3" stroke-dasharray="3,3"/>
    <text x="460" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">BlastArea Manager</text>
    
    <rect x="560" y="565" width="160" height="40" fill="#e0f2fe" stroke="#0ea5e9" stroke-width="1" rx="3" stroke-dasharray="3,3"/>
    <text x="640" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">DrillHole Manager</text>
  </g>
  
  <!-- External Systems -->
  <g id="external-systems">
    <rect x="50" y="650" width="1100" height="80" fill="#fef2f2" stroke="#ef4444" stroke-width="2" rx="10" filter="url(#shadow)"/>
    <text x="70" y="675" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#2d3748">External Systems Integration</text>
    
    <rect x="80" y="695" width="120" height="25" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="3"/>
    <text x="140" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Unity Engine</text>
    
    <rect x="220" y="695" width="120" height="25" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="3"/>
    <text x="280" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Cesium for Unity</text>
    
    <rect x="360" y="695" width="80" height="25" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="3"/>
    <text x="400" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">URP</text>
    
    <rect x="460" y="695" width="100" height="25" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="3"/>
    <text x="510" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">UI Toolkit</text>
    
    <rect x="580" y="695" width="100" height="25" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="3"/>
    <text x="630" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">SQL Server</text>
    
    <rect x="700" y="695" width="80" height="25" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="3"/>
    <text x="740" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">SQLite</text>
    
    <rect x="800" y="695" width="80" height="25" fill="#fecaca" stroke="#ef4444" stroke-width="1" rx="3"/>
    <text x="840" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Dapper</text>
  </g>
  
  <!-- Arrows showing data flow -->
  <g id="arrows">
    <!-- Input to Core -->
    <line x1="325" y1="275" x2="325" y2="200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- Core to UI -->
    <line x1="600" y1="150" x2="650" y2="150" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    <line x1="650" y1="150" x2="720" y2="275" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
    
    <!-- UI Event System connections -->
    <line x1="810" y1="305" x2="650" y2="305" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
    <line x1="950" y1="305" x2="810" y2="305" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
    
    <!-- UI to Components -->
    <line x1="720" y1="335" x2="720" y2="355" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
    <line x1="720" y1="355" x2="690" y2="355" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
    <line x1="720" y1="355" x2="750" y2="355" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
    <line x1="780" y1="355" x2="810" y2="355" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
    <line x1="870" y1="355" x2="900" y2="355" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
    <line x1="960" y1="355" x2="990" y2="355" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  </g>
  
  <!-- Legend -->
  <g id="legend">
    <rect x="50" y="750" width="400" height="40" fill="white" stroke="#cbd5e0" stroke-width="1" rx="5"/>
    <text x="60" y="765" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3748">Legend:</text>
    <rect x="60" y="770" width="15" height="10" fill="#3182ce"/>
    <text x="80" y="779" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Core Systems</text>
    <rect x="150" y="770" width="15" height="10" fill="#38a169"/>
    <text x="170" y="779" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Input Layer</text>
    <rect x="230" y="770" width="15" height="10" fill="#d69e2e"/>
    <text x="250" y="779" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">UI Layer</text>
    <rect x="300" y="770" width="15" height="10" fill="#0ea5e9" stroke-dasharray="2,2"/>
    <text x="320" y="779" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Planned</text>
    <rect x="370" y="770" width="15" height="10" fill="#ef4444"/>
    <text x="390" y="779" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">External</text>
  </g>
</svg>