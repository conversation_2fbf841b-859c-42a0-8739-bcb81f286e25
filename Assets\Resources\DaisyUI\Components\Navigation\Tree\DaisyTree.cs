using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    /// <summary>
    /// DaisyTree is a partial class implementation of a tree component following DaisyUI design principles.
    /// 
    /// This class is split into multiple partial files for better organization:
    /// - DaisyTree.Core.cs: Core properties, constructors, and initialization
    /// - DaisyTree.Children.cs: Node and item management functionality  
    /// - DaisyTree.Selection.cs: Selection state management
    /// - DaisyTree.Search.cs: Search and filtering functionality
    /// - DaisyTree.Display.cs: UI display updates
    /// - DaisyTree.Events.cs: Event handling
    /// - DaisyTree.API.cs: Public fluent API methods
    /// </summary>
    [UxmlElement]
    public partial class DaisyTree : DaisyComponent
    {
        // All implementation is distributed across partial class files in the DaisyTree folder
        // This main file serves as the entry point and documentation
    }
}