using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.DaisyUI.Components.Actions;
using BlastingDesign.UI.Extensions;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 按钮组件预览提供者
    /// </summary>
    public class ButtonPreviewProvider : BaseComponentPreviewProvider
    {
        public override string ComponentId => "buttons-item";
        public override string ComponentName => "按钮 (Button)";
        public override string ComponentDescription => "按钮组件提供各种样式和尺寸的可点击按钮，支持多种颜色主题和状态";

        public override VisualElement CreatePreview()
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 基本按钮组
            var basicSection = new VisualElement();
            basicSection.AddToClassList("preview-section");

            var basicTitle = CreatePreviewTitle("基本按钮");
            basicSection.Add(basicTitle);

            var basicButtonContainer = CreatePreviewContainer("flex", "gap-2", "flex-wrap");

            // 主要按钮
            var primaryBtn = DaisyBuilder.Button("Primary")
                .SetPrimary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("Primary 按钮被点击"));
            basicButtonContainer.Add(primaryBtn);

            // 次要按钮
            var secondaryBtn = DaisyBuilder.Button("Secondary")
                .SetSecondary()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("Secondary 按钮被点击"));
            basicButtonContainer.Add(secondaryBtn);

            // 成功按钮
            var successBtn = DaisyBuilder.Button("Success")
                .SetSuccess()
                .OnClick(() => UIEventSystem.TriggerStatusMessage("Success 按钮被点击"));
            basicButtonContainer.Add(successBtn);

            basicSection.Add(basicButtonContainer);
            container.Add(basicSection);

            // 按钮尺寸组
            var sizeSection = new VisualElement();
            sizeSection.AddToClassList("preview-section");

            var sizeTitle = CreatePreviewTitle("按钮尺寸");
            sizeSection.Add(sizeTitle);

            var sizeButtonContainer = CreatePreviewContainer("flex", "gap-2", "flex-wrap", "items-center");

            // 小按钮
            var smallBtn = DaisyBuilder.Button("Small")
                .SetPrimary()
                .SetSmall();
            sizeButtonContainer.Add(smallBtn);

            // 中等按钮
            var mediumBtn = DaisyBuilder.Button("Medium")
                .SetPrimary()
                .SetMedium();
            sizeButtonContainer.Add(mediumBtn);

            // 大按钮
            var largeBtn = DaisyBuilder.Button("Large")
                .SetPrimary()
                .SetLarge();
            sizeButtonContainer.Add(largeBtn);

            sizeSection.Add(sizeButtonContainer);
            container.Add(sizeSection);

            // 按钮状态组
            var stateSection = new VisualElement();
            stateSection.AddToClassList("preview-section");

            var stateTitle = CreatePreviewTitle("按钮状态");
            stateSection.Add(stateTitle);

            var stateButtonContainer = CreatePreviewContainer("flex", "gap-2", "flex-wrap");

            // 正常按钮
            var normalBtn = DaisyBuilder.Button("Normal")
                .SetPrimary();
            stateButtonContainer.Add(normalBtn);

            // 禁用按钮
            var disabledBtn = DaisyBuilder.Button("Disabled")
                .SetPrimary()
                .SetDisabled(true);
            stateButtonContainer.Add(disabledBtn);

            // 加载按钮
            var loadingBtn = DaisyBuilder.Button("Loading")
                .SetPrimary()
                .SetLoading(true);
            stateButtonContainer.Add(loadingBtn);

            stateSection.Add(stateButtonContainer);
            container.Add(stateSection);

            return container;
        }

        public override string GetCodeExample()
        {
            return @"// 方式一：使用DaisyBuilder（推荐）
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.Extensions;

var button = DaisyBuilder.Button(""点击我"")
    .SetPrimary()
    .OnClick(() => Debug.Log(""按钮被点击""));

// 方式二：直接创建DaisyButton
var daisyButton = DaisyButton.Create(""按钮"")
    .SetPrimary()
    .OnClick(() => Debug.Log(""点击""));

// 设置按钮样式
button.SetSecondary();  // 次要样式
button.SetSuccess();    // 成功样式
button.SetWarning();    // 警告样式
button.SetError();      // 错误样式

// 设置按钮尺寸
button.SetSmall();      // 小尺寸
button.SetMedium();     // 中等尺寸
button.SetLarge();      // 大尺寸

// 设置按钮状态
button.SetDisabled(true);   // 禁用状态
button.SetLoading(true);    // 加载状态

// 添加到容器
container.Add(button);";
        }

        public override string GetUsageInstructions()
        {
            return @"按钮组件使用说明：

1. 基本用法：
   - 使用 DaisyBuilder.Button() 创建按钮
   - 设置按钮文本和点击事件

2. 样式变体：
   - SetPrimary()：主要按钮样式
   - SetSecondary()：次要按钮样式
   - SetSuccess()：成功按钮样式
   - SetWarning()：警告按钮样式
   - SetError()：错误按钮样式

3. 尺寸选项：
   - SetSmall()：小尺寸按钮
   - SetMedium()：中等尺寸按钮（默认）
   - SetLarge()：大尺寸按钮

4. 状态控制：
   - SetDisabled(bool)：设置禁用状态
   - SetLoading(bool)：设置加载状态

5. 事件处理：
   - OnClick(Action)：设置点击事件回调

6. 最佳实践：
   - 使用语义化的按钮样式
   - 为重要操作使用Primary样式
   - 为危险操作使用Error样式
   - 合理使用按钮尺寸以适应界面布局";
        }

        public override string[] GetSupportedVariants()
        {
            return new string[]
            {
                "primary",
                "secondary",
                "success",
                "warning",
                "error",
                "ghost",
                "outline"
            };
        }

        public override VisualElement CreateVariantPreview(string variant)
        {
            var container = CreatePreviewContainer("flex", "gap-2");

            var button = DaisyBuilder.Button($"{variant.ToUpper()} Button");

            switch (variant.ToLower())
            {
                case "primary":
                    button.SetPrimary();
                    break;
                case "secondary":
                    button.SetSecondary();
                    break;
                case "success":
                    button.SetSuccess();
                    break;
                case "warning":
                    button.SetWarning();
                    break;
                case "error":
                    button.SetError();
                    break;
                case "ghost":
                    button.SetGhost();
                    break;
                case "outline":
                    button.SetOutline();
                    break;
                default:
                    return null;
            }

            container.Add(button);
            return container;
        }

        public override string GetVariantCodeExample(string variant)
        {
            return variant.ToLower() switch
            {
                "primary" => @"var button = DaisyBuilder.Button(""Primary"").SetPrimary();",
                "secondary" => @"var button = DaisyBuilder.Button(""Secondary"").SetSecondary();",
                "success" => @"var button = DaisyBuilder.Button(""Success"").SetSuccess();",
                "warning" => @"var button = DaisyBuilder.Button(""Warning"").SetWarning();",
                "error" => @"var button = DaisyBuilder.Button(""Error"").SetError();",
                "ghost" => @"var button = DaisyBuilder.Button(""Ghost"").SetGhost();",
                "outline" => @"var button = DaisyBuilder.Button(""Outline"").SetOutline();",
                _ => null
            };
        }
    }
}
