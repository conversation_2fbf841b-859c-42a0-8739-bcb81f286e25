using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// InputManager键盘移动控制 - 键盘移动和动态敏感度管理
/// </summary>
public partial class InputManager
{
    #region 键盘移动控制

    /// <summary>
    /// 处理键盘移动输入（WASD + QE）
    /// </summary>
    private void HandleKeyboardMovement()
    {
        if (mainCamera == null || moveAction == null || verticalMoveAction == null) return;

        // 获取WASD输入
        Vector2 moveInput = moveAction.ReadValue<Vector2>();

        // 获取QE输入
        float verticalInput = verticalMoveAction.ReadValue<float>();

        // 检查是否有输入
        bool hasMovement = moveInput.magnitude > 0.1f || Mathf.Abs(verticalInput) > 0.1f;
        if (!hasMovement)
        {
            isKeyboardMoving = false;
            return;
        }

        isKeyboardMoving = true;

        // 检查Shift键加速
        bool isSpeedBoosted = Keyboard.current.leftShiftKey.isPressed || Keyboard.current.rightShiftKey.isPressed;

        // 使用动态敏感度
        float dynamicKeyboardSensitivity = GetDynamicKeyboardSensitivity();
        float currentMoveSpeed = dynamicKeyboardSensitivity * (isSpeedBoosted ? keyboardSpeedMultiplier : 1f);
        float currentVerticalSpeed = verticalMoveSensitivity * (isSpeedBoosted ? keyboardSpeedMultiplier : 1f);

        // 计算移动向量（基于相机的方向）
        Vector3 forward = mainCamera.transform.forward;
        Vector3 right = mainCamera.transform.right;

        // 只使用水平分量进行水平移动
        forward.y = 0;
        right.y = 0;
        forward.Normalize();
        right.Normalize();

        Vector3 moveDirection = (forward * moveInput.y + right * moveInput.x) * currentMoveSpeed * Time.deltaTime;
        Vector3 verticalDirection = Vector3.up * verticalInput * currentVerticalSpeed * Time.deltaTime;

        // 根据是否启用新的高度适应系统选择移动方式
        if (useCameraHeightAdaptation && cameraHeightAdaptationSystem != null)
        {
            // 只应用水平移动和显式的垂直移动，让高度适应系统处理自动高度调整
            Vector3 totalMovement = moveDirection + verticalDirection;
            mainCamera.transform.position += totalMovement;

            // 同时移动旋转中心（保持相对位置）
            rotationCenterPosition += totalMovement;
            if (rotationCenter != null)
            {
                rotationCenter.position = rotationCenterPosition;
            }

            // 只有在垂直移动（QE键）时才更新距离值，水平移动（WASD）不更新距离
            if (Mathf.Abs(verticalInput) > 0.1f)
            {
                cameraHeightAdaptationSystem.ForceUpdateDistance();
                if (showDebugRaycast)
                {
                    Debug.Log($"[InputManager] QE键垂直移动，更新目标距离");
                }
            }
            // 水平移动时不调用 ForceUpdateDistance()，让系统自动保持距离
        }
        else
        {
            // 使用原有逻辑
            Vector3 totalMovement = moveDirection + verticalDirection;
            mainCamera.transform.position += totalMovement;

            // 同时移动旋转中心（保持相对位置）
            rotationCenterPosition += totalMovement;
            if (rotationCenter != null)
            {
                rotationCenter.position = rotationCenterPosition;
            }
        }
    }

    #endregion

    #region 动态敏感度计算

    /// <summary>
    /// 根据当前相机距离计算动态敏感度倍数
    /// </summary>
    /// <returns>敏感度倍数</returns>
    private float CalculateDistanceBasedSensitivityMultiplier()
    {
        if (mainCamera == null) return 1f;

        // 计算当前距离在最小和最大距离之间的比例
        float normalizedDistance = Mathf.InverseLerp(minZoomDistance, maxZoomDistance, currentDistance);

        // 线性插值：距离越远，敏感度倍数越大
        // 在最小距离时倍数为0.2，在最大距离时倍数为2.0
        float multiplier = Mathf.Lerp(0.2f, 2.0f, normalizedDistance);

        return multiplier;
    }

    /// <summary>
    /// 获取鼠标平移的动态敏感度
    /// </summary>
    /// <returns>动态敏感度</returns>
    public float GetDynamicPanSensitivity()
    {
        float baseSensitivity = panSensitivity;

        if (useDistanceBasedPanSensitivity)
        {
            float distanceMultiplier = CalculateDistanceBasedSensitivityMultiplier();
            baseSensitivity *= distanceMultiplier * panSensitivityMultiplier;
        }

        return baseSensitivity;
    }

    /// <summary>
    /// 获取键盘移动的动态敏感度
    /// </summary>
    /// <returns>动态敏感度</returns>
    public float GetDynamicKeyboardSensitivity()
    {
        float baseSensitivity = keyboardMoveSensitivity;

        if (useDistanceBasedKeyboardSensitivity)
        {
            float distanceMultiplier = CalculateDistanceBasedSensitivityMultiplier();
            baseSensitivity *= distanceMultiplier;
        }

        return baseSensitivity;
    }

    #endregion
}