using UnityEngine;

namespace BlastingDesign.Core.Camera
{
    /// <summary>
    /// 相机高度自适应系统 - 射线检测工具
    /// </summary>
    public partial class CameraHeightAdaptationSystem
    {
        #region 距离计算方法

        private Vector3 GetCurrentHitPoint()
        {
            if (useScreenCenter)
            {
                return GetScreenCenterHitPoint();
            }
            else
            {
                return GetCameraDownwardHitPoint();
            }
        }

        private Vector3 GetScreenCenterHitPoint()
        {
            // 使用相机正方向Z轴 + 可配置俯视角度的组合射线方向
            // 这样可以避免低视角时通过屏幕中心发射射线导致的焦点抖动
            Vector3 cameraForward = targetCamera.transform.forward;
            Vector3 cameraRight = targetCamera.transform.right;

            // 将角度转换为弧度
            float angleInRadians = focusDownwardAngle * Mathf.Deg2Rad;

            // 创建一个向下指定角度的方向向量
            // 保持相机的前进方向(Z轴)，但在Y轴上添加指定的俯视角度
            Vector3 downwardDirection = new Vector3(0, -Mathf.Sin(angleInRadians), Mathf.Cos(angleInRadians));

            // 将这个方向转换到相机的局部坐标系中
            Vector3 rayDirection = targetCamera.transform.TransformDirection(downwardDirection).normalized;

            Ray ray = new Ray(targetCamera.transform.position, rayDirection);
            return PerformRaycast(ray);
        }

        private Vector3 GetCameraDownwardHitPoint()
        {
            Ray ray = new Ray(targetCamera.transform.position, Vector3.down);
            return PerformRaycast(ray);
        }

        private Vector3 PerformRaycast(Ray ray)
        {
            Vector3 hitPoint = Vector3.zero;

            // 优先级1: 检测Cesium模型
            if (RaycastToCesium(ray, out hitPoint))
            {
                return hitPoint;
            }

            // 优先级2: 检测地形
            if (RaycastToTerrain(ray, out hitPoint))
            {
                return hitPoint;
            }

            // // 优先级3: 数学平面交点
            // if (RaycastToGroundPlane(ray, out hitPoint))
            // {
            //     return hitPoint;
            // }

            return Vector3.zero;
        }

        private bool RaycastToCesium(Ray ray, out Vector3 hitPoint)
        {
            hitPoint = Vector3.zero;

            if (Physics.Raycast(ray, out RaycastHit hit, maxRaycastDistance, cesiumLayerMask))
            {
                hitPoint = hit.point;

                if (showDebugInfo)
                {
                    Debug.Log($"[CameraHeightAdaptationSystem] 检测到Cesium模型: {hit.collider.name}");
                }

                return true;
            }

            return false;
        }

        private bool RaycastToTerrain(Ray ray, out Vector3 hitPoint)
        {
            hitPoint = Vector3.zero;

            if (Physics.Raycast(ray, out RaycastHit hit, maxRaycastDistance, terrainLayerMask))
            {
                hitPoint = hit.point;
                return true;
            }

            return false;
        }

        private bool RaycastToGroundPlane(Ray ray, out Vector3 hitPoint)
        {
            hitPoint = Vector3.zero;

            // 定义基面（Y = groundPlaneY的水平面）
            Vector3 planeNormal = Vector3.up;
            Vector3 planePoint = new Vector3(0, groundPlaneY, 0);

            // 计算射线与平面的交点
            float denominator = Vector3.Dot(planeNormal, ray.direction);

            // 如果射线与平面平行，没有交点
            if (Mathf.Abs(denominator) < 0.0001f)
            {
                return false;
            }

            Vector3 planeToRayOrigin = planePoint - ray.origin;
            float t = Vector3.Dot(planeToRayOrigin, planeNormal) / denominator;

            // 如果交点在射线起点后面，无效
            if (t < 0)
            {
                return false;
            }

            hitPoint = ray.origin + ray.direction * t;
            return true;
        }

        #endregion
    }
}