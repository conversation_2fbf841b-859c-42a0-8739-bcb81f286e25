using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    [Serializable]
    public class DaisyTreeData
    {
        #region Events
        public event Action<string> OnNodeExpanded;
        public event Action<string> OnNodeCollapsed;
        public event Action<string> OnItemSelected;
        public event Action<string, string> OnItemAction;
        #endregion

        #region Properties
        public string Id { get; set; }
        public string Text { get; set; }
        public string Icon { get; set; }
        public bool IsExpanded { get; set; }
        public bool IsSelected { get; set; }
        public bool IsVisible { get; set; } = true;
        public bool HasChildren => Children.Count > 0;
        public DaisyTreeData Parent { get; set; }
        public string ParentId => Parent?.Id;
        public List<DaisyTreeData> Children { get; set; } = new List<DaisyTreeData>();
        public List<DaisyTreeAction> Actions { get; set; } = new List<DaisyTreeAction>();
        public Dictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        #endregion

        #region Constructors
        public DaisyTreeData(string id, string text, string icon = null)
        {
            Id = id;
            Text = text;
            Icon = icon;
        }
        #endregion

        #region Tree Operations
        public DaisyTreeData AddChild(string id, string text, string icon = null)
        {
            var child = new DaisyTreeData(id, text, icon)
            {
                Parent = this
            };
            Children.Add(child);
            return child;
        }

        public DaisyTreeData AddChild(DaisyTreeData child)
        {
            if (child == null) return null;
            
            child.Parent = this;
            Children.Add(child);
            return child;
        }

        public void RemoveChild(string id)
        {
            var child = Children.FirstOrDefault(c => c.Id == id);
            if (child != null)
            {
                child.Parent = null;
                Children.Remove(child);
            }
        }

        public void RemoveChild(DaisyTreeData child)
        {
            if (child != null && Children.Contains(child))
            {
                child.Parent = null;
                Children.Remove(child);
            }
        }

        public DaisyTreeData FindChild(string id)
        {
            return Children.FirstOrDefault(c => c.Id == id);
        }

        public DaisyTreeData FindDescendant(string id)
        {
            foreach (var child in Children)
            {
                if (child.Id == id)
                    return child;
                
                var descendant = child.FindDescendant(id);
                if (descendant != null)
                    return descendant;
            }
            return null;
        }

        public void ClearChildren()
        {
            foreach (var child in Children)
            {
                child.Parent = null;
            }
            Children.Clear();
        }

        public IEnumerable<DaisyTreeData> GetAllDescendants()
        {
            foreach (var child in Children)
            {
                yield return child;
                foreach (var descendant in child.GetAllDescendants())
                {
                    yield return descendant;
                }
            }
        }

        public IEnumerable<DaisyTreeData> GetPath()
        {
            var path = new List<DaisyTreeData>();
            var current = this;
            while (current != null)
            {
                path.Insert(0, current);
                current = current.Parent;
            }
            return path;
        }

        public int GetDepth()
        {
            int depth = 0;
            var current = Parent;
            while (current != null)
            {
                depth++;
                current = current.Parent;
            }
            return depth;
        }

        public bool IsAncestorOf(DaisyTreeData other)
        {
            if (other == null) return false;
            
            var current = other.Parent;
            while (current != null)
            {
                if (current == this) return true;
                current = current.Parent;
            }
            return false;
        }

        public bool IsDescendantOf(DaisyTreeData other)
        {
            return other?.IsAncestorOf(this) ?? false;
        }
        #endregion

        #region State Management
        public void Expand()
        {
            if (!IsExpanded)
            {
                IsExpanded = true;
                OnNodeExpanded?.Invoke(Id);
            }
        }

        public void Collapse()
        {
            if (IsExpanded)
            {
                IsExpanded = false;
                OnNodeCollapsed?.Invoke(Id);
            }
        }

        public void Toggle()
        {
            if (IsExpanded)
                Collapse();
            else
                Expand();
        }

        public void Select()
        {
            if (!IsSelected)
            {
                IsSelected = true;
                OnItemSelected?.Invoke(Id);
            }
        }

        public void Deselect()
        {
            IsSelected = false;
        }

        public void SetVisible(bool visible)
        {
            IsVisible = visible;
        }

        public void Show()
        {
            SetVisible(true);
        }

        public void Hide()
        {
            SetVisible(false);
        }
        #endregion

        #region Actions
        public void AddAction(string actionId, string tooltip = null, string icon = null)
        {
            var action = new DaisyTreeAction(actionId, tooltip, icon);
            Actions.Add(action);
        }

        public void AddAction(DaisyTreeAction action)
        {
            if (action != null)
            {
                Actions.Add(action);
            }
        }

        public void RemoveAction(string actionId)
        {
            var action = Actions.FirstOrDefault(a => a.Id == actionId);
            if (action != null)
            {
                Actions.Remove(action);
            }
        }

        public void ClearActions()
        {
            Actions.Clear();
        }

        public void TriggerAction(string actionId)
        {
            var action = Actions.FirstOrDefault(a => a.Id == actionId);
            if (action != null)
            {
                OnItemAction?.Invoke(Id, actionId);
            }
        }
        #endregion

        #region Data Management
        public void SetData(string key, object value)
        {
            Data[key] = value;
        }

        public T GetData<T>(string key)
        {
            return Data.TryGetValue(key, out var value) && value is T ? (T)value : default(T);
        }

        public bool HasData(string key)
        {
            return Data.ContainsKey(key);
        }

        public void RemoveData(string key)
        {
            Data.Remove(key);
        }

        public void ClearData()
        {
            Data.Clear();
        }
        #endregion

        #region Utility Methods
        public override string ToString()
        {
            return $"DaisyTreeData(Id: {Id}, Text: {Text}, Children: {Children.Count})";
        }

        public DaisyTreeData Clone()
        {
            var clone = new DaisyTreeData(Id, Text, Icon)
            {
                IsExpanded = IsExpanded,
                IsSelected = IsSelected,
                IsVisible = IsVisible,
                Data = new Dictionary<string, object>(Data),
                Actions = new List<DaisyTreeAction>(Actions)
            };

            foreach (var child in Children)
            {
                clone.AddChild(child.Clone());
            }

            return clone;
        }
        #endregion
    }

    [Serializable]
    public class DaisyTreeAction
    {
        public string Id { get; set; }
        public string Text { get; set; }
        public string Tooltip { get; set; }
        public string Icon { get; set; }
        public bool IsEnabled { get; set; } = true;
        public bool IsVisible { get; set; } = true;

        public DaisyTreeAction(string id, string text = null, string icon = null)
        {
            Id = id;
            Text = text ?? id;
            Tooltip = text ?? id; // Use text as tooltip if not specified separately
            Icon = icon;
        }

        public override string ToString()
        {
            return $"DaisyTreeAction(Id: {Id}, Icon: {Icon})";
        }
    }
}