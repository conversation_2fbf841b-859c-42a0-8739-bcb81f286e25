using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;
using System;

namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyUI输入框组件 - 核心功能部分
    /// 包含基础属性、构造函数和核心初始化逻辑
    /// </summary>
    public partial class DaisyInput
    {
        #region 私有字段

        private const string TEMPLATE_PATH = "DaisyUI/Components/DataInput/Input/DaisyInput";
        
        private TextField _textField;
        private Label _label;
        private Label _helperText;
        private Label _placeholderLabel;
        private VisualElement _inputContainer;
        private string _inputType = "text";
        private string _placeholder = "";
        private bool _isDisabled = false;
        private bool _isReadOnly = false;
        private string _currentSize = "md";
        private string _currentState = "normal";

        #endregion

        #region 属性

        /// <summary>
        /// 输入框的值
        /// </summary>
        [UxmlAttribute]
        public string Value
        {
            get => _textField?.value ?? string.Empty;
            set
            {
                if (_textField != null)
                {
                    _textField.value = value ?? string.Empty;
                }
            }
        }

        /// <summary>
        /// 输入框标签
        /// </summary>
        [UxmlAttribute]
        public string Label
        {
            get => _label?.text ?? string.Empty;
            set
            {
                if (_label != null)
                {
                    _label.text = value ?? string.Empty;
                    _label.style.display = string.IsNullOrEmpty(value) ? DisplayStyle.None : DisplayStyle.Flex;
                }
            }
        }

        /// <summary>
        /// 占位符文本
        /// </summary>
        [UxmlAttribute]
        public string Placeholder
        {
            get => _placeholder;
            set
            {
                _placeholder = value ?? string.Empty;
                UpdatePlaceholder();
            }
        }

        /// <summary>
        /// 输入类型
        /// </summary>
        [UxmlAttribute]
        public string InputType
        {
            get => _inputType;
            set
            {
                _inputType = value ?? "text";
                UpdateInputType();
            }
        }

        /// <summary>
        /// 帮助文本
        /// </summary>
        [UxmlAttribute]
        public string HelperText
        {
            get => _helperText?.text ?? string.Empty;
            set
            {
                if (_helperText != null)
                {
                    _helperText.text = value ?? string.Empty;
                    _helperText.style.display = string.IsNullOrEmpty(value) ? DisplayStyle.None : DisplayStyle.Flex;
                }
            }
        }

        /// <summary>
        /// 是否只读
        /// </summary>
        [UxmlAttribute]
        public bool IsReadOnly
        {
            get => _isReadOnly;
            set
            {
                _isReadOnly = value;
                if (_textField != null)
                {
                    _textField.isReadOnly = value;
                }
                UpdateModifierClass("readonly", value);
            }
        }

        /// <summary>
        /// 是否禁用
        /// </summary>
        [UxmlAttribute]
        public new bool IsDisabled
        {
            get => _isDisabled;
            set
            {
                _isDisabled = value;
                if (_textField != null)
                {
                    _textField.SetEnabled(!value);
                }
                UpdateModifierClass("disabled", value);
            }
        }

        /// <summary>
        /// 最大长度
        /// </summary>
        [UxmlAttribute]
        public int MaxLength
        {
            get => _textField?.maxLength ?? -1;
            set
            {
                if (_textField != null)
                {
                    _textField.maxLength = value;
                }
            }
        }

        /// <summary>
        /// 底层TextField元素
        /// </summary>
        public TextField TextField => _textField;

        /// <summary>
        /// 模板路径
        /// </summary>
        protected override string TemplatePath => TEMPLATE_PATH;

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public DaisyInput() : base("input")
        {
            _placeholder = string.Empty;
            _currentSize = "md";
            _currentState = "normal";
        }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        public DaisyInput(string placeholder) : base("input")
        {
            _placeholder = placeholder ?? string.Empty;
            _currentSize = "md";
            _currentState = "normal";
        }

        #endregion

        #region 重写方法

        /// <summary>
        /// 重写基类的模板加载方法
        /// </summary>
        protected override void LoadTemplate()
        {
            try
            {
                // 调用基类的模板加载逻辑
                base.LoadTemplate();
                
                // 查询和缓存子元素
                InitializeFromTemplate();
            }
            catch (System.Exception ex)
            {
                Logging.LogError("DaisyInput", $"初始化组件失败: {ex.Message}");
                CreateInputStructure();
            }
        }

        /// <summary>
        /// 组件特定的初始化
        /// </summary>
        protected override void OnInitialize()
        {
            // 确保组件设置完成
            if (_textField != null)
            {
                SetupComponent();
            }
            
            ValidateComponent();
            if (DaisyUtilities.IsDebugMode)
            {
                Logging.LogInfo("DaisyInput", "输入框组件初始化完成");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新修饰符类
        /// </summary>
        private void UpdateModifierClass(string modifier, bool enabled)
        {
            string className = $"daisy-input-{modifier}";
            if (enabled)
            {
                AddToClassList(className);
            }
            else
            {
                RemoveFromClassList(className);
            }
        }

        /// <summary>
        /// 验证组件完整性
        /// </summary>
        private new void ValidateComponent()
        {
            if (_textField == null)
            {
                Logging.LogError("DaisyInput", "TextField组件未正确初始化");
            }
            
            if (DaisyUtilities.IsDebugMode)
            {
                Logging.LogInfo("DaisyInput", $"组件验证完成 - 类型: {_inputType}, 状态: {_currentState}, 尺寸: {_currentSize}");
            }
        }

        #endregion
    }
}