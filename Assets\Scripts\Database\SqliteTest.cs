// Microsoft.Data.Sqlite SQLite 测试脚本
// 使用 NuGet 包管理器安装的 Microsoft.Data.Sqlite
//
// 安装方法：
// 1. 在 Unity 中打开 Window > Package Manager
// 2. 点击 + 号，选择 "Add package from git URL"
// 3. 输入：com.unity.nuget.newtonsoft-json (如果需要)
// 4. 或者使用 NuGet for Unity 插件安装 Microsoft.Data.Sqlite

using System;
using UnityEngine;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using System.Linq;
using BlastingDesign.Utils;

// 使用 Microsoft.Data.Sqlite
using Microsoft.Data.Sqlite;

// SQLitePCL 初始化支持
#if UNITY_EDITOR || UNITY_STANDALONE
using SQLitePCL;
#endif

public class SqliteTest : MonoBehaviour
{
  [Header("SQLite 数据库配置")]
  [SerializeField] private string databaseName = "TestDatabase.db";
  [SerializeField] private bool useMemoryDatabase = false;
  [SerializeField] private bool createTestTable = true;
  [SerializeField] private bool useAsyncOperations = true;

  // SQLite连接字符串配置
  private string[] connectionStrings;
  private SqliteConnection conn;     // 创建一个SQLite数据库连接
  private string databasePath;
  private static bool isInitialized = false;

  async void Start()
  {
    try
    {
      // 初始化 SQLitePCL
      InitializeSQLitePCL();

      InitializeConnectionStrings();

      if (useAsyncOperations)
      {
        await SQLiteToConnectionAsync();
      }
      else
      {
        SQLiteToConnection();
      }
    }
    catch (Exception ex)
    {
      Logging.LogError("SqliteTest", $"启动时发生错误: {ex.Message}");
    }
  }

  /// <summary>
  /// 初始化 SQLitePCL 提供程序
  /// </summary>
  private static void InitializeSQLitePCL()
  {
    if (isInitialized)
    {
      return;
    }

    try
    {
      Logging.LogInfo("SqliteTest", "开始初始化 SQLitePCL...");

      // 简化的初始化方法
      // 对于大多数情况，Microsoft.Data.Sqlite 会自动处理初始化
      // 如果遇到 SetProvider 错误，可能需要安装额外的 NuGet 包

      Logging.LogInfo("SqliteTest", "使用 Microsoft.Data.Sqlite 自动初始化");
      Logging.LogInfo("SqliteTest", "如果遇到 SetProvider 错误，请安装以下 NuGet 包：");
      Logging.LogInfo("SqliteTest", "1. SQLitePCLRaw.bundle_e_sqlite3");
      Logging.LogInfo("SqliteTest", "2. 或者 SQLitePCLRaw.bundle_green");
      Logging.LogInfo("SqliteTest", "3. 或者 SQLitePCLRaw.bundle_winsqlite3");

      isInitialized = true;
      Logging.LogInfo("SqliteTest", "SQLitePCL 初始化完成");
    }
    catch (Exception ex)
    {
      Logging.LogError("SqliteTest", $"SQLitePCL 初始化失败: {ex.Message}");
      Logging.LogError("SqliteTest", "请安装正确的 SQLitePCLRaw bundle 包");
      isInitialized = true; // 标记为已初始化，避免重复尝试
    }
  }

  /// <summary>
  /// 初始化连接字符串
  /// </summary>
  private void InitializeConnectionStrings()
  {
    if (useMemoryDatabase)
    {
      // 内存数据库，用于测试
      connectionStrings = new string[]
      {
                "Data Source=:memory:",
                "Data Source=:memory:;Cache=Shared;"
      };
      Logging.LogInfo("SqliteTest", "使用内存数据库模式");
    }
    else
    {
      // 持久化数据库文件
      databasePath = Path.Combine(Application.persistentDataPath, databaseName);

      connectionStrings = new string[]
      {
                $"Data Source={databasePath}",
                $"Data Source={databasePath};Mode=ReadWriteCreate;",
                $"Data Source={databasePath};Mode=ReadWriteCreate;Cache=Shared;",
                $"Data Source={databasePath};Mode=ReadWriteCreate;Cache=Shared;Foreign Keys=True;"
      };

      Logging.LogInfo("SqliteTest", $"数据库文件路径: {databasePath}");
    }
  }

  /// <summary>
  /// 尝试连接SQLite数据库（同步版本）
  /// </summary>
  private void SQLiteToConnection()
  {
    // 尝试不同的连接字符串
    for (int i = 0; i < connectionStrings.Length; i++)
    {
      try
      {
        Logging.LogInfo("SqliteTest", $"尝试连接方案 {i + 1}: {connectionStrings[i]}");

        conn = new SqliteConnection(connectionStrings[i]);
        Logging.LogInfo("SqliteTest", "开始连接数据库:" + conn.State);

        // 判断数据库是否处于关闭状态
        if (conn.State == ConnectionState.Closed)
        {
          conn.Open();
          Logging.LogInfo("SqliteTest", "尝试连接数据库:" + conn.State);

          if (conn.State == ConnectionState.Open)
          {
            Logging.LogInfo("SqliteTest", $"SQLite连接成功！使用方案 {i + 1}");

            // 测试数据库操作
            if (createTestTable)
            {
              TestDatabaseOperations();
            }

            return; // 连接成功，退出循环
          }
          else
          {
            Logging.LogError("SqliteTest", $"方案 {i + 1} 连接失败");
          }
        }
      }
      catch (Exception ex)
      {
        Logging.LogError("SqliteTest", $"方案 {i + 1} 数据库连接异常: " + ex.Message);
        if (i == connectionStrings.Length - 1) // 最后一个方案也失败了
        {
          Logging.LogError("SqliteTest", "所有连接方案都失败了，异常详情: " + ex.ToString());
        }

        // 关闭当前连接（如果存在）
        if (conn != null && conn.State != ConnectionState.Closed)
        {
          conn.Close();
        }
      }
    }
  }

  /// <summary>
  /// 尝试连接SQLite数据库（异步版本）
  /// </summary>
  private async Task SQLiteToConnectionAsync()
  {
    // 尝试不同的连接字符串
    for (int i = 0; i < connectionStrings.Length; i++)
    {
      try
      {
        Logging.LogInfo("SqliteTest", $"尝试异步连接方案 {i + 1}: {connectionStrings[i]}");

        conn = new SqliteConnection(connectionStrings[i]);
        Logging.LogInfo("SqliteTest", "开始连接数据库:" + conn.State);

        // 判断数据库是否处于关闭状态
        if (conn.State == ConnectionState.Closed)
        {
          await conn.OpenAsync();
          Logging.LogInfo("SqliteTest", "尝试连接数据库:" + conn.State);

          if (conn.State == ConnectionState.Open)
          {
            Logging.LogInfo("SqliteTest", $"SQLite异步连接成功！使用方案 {i + 1}");

            // 测试数据库操作
            if (createTestTable)
            {
              await TestDatabaseOperationsAsync();
            }

            return; // 连接成功，退出循环
          }
          else
          {
            Logging.LogError("SqliteTest", $"方案 {i + 1} 连接失败");
          }
        }
      }
      catch (Exception ex)
      {
        Logging.LogError("SqliteTest", $"方案 {i + 1} 数据库连接异常: " + ex.Message);
        if (i == connectionStrings.Length - 1) // 最后一个方案也失败了
        {
          Logging.LogError("SqliteTest", "所有连接方案都失败了，异常详情: " + ex.ToString());
        }

        // 关闭当前连接（如果存在）
        if (conn != null && conn.State != ConnectionState.Closed)
        {
          conn.Close();
        }
      }
    }
  }

  /// <summary>
  /// 测试数据库基本操作
  /// </summary>
  private void TestDatabaseOperations()
  {
    try
    {
      Logging.LogInfo("SqliteTest", "开始测试数据库操作...");

      // 创建测试表
      string createTableSQL = @"
                CREATE TABLE IF NOT EXISTS TestTable (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    value REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

      using (var command = new SqliteCommand(createTableSQL, conn))
      {
        command.ExecuteNonQuery();
        Logging.LogInfo("SqliteTest", "测试表创建成功");
      }

      // 插入测试数据
      string insertSQL = "INSERT INTO TestTable (name, value) VALUES (@name, @value)";
      using (var command = new SqliteCommand(insertSQL, conn))
      {
        command.Parameters.AddWithValue("@name", "测试数据");
        command.Parameters.AddWithValue("@value", 123.45);
        int rowsAffected = command.ExecuteNonQuery();
        Logging.LogInfo("SqliteTest", $"插入数据成功，影响行数: {rowsAffected}");
      }

      // 查询数据
      string selectSQL = "SELECT COUNT(*) FROM TestTable";
      using (var command = new SqliteCommand(selectSQL, conn))
      {
        object result = command.ExecuteScalar();
        Logging.LogInfo("SqliteTest", $"表中数据总数: {result}");
      }

      // 查询详细数据
      string selectDetailSQL = "SELECT id, name, value, created_at FROM TestTable ORDER BY id DESC LIMIT 5";
      using (var command = new SqliteCommand(selectDetailSQL, conn))
      {
        using (var reader = command.ExecuteReader())
        {
          Logging.LogInfo("SqliteTest", "最近的5条记录:");
          while (reader.Read())
          {
            string record = $"ID: {reader["id"]}, Name: {reader["name"]}, Value: {reader["value"]}, Created: {reader["created_at"]}";
            Logging.LogInfo("SqliteTest", record);
          }
        }
      }

      Logging.LogInfo("SqliteTest", "数据库操作测试完成");
    }
    catch (Exception ex)
    {
      Logging.LogError("SqliteTest", $"数据库操作测试失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 获取数据库信息
  /// </summary>
  [ContextMenu("获取数据库信息")]
  public void GetDatabaseInfo()
  {
    if (conn == null || conn.State != ConnectionState.Open)
    {
      Logging.LogWarning("SqliteTest", "数据库未连接");
      return;
    }

    try
    {
      // 获取SQLite版本
      string versionSQL = "SELECT sqlite_version()";
      using (var command = new SqliteCommand(versionSQL, conn))
      {
        object version = command.ExecuteScalar();
        Logging.LogInfo("SqliteTest", $"SQLite版本: {version}");
      }

      // 获取数据库文件大小（仅对文件数据库有效）
      if (!useMemoryDatabase && File.Exists(databasePath))
      {
        FileInfo fileInfo = new FileInfo(databasePath);
        Logging.LogInfo("SqliteTest", $"数据库文件大小: {fileInfo.Length} 字节");
      }

      // 获取表列表
      string tablesSQL = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name";
      using (var command = new SqliteCommand(tablesSQL, conn))
      {
        using (var reader = command.ExecuteReader())
        {
          Logging.LogInfo("SqliteTest", "数据库中的表:");
          while (reader.Read())
          {
            Logging.LogInfo("SqliteTest", $"  - {reader["name"]}");
          }
        }
      }
    }
    catch (Exception ex)
    {
      Logging.LogError("SqliteTest", $"获取数据库信息失败: {ex.Message}");
    }
  }

  /// <summary>
  /// 清理资源
  /// </summary>
  void OnDestroy()
  {
    if (conn != null && conn.State != ConnectionState.Closed)
    {
      conn.Close();
      Logging.LogInfo("SqliteTest", "SQLite连接已关闭");
    }
  }

  /// <summary>
  /// 手动测试连接
  /// </summary>
  [ContextMenu("重新测试连接")]
  public void RetestConnection()
  {
    if (conn != null && conn.State != ConnectionState.Closed)
    {
      conn.Close();
    }

    InitializeConnectionStrings();
    SQLiteToConnection();
  }

  /// <summary>
  /// 测试数据库基本操作（异步版本）
  /// </summary>
  private async Task TestDatabaseOperationsAsync()
  {
    try
    {
      Logging.LogInfo("SqliteTest", "开始异步测试数据库操作...");

      // 创建测试表
      string createTableSQL = @"
                CREATE TABLE IF NOT EXISTS TestTable (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    value REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

      using (var command = new SqliteCommand(createTableSQL, conn))
      {
        await command.ExecuteNonQueryAsync();
        Logging.LogInfo("SqliteTest", "测试表创建成功");
      }

      // 插入测试数据
      string insertSQL = "INSERT INTO TestTable (name, value) VALUES (@name, @value)";
      using (var command = new SqliteCommand(insertSQL, conn))
      {
        command.Parameters.AddWithValue("@name", "异步测试数据");
        command.Parameters.AddWithValue("@value", 456.78);
        int rowsAffected = await command.ExecuteNonQueryAsync();
        Logging.LogInfo("SqliteTest", $"异步插入数据成功，影响行数: {rowsAffected}");
      }

      // 查询数据
      string selectSQL = "SELECT COUNT(*) FROM TestTable";
      using (var command = new SqliteCommand(selectSQL, conn))
      {
        object result = await command.ExecuteScalarAsync();
        Logging.LogInfo("SqliteTest", $"表中数据总数: {result}");
      }

      // 查询详细数据
      string selectDetailSQL = "SELECT id, name, value, created_at FROM TestTable ORDER BY id DESC LIMIT 5";
      using (var command = new SqliteCommand(selectDetailSQL, conn))
      {
        using (var reader = await command.ExecuteReaderAsync())
        {
          Logging.LogInfo("SqliteTest", "最近的5条记录:");
          while (await reader.ReadAsync())
          {
            string record = $"ID: {reader["id"]}, Name: {reader["name"]}, Value: {reader["value"]}, Created: {reader["created_at"]}";
            Logging.LogInfo("SqliteTest", record);
          }
        }
      }

      Logging.LogInfo("SqliteTest", "异步数据库操作测试完成");
    }
    catch (Exception ex)
    {
      Logging.LogError("SqliteTest", $"异步数据库操作测试失败: {ex.Message}");
    }
  }
}