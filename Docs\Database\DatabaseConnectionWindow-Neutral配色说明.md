# DatabaseConnectionWindow Neutral 配色方案说明

## 配色方案对比

根据您的反馈，原来的 Slate 色系确实偏蓝，我已经将配色方案更改为 **Neutral 色系**，这是 Utility.uss 中最纯粹的灰色方案。

### 🎨 **颜色对比分析**

#### Slate 色系 (偏蓝灰) → Neutral 色系 (纯灰)

| 色阶 | Slate (原配色) | Neutral (新配色) | 差异说明 |
|------|----------------|------------------|----------|
| 900 | `#0f172a` (深蓝灰) | `#171717` (纯深灰) | 去除蓝色调，更纯粹 |
| 800 | `#1e293b` (中深蓝灰) | `#262626` (纯中深灰) | 更中性的深灰色 |
| 700 | `#334155` (中蓝灰) | `#404040` (纯中灰) | 完全中性的灰色 |
| 600 | `#475569` (浅蓝灰) | `#525252` (纯浅灰) | 无色调偏向 |
| 500 | `#64748b` (中等蓝灰) | `#737373` (中等纯灰) | 标准中性灰 |
| 400 | `#94a3b8` (浅蓝灰) | `#a3a3a3` (浅纯灰) | 更接近您的需求 |
| 300 | `#cbd5e1` (很浅蓝灰) | `#d4d4d4` (很浅纯灰) | 纯净的浅灰 |
| 200 | `#e2e8f0` (极浅蓝灰) | `#e5e5e5` (极浅纯灰) | 无色调的浅灰 |
| 100 | `#f1f5f9` (近白蓝灰) | `#f5f5f5` (近白纯灰) | 纯净的近白色 |

### 🔍 **视觉效果对比**

#### 主要改进
1. **去除蓝色调**: Neutral 色系完全没有蓝色倾向
2. **更纯粹的灰色**: 接近您截图中的深灰色效果
3. **更好的中性感**: 适合专业的数据库配置界面
4. **更接近系统原生**: 与操作系统的深色主题更一致

## 🎯 **新配色方案详解**

### 主要元素配色

#### 1. 主背景
```css
.bg-neutral-900 /* #171717 - 纯深灰色主背景 */
```

#### 2. 卡片背景
```css
.bg-neutral-800 /* #262626 - 纯深灰卡片背景 */
```

#### 3. 高级设置背景
```css
.bg-neutral-700 /* #404040 - 中等纯灰背景 */
```

#### 4. 输入框背景
```css
.bg-neutral-700 /* #404040 - 与高级设置同色 */
```

#### 5. 文本颜色
```css
.text-neutral-100 /* #f5f5f5 - 主标题，高对比度 */
.text-neutral-200 /* #e5e5e5 - 分组标题 */
.text-neutral-300 /* #d4d4d4 - 标签文本 */
.text-neutral-400 /* #a3a3a3 - 副标题 */
```

#### 6. 边框颜色
```css
.border-neutral-600 /* #525252 - 主要边框 */
.border-neutral-700 /* #404040 - 分隔线 */
```

### 输入框样式 `.field-input-enhanced-neutral`

```css
.field-input-enhanced-neutral {
    background-color: var(--neutral-700);  /* #404040 */
    border-color: var(--neutral-600);      /* #525252 */
    color: var(--neutral-100);             /* #f5f5f5 */
}

/* 交互状态 */
.field-input-enhanced-neutral:hover {
    border-color: var(--neutral-500);      /* #737373 */
}

.field-input-enhanced-neutral:focus {
    border-color: var(--blue-400);         /* #60a5fa - 保持蓝色聚焦 */
}
```

## 🔄 **其他可选的灰色方案**

如果您觉得 Neutral 还不够理想，Utility.uss 中还有其他选择：

### 1. **Gray 色系** (略带蓝调的灰色)
```css
--gray-900: #111827  /* 比 Neutral 稍微偏蓝一点 */
--gray-800: #1f2937
--gray-700: #374151
```

### 2. **Zinc 色系** (现代中性灰)
```css
--zinc-900: #18181b  /* 非常接近纯黑的现代灰 */
--zinc-800: #27272a
--zinc-700: #3f3f46
```

### 3. **Stone 色系** (暖灰色)
```css
--stone-900: #1c1917  /* 带有轻微暖色调的灰色 */
--stone-800: #292524
--stone-700: #44403c
```

## 📊 **配色推荐排序**

根据您的需求（纯深灰色，不偏蓝），推荐顺序：

1. **🥇 Neutral** (当前使用) - 最纯粹的灰色
2. **🥈 Zinc** - 现代中性灰，略偏冷色调
3. **🥉 Stone** - 暖灰色，适合温暖的界面
4. **Gray** - 略带蓝调，不推荐

## 🛠 **快速切换方法**

如果您想尝试其他配色方案，只需要替换 UXML 中的颜色类名：

### 切换到 Zinc 色系
```bash
# 全局替换
neutral-900 → zinc-900
neutral-800 → zinc-800
neutral-700 → zinc-700
neutral-600 → zinc-600
neutral-500 → zinc-500
neutral-400 → zinc-400
neutral-300 → zinc-300
neutral-200 → zinc-200
neutral-100 → zinc-100
```

### 切换到 Stone 色系
```bash
# 全局替换
neutral-900 → stone-900
neutral-800 → stone-800
neutral-700 → stone-700
# ... 以此类推
```

## 🎨 **自定义配色建议**

如果您有特定的颜色需求，也可以在补充样式文件中覆盖：

```css
/* 自定义更深的灰色 */
:root {
    --custom-gray-900: #0a0a0a;  /* 更深的主背景 */
    --custom-gray-800: #1a1a1a;  /* 更深的卡片背景 */
    --custom-gray-700: #2a2a2a;  /* 更深的输入框背景 */
}

.bg-custom-gray-900 {
    background-color: var(--custom-gray-900);
}

.bg-custom-gray-800 {
    background-color: var(--custom-gray-800);
}

.bg-custom-gray-700 {
    background-color: var(--custom-gray-700);
}
```

## 📝 **使用说明**

### 当前配置
- 主配色：**Neutral 色系**
- 强调色：**Blue 色系** (用于聚焦状态和主按钮)
- 输入框样式：`.field-input-enhanced-neutral`

### 文件更新
1. ✅ `DatabaseConnectionWindow.uxml` - 已更新为 Neutral 配色
2. ✅ `DatabaseConnectionWindow-Utility.uss` - 已添加 Neutral 样式类
3. ✅ 所有交互状态已适配

### 测试建议
1. 检查整体视觉效果是否符合预期
2. 测试输入框的交互状态（悬停、聚焦）
3. 确认文本对比度是否足够清晰
4. 验证按钮的交互反馈

现在的配色方案应该更接近您需要的纯深灰色效果，没有蓝色倾向。如果您觉得还需要调整，我可以帮您尝试其他配色方案或进行细微调整。
