using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 组件预览管理器
    /// 负责管理所有组件预览提供者的注册和获取
    /// </summary>
    public class ComponentPreviewManager
    {
        #region 单例模式

        private static ComponentPreviewManager _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static ComponentPreviewManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ComponentPreviewManager();
                        }
                    }
                }
                return _instance;
            }
        }

        #endregion

        #region 私有字段

        private readonly Dictionary<string, IComponentPreviewProvider> _providers;
        private bool _isInitialized = false;

        #endregion

        #region 构造函数

        private ComponentPreviewManager()
        {
            _providers = new Dictionary<string, IComponentPreviewProvider>();
            Initialize();
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化预览提供者
        /// </summary>
        private void Initialize()
        {
            try
            {
                if (_isInitialized)
                {
                    return;
                }

                // 注册所有内置的预览提供者
                RegisterBuiltinProviders();

                _isInitialized = true;
                Logging.LogInfo("ComponentPreviewManager", "组件预览管理器初始化完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"初始化组件预览管理器时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 注册内置的预览提供者
        /// </summary>
        private void RegisterBuiltinProviders()
        {
            try
            {
                // 注册按钮预览提供者
                RegisterProvider(new ButtonPreviewProvider());

                // 注册输入框预览提供者
                RegisterProvider(new InputPreviewProvider());

                // 注册卡片预览提供者
                RegisterProvider(new CardPreviewProvider());

                // 注册选择器预览提供者
                RegisterProvider(new SelectPreviewProvider());

                // 注册模态框预览提供者
                RegisterProvider(new ModalPreviewProvider());

                // 注册下拉菜单预览提供者
                RegisterProvider(new DropdownPreviewProvider());

                // 注册选项卡预览提供者
                RegisterProvider(new TabPreviewProvider());

                // 注册树组件预览提供者
                RegisterProvider(new TreePreviewProvider());

                Logging.LogInfo("ComponentPreviewManager", $"已注册 {_providers.Count} 个内置预览提供者");
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"注册内置预览提供者时发生错误: {ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 注册预览提供者
        /// </summary>
        /// <param name="provider">预览提供者实例</param>
        public void RegisterProvider(IComponentPreviewProvider provider)
        {
            try
            {
                if (provider == null)
                {
                    Logging.LogWarning("ComponentPreviewManager", "尝试注册空的预览提供者");
                    return;
                }

                if (string.IsNullOrEmpty(provider.ComponentId))
                {
                    Logging.LogWarning("ComponentPreviewManager", "预览提供者的ComponentId不能为空");
                    return;
                }

                if (_providers.ContainsKey(provider.ComponentId))
                {
                    Logging.LogWarning("ComponentPreviewManager", $"预览提供者 {provider.ComponentId} 已存在，将被覆盖");
                }

                _providers[provider.ComponentId] = provider;
                Logging.LogInfo("ComponentPreviewManager", $"已注册预览提供者: {provider.ComponentId} - {provider.ComponentName}");
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"注册预览提供者时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取预览提供者
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <returns>预览提供者实例，如果不存在则返回null</returns>
        public IComponentPreviewProvider GetProvider(string componentId)
        {
            try
            {
                if (string.IsNullOrEmpty(componentId))
                {
                    return null;
                }

                return _providers.TryGetValue(componentId, out var provider) ? provider : null;
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"获取预览提供者时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检查是否存在指定的预览提供者
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <returns>如果存在返回true，否则返回false</returns>
        public bool HasProvider(string componentId)
        {
            try
            {
                return !string.IsNullOrEmpty(componentId) && _providers.ContainsKey(componentId);
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"检查预览提供者存在性时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有已注册的预览提供者
        /// </summary>
        /// <returns>预览提供者列表</returns>
        public IEnumerable<IComponentPreviewProvider> GetAllProviders()
        {
            try
            {
                return _providers.Values.ToList();
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"获取所有预览提供者时发生错误: {ex.Message}");
                return new List<IComponentPreviewProvider>();
            }
        }

        /// <summary>
        /// 获取所有已注册的组件ID
        /// </summary>
        /// <returns>组件ID列表</returns>
        public IEnumerable<string> GetAllComponentIds()
        {
            try
            {
                return _providers.Keys.ToList();
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"获取所有组件ID时发生错误: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// 移除预览提供者
        /// </summary>
        /// <param name="componentId">组件ID</param>
        /// <returns>如果成功移除返回true，否则返回false</returns>
        public bool RemoveProvider(string componentId)
        {
            try
            {
                if (string.IsNullOrEmpty(componentId))
                {
                    return false;
                }

                if (_providers.Remove(componentId))
                {
                    Logging.LogInfo("ComponentPreviewManager", $"已移除预览提供者: {componentId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"移除预览提供者时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清空所有预览提供者
        /// </summary>
        public void ClearProviders()
        {
            try
            {
                var count = _providers.Count;
                _providers.Clear();
                Logging.LogInfo("ComponentPreviewManager", $"已清空 {count} 个预览提供者");
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"清空预览提供者时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 重新初始化管理器
        /// </summary>
        public void Reinitialize()
        {
            try
            {
                _isInitialized = false;
                ClearProviders();
                Initialize();
                Logging.LogInfo("ComponentPreviewManager", "组件预览管理器已重新初始化");
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"重新初始化组件预览管理器时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取管理器状态信息
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public string GetStatusInfo()
        {
            try
            {
                return $"ComponentPreviewManager Status:\n" +
                       $"- Initialized: {_isInitialized}\n" +
                       $"- Registered Providers: {_providers.Count}\n" +
                       $"- Component IDs: [{string.Join(", ", _providers.Keys)}]";
            }
            catch (Exception ex)
            {
                Logging.LogError("ComponentPreviewManager", $"获取状态信息时发生错误: {ex.Message}");
                return "Error getting status info";
            }
        }

        #endregion
    }
}
