/* DaisyUI Selects - 选择器组件样式 */

/* 基础选择器样式 */
.daisy-select {
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* 选择器标签 */
.daisy-select-label {
    font-size: 14px;
    -unity-font-style: normal;
    color: var(--base-content);
    margin-bottom: 4px;
}

/* 选择器容器 */
.daisy-select-container {
    position: relative;
    display: flex;
    align-items: center;
}

/* 选择器字段 */
.daisy-select-field {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--base-content);
    background-color: var(--base-100);
    border-width: 1px;
    border-radius: var(--border-radius);
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

/* Unity DropdownField 特定样式 */
.daisy-select-field > .unity-base-dropdown__container-outer {
    background-color: transparent;
    border-width: 1px;
    padding: 0;
    margin: 0;
}

.daisy-select-field > .unity-base-dropdown__container-outer > .unity-base-dropdown__container-inner {
    background-color: transparent;
    border-width: 1px;
    padding: 0;
    margin: 0;
}

.daisy-select-field .unity-base-dropdown__text {
    color: inherit;
    font-size: inherit;
    padding: 0;
    margin: 0;
}

.daisy-select-field .unity-base-dropdown__arrow {
    width: 16px;
    height: 16px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    -unity-background-image-tint-color: var(--base-content);
}

/* 焦点状态 */
.daisy-select:focus .daisy-select-field {
    border-color: var(--primary);
}

/* 帮助文本 */
.daisy-select-helper {
    font-size: 12px;
    color: var(--base-content);
    opacity: 0.7;
    margin-top: 4px;
}

/* 选择器尺寸变体 */
.daisy-select-xs .daisy-select-field {
    padding: 6px 8px;
    font-size: 12px;
}

.daisy-select-xs .daisy-select-label {
    font-size: 12px;
}

.daisy-select-sm .daisy-select-field {
    padding: 8px 12px;
    font-size: 13px;
}

.daisy-select-sm .daisy-select-label {
    font-size: 13px;
}

.daisy-select-lg .daisy-select-field {
    padding: 16px 20px;
    font-size: 16px;
}

.daisy-select-lg .daisy-select-label {
    font-size: 16px;
}

/* 选择器修饰符 */

/* 边框样式 */
.daisy-select-bordered .daisy-select-field {
    border-color: var(--base-300);
    background-color: var(--base-100);
}

.daisy-select-bordered:focus .daisy-select-field {
    border-color: var(--primary);
}

/* 幽灵样式 */
.daisy-select-ghost .daisy-select-field {
    border-color: transparent;
    background-color: transparent;
}

.daisy-select-ghost:focus .daisy-select-field {
    border-color: var(--primary);
    background-color: var(--base-100);
}

/* 状态样式 */

/* 错误状态 */
.daisy-select-error .daisy-select-field {
    border-color: var(--error);
}

.daisy-select-error:focus .daisy-select-field {
    border-color: var(--error);
}

.daisy-select-error .daisy-select-helper {
    color: var(--error);
}

/* 成功状态 */
.daisy-select-success .daisy-select-field {
    border-color: var(--success);
}

.daisy-select-success:focus .daisy-select-field {
    border-color: var(--success);
}

.daisy-select-success .daisy-select-helper {
    color: var(--success);
}

/* 警告状态 */
.daisy-select-warning .daisy-select-field {
    border-color: var(--warning);
}

.daisy-select-warning:focus .daisy-select-field {
    border-color: var(--warning);
}

.daisy-select-warning .daisy-select-helper {
    color: var(--warning);
}

/* 信息状态 */
.daisy-select-info .daisy-select-field {
    border-color: var(--info);
}

.daisy-select-info:focus .daisy-select-field {
    border-color: var(--info);
}

.daisy-select-info .daisy-select-helper {
    color: var(--info);
}

/* 禁用状态 */
.daisy-select-disabled .daisy-select-field {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: var(--base-200);
    border-color: var(--base-300);
}

.daisy-select-disabled .daisy-select-label {
    opacity: 0.6;
}

/* 多选状态 */
.daisy-select-multiple .daisy-select-field {
    min-height: 40px;
    padding: 8px 16px;
}

.daisy-select-multiple .daisy-select-field .unity-base-dropdown__text {
    white-space: normal;
}

/* 悬停效果 */
.daisy-select:hover:not(.daisy-select-disabled) .daisy-select-field {
    border-color: var(--primary);
}

/* 下拉菜单样式 */
.unity-base-dropdown__list-view {
    background-color: var(--base-100);
    border-width: 1px;
    border-radius: var(--border-radius);
    max-height: 200px;
}

.unity-base-dropdown__item {
    padding: 8px 16px;
    color: var(--base-content);
    background-color: transparent;
    border-width: 1px;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

.unity-base-dropdown__item:hover {
    background-color: var(--base-200);
}

.unity-base-dropdown__item--selected {
    background-color: var(--primary);
    color: var(--primary-content);
}

/* 选择器组合 */
.daisy-select-group {
    display: flex;
    align-items: flex-end;
}

.daisy-select-group .daisy-select {
    flex: 1;
}

/* 选择器网格 */
.daisy-select-grid {
    display: grid;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .daisy-select-grid {
    }
    
    .daisy-select-group {
        flex-direction: column;
        align-items: stretch;
    }
}

/* 暗色主题适配 */
.theme-dark .daisy-select-field {
    background-color: var(--base-200);
    border-color: var(--base-300);
    color: var(--base-content);
}

.theme-dark .daisy-select-ghost .daisy-select-field {
    background-color: transparent;
}

.theme-dark .daisy-select-disabled .daisy-select-field {
    background-color: var(--base-300);
}

.theme-dark .unity-base-dropdown__list-view {
    background-color: var(--base-200);
    border-color: var(--base-300);
}

.theme-dark .unity-base-dropdown__item:hover {
    background-color: var(--base-300);
}

/* 动画效果 */
.daisy-select-animate-in {
}

@keyframes selectFadeIn {
    from {
        opacity: 0;
        translate: translateY(10px);
    }
    to {
        opacity: 1;
        translate: translateY(0);
    }
}

.daisy-select-animate-out {
}

@keyframes selectFadeOut {
    from {
        opacity: 1;
        translate: translateY(0);
    }
    to {
        opacity: 0;
        translate: translateY(-10px);
    }
}



