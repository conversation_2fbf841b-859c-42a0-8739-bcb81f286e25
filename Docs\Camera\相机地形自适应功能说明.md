# 相机地形自适应功能说明

## 功能概述

新增了相机地形自适应缩放和平移功能，使相机能够更智能地处理复杂地形场景。

## 主要特性

### 1. 地形自适应缩放 (Terrain Adaptive Zoom)

**功能描述**：
- 缩放时优先计算屏幕中心射线到物体表面的实际距离
- 如果射线没有碰撞到物体，则计算相机与基面的距离
- 基于实际检测到的表面距离进行缩放限制

**配置参数**：
- `useTerrainAdaptiveZoom` (bool): 是否启用地形自适应缩放，默认为 true
- `adaptiveMinZoomDistance` (float): 地形自适应模式下的最小缩放距离，默认为 0.5

**工作原理**：
1. 发射从屏幕中心到场景的射线
2. 按优先级检测：**Cesium倾斜摄影模型** → GridPlane → 物理物体 → 数学基面
3. 计算相机到检测点的实际距离
4. 使用自适应最小距离限制，允许更接近表面
5. 基于实际距离应用缩放操作

### 2. 地形自适应平移 (Terrain Adaptive Pan)

**功能描述**：
- 平移相机时保持相机与地形的相对高度一致
- 相机能够适应地形高度变化，实现平滑的地形跟随

**配置参数**：
- `useTerrainAdaptivePan` (bool): 是否启用地形自适应平移，默认为 true
- `terrainFollowSmoothness` (float): 地形跟随平滑度，默认为 0.1

**工作原理**：
1. 计算平移后的目标位置
2. 检测目标位置的地面高度
3. 计算当前相机相对于地面的高度
4. 调整目标位置以保持相对高度不变
5. 使用平滑插值避免高度变化过于突兀

## 技术实现

### 新增方法

#### InputManager.RaycastUtils.cs
- `CalculateDistanceToSurface(Vector2 screenPosition)`: 计算屏幕位置射线到表面的距离
- `GetSurfaceHeightAtPosition(Vector3 worldPosition)`: 获取世界坐标位置处的表面高度
- `AdjustCameraHeightToTerrain(Vector3 targetPosition)`: 调整相机高度以适应地形

#### InputManager.ViewportControl.cs
- `ZoomCameraWithTerrainAdaptive(float zoomDelta, Vector2 screenPosition)`: 地形自适应缩放
- `ZoomCameraWithTerrainAdaptiveEasing(float zoomDelta, Vector2 screenCenter)`: 带缓动的地形自适应缩放
- 修改 `HandlePanning(Vector2 mouseDelta)`: 添加地形高度跟随逻辑

### 向后兼容性

- 保留了原有的缩放和平移逻辑作为 Legacy 方法
- 通过配置参数控制是否启用新功能
- 默认启用新功能，但可以通过设置参数禁用

## 使用方法

### 在 Inspector 中配置

1. 找到 InputManager 组件
2. 在 "缩放设置" 部分：
   - 勾选 `Use Terrain Adaptive Zoom` 启用地形自适应缩放
   - 勾选 `Use Terrain Adaptive Pan` 启用地形自适应平移
   - 调整 `Terrain Follow Smoothness` 控制地形跟随的平滑度

### 代码中控制

```csharp
// 启用/禁用地形自适应缩放
inputManager.useTerrainAdaptiveZoom = true;

// 启用/禁用地形自适应平移
inputManager.useTerrainAdaptivePan = true;

// 调整地形跟随平滑度
inputManager.terrainFollowSmoothness = 0.2f;

// 设置自适应最小缩放距离
inputManager.adaptiveMinZoomDistance = 0.3f;
```

## Cesium倾斜摄影模型支持

### 特殊优化

针对Cesium倾斜摄影模型进行了特殊优化：

1. **优先级检测**：Cesium模型在射线检测中具有最高优先级
2. **精确距离计算**：直接基于Cesium模型表面计算缩放距离
3. **更小的最小距离**：允许相机更接近Cesium模型表面
4. **高度跟随**：平移时准确跟随Cesium地形高度变化

### 配置要求

- 确保Cesium模型在正确的图层上（通过 `cesiumLayerMask` 设置）
- 建议设置 `adaptiveMinZoomDistance` 为较小值（如0.5）以便更接近模型
- 启用 `showDebugRaycast` 可以查看射线检测的详细信息

## 适用场景

- **Cesium倾斜摄影模型**场景（城市建模、地形可视化等）
- 复杂地形场景（山地、峡谷等）
- 建筑物高度变化较大的城市场景
- 需要精确控制相机与地面距离的应用
- 地形编辑器或建筑设计工具

## 注意事项

1. **性能考虑**：每次缩放和平移都会进行射线检测，在复杂场景中可能影响性能
2. **图层设置**：确保 `groundLayerMask` 正确设置，包含需要检测的地形物体
3. **平滑度调节**：`terrainFollowSmoothness` 值过大可能导致相机跟随过于缓慢
4. **边界情况**：在没有地形物体的区域，系统会回退到基面高度计算

## 调试功能

- 设置 `showDebugRaycast = true` 可以在 Scene 视图中看到射线检测的可视化
- 控制台会输出相关的调试信息
