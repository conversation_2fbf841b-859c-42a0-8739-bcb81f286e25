# src/db/ 数据库管理层技术规格文档

## 模块概述

数据库管理层是系统的数据持久化核心组件，负责管理SQLite和SQL Server双数据库架构的连接、会话、事务处理等功能。该模块基于SQLAlchemy ORM框架，提供统一的数据访问接口，支持数据库的自动切换和双写操作。

## 文件清单及功能

### 核心文件

| 文件名 | 功能描述 | 关键职责 |
|--------|----------|----------|
| `database_manager.py` | 数据库管理器核心类 | 连接管理、会话管理、引擎初始化 |

### 相关文件

| 文件名 | 位置 | 功能描述 |
|--------|------|----------|
| `base_model.py` | `src/models/` | 基础模型类，集成数据库操作 |
| `__init__.py` | `src/models/` | 模型模块初始化，数据库初始化接口 |

## 核心数据结构

### DatabaseManager类架构

```python
class DatabaseManager:
    """数据库管理器，处理双数据库操作"""
    
    # 单例模式属性
    _instance: Optional[DatabaseManager] = None
    
    # 数据库引擎
    sqlite_engine: Optional[Engine] = None
    sqlserver_engine: Optional[Engine] = None
    
    # 会话工厂
    sqlite_session: Optional[scoped_session] = None
    sqlserver_session: Optional[scoped_session] = None
```

### 连接字符串格式

```python
# SQLite连接字符串
sqlite_connection = "sqlite:///{database_path}?check_same_thread=False"

# SQL Server连接字符串  
sqlserver_connection = "mssql+pyodbc://{username}:{password}@{server}:{port}/{database}?driver=ODBC+Driver+17+for+SQL+Server"
```

### 配置结构

```yaml
# SQLite配置
sqlite:
  path: "data"                           # 数据库目录
  filename_pattern: "database_{timestamp}.db"  # 文件名模式
  timestamp_format: "%Y%m%d_%H%M%S"     # 时间戳格式
  full_path: "完整数据库路径"             # 完整路径(优先级最高)
  sql_print: false                      # SQL语句打印开关

# SQL Server配置
sqlserver:
  enabled: false                        # 启用状态
  server: "localhost"                   # 服务器地址
  port: "1433"                         # 端口号
  database: ""                         # 数据库名
  username: ""                         # 用户名
  password: ""                         # 密码
```

## 接口规范

### DatabaseManager核心接口

#### 初始化接口
```python
def __new__(cls) -> DatabaseManager
    """
    单例模式构造函数
    确保全局只有一个数据库管理器实例
    """

def init_engines() -> None
    """
    初始化数据库引擎
    - 创建SQLite引擎
    - 根据配置创建SQL Server引擎
    - 设置连接参数和选项
    """

def init_sessions() -> None
    """
    初始化会话工厂
    - 创建SQLite会话工厂
    - 创建SQL Server会话工厂
    - 配置scoped_session
    """

def create_all_tables() -> None
    """
    创建所有数据表
    - 在SQLite中创建表结构
    - 在SQL Server中创建表结构
    - 基于SQLAlchemy元数据
    """
```

#### 会话管理接口
```python
def get_sqlite_session() -> Optional[Session]
    """
    获取SQLite数据库会话
    
    返回:
        SQLite会话实例，如果未初始化则返回None
    """

def get_sqlserver_session() -> Optional[Session]
    """
    获取SQL Server数据库会话
    
    返回:
        SQL Server会话实例，如果未启用或未初始化则返回None
    """

def close_all_sessions() -> None
    """
    关闭所有数据库会话
    - 关闭SQLite会话
    - 关闭SQL Server会话
    - 释放连接资源
    """
```

### 全局数据库接口

#### 初始化接口
```python
def init_db() -> None
    """
    初始化数据库系统
    - 初始化数据库引擎
    - 初始化会话工厂
    - 创建所有数据表
    """

def get_session() -> Session
    """
    获取主数据库会话(默认SQLite)
    
    返回:
        数据库会话实例
        
    异常:
        RuntimeError: 数据库初始化失败
    """

def close_all_sessions() -> None
    """
    关闭所有数据库会话
    """

def db_is_exist() -> bool
    """
    检查数据库是否存在
    
    返回:
        数据库存在状态
    """
```

#### 连接管理接口
```python
def get_connection_string() -> str
    """
    获取数据库连接字符串
    - 根据配置选择SQLite或SQL Server
    - 生成对应的连接字符串
    
    返回:
        数据库连接字符串
    """
```

### BaseModel数据库操作接口

#### CRUD操作接口
```python
@classmethod
def create(cls, data: Dict[str, Any]) -> Optional[T]
    """
    创建记录到双数据库
    
    参数:
        data: 记录数据字典
        
    返回:
        创建的模型实例，失败返回None
        
    功能:
        - 同时写入SQLite和SQL Server
        - 事务回滚处理
        - 错误日志记录
    """

@classmethod  
def update(cls, id: str, data: Dict[str, Any]) -> bool
    """
    更新双数据库中的记录
    
    参数:
        id: 记录ID
        data: 更新数据字典
        
    返回:
        更新是否成功
        
    功能:
        - 同时更新两个数据库
        - 事务一致性保证
        - 错误处理和回滚
    """

@classmethod
def delete(cls, id: str) -> bool
    """
    从双数据库删除记录
    
    参数:
        id: 记录ID
        
    返回:
        删除是否成功
        
    功能:
        - 软删除标记
        - 双数据库同步删除
        - 事务安全保证
    """

@classmethod
def get_by_id(cls, id: str) -> Optional[T]
    """
    根据ID获取记录
    
    参数:
        id: 记录ID
        
    返回:
        模型实例或None
        
    功能:
        - 优先从SQLite查询
        - 自动类型转换
        - 缓存优化
    """

@classmethod
def get_all(cls) -> List[T]
    """
    获取所有记录
    
    返回:
        模型实例列表
        
    功能:
        - 从主数据库查询
        - 分页支持
        - 排序和过滤
    """
```

## 业务规则

### 双数据库策略
- **SQLite作为主数据库**: 本地存储，快速访问，离线支持
- **SQL Server作为备份数据库**: 企业级存储，数据共享，集中管理
- **双写策略**: 数据同时写入两个数据库，确保数据一致性
- **读取优先级**: 优先从SQLite读取，提高响应速度

### 事务处理规则
- **独立事务**: 每个数据库维护独立的事务
- **最佳努力一致性**: 尽力保证两个数据库数据一致
- **错误隔离**: 一个数据库失败不影响另一个数据库操作
- **补偿机制**: 支持数据同步和修复

### 连接管理规则
- **连接池管理**: 使用SQLAlchemy连接池
- **会话作用域**: 使用scoped_session管理会话生命周期
- **资源释放**: 应用关闭时自动释放所有连接
- **重连机制**: 支持数据库连接断开重连

### 配置优先级规则
- **SQL Server启用检查**: 根据enabled字段决定是否启用
- **连接参数验证**: 启动时验证数据库连接参数
- **路径规范化**: 自动处理数据库路径格式
- **默认值处理**: 提供合理的配置默认值

## 关键约束条件

### 数据库约束
- SQLite数据库文件必须可读写
- SQL Server需要ODBC Driver 17支持
- 数据库表结构必须在两个数据库中保持一致
- 主键字段使用String类型，支持UUID

### 会话约束
- 会话不能跨线程使用
- 长时间运行的会话需要定期提交
- 会话异常时必须回滚
- 应用关闭前必须关闭所有会话

### 事务约束
- 每个操作都包装在事务中
- 事务失败时自动回滚
- 不支持跨数据库事务
- 批量操作需要手动事务管理

### 性能约束
- SQLite适合小到中等规模数据
- SQL Server适合大规模企业数据
- 避免长时间持有数据库锁
- 合理使用索引优化查询性能

## 扩展点设计

### 数据库类型扩展
```python
# 支持新数据库类型
def init_mysql_engine():
    """初始化MySQL引擎"""
    pass

def get_mysql_session():
    """获取MySQL会话"""
    pass
```

### 连接池扩展
```python
# 自定义连接池配置
engine = create_engine(
    connection_string,
    pool_size=20,           # 连接池大小
    max_overflow=30,        # 最大溢出连接
    pool_timeout=30,        # 连接超时
    pool_recycle=3600       # 连接回收时间
)
```

### 中间件扩展
```python
# 数据库操作中间件
class DatabaseMiddleware:
    def before_query(self, query):
        """查询前处理"""
        pass
    
    def after_query(self, result):
        """查询后处理"""
        pass
```

## 性能优化

### 连接优化
- 使用连接池减少连接开销
- 配置合适的连接池大小
- 启用连接回收机制
- 监控连接使用情况

### 查询优化
- 使用索引优化查询性能
- 避免N+1查询问题
- 合理使用懒加载和预加载
- 批量操作优化

### 内存优化
- 及时关闭不用的会话
- 避免大结果集一次性加载
- 使用流式查询处理大数据
- 定期清理会话缓存

## 安全考虑

### 连接安全
- 数据库密码加密存储
- 使用SSL连接SQL Server
- 限制数据库用户权限
- 定期更新数据库驱动

### 数据安全
- 敏感数据字段加密
- 审计日志记录
- 数据备份和恢复
- 访问权限控制

### 注入防护
- 使用参数化查询
- 输入数据验证和清理
- ORM层安全检查
- SQL注入检测和防护
