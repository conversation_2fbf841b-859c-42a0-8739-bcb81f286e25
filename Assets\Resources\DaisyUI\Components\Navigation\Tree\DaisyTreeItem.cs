using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    [UxmlElement]
    public partial class DaisyTreeItem : DaisyComponent
    {
        #region UXML Factory
        [UxmlAttribute]
        public string itemId { get; set; }

        [UxmlAttribute]
        public string text { get; set; }

        [UxmlAttribute]
        public string icon { get; set; }

        [UxmlAttribute]
        public bool selected { get; set; }

        [UxmlAttribute]
        public int depth { get; set; }
        #endregion

        #region Events
        public event Action<string> OnItemClicked;
        public event Action<string> OnItemSelected;
        public event Action<string> OnItemDeselected;
        public event Action<string, string> OnActionTriggered;
        #endregion

        #region Properties
        public string ItemId
        {
            get => itemId;
            set
            {
                itemId = value;
                name = $"tree-item-{value}";
            }
        }

        public string Text
        {
            get => text;
            set
            {
                text = value;
                UpdateText();
            }
        }

        public string Icon
        {
            get => icon;
            set
            {
                icon = value;
                UpdateIcon();
            }
        }

        public bool Selected
        {
            get => selected;
            set
            {
                selected = value;
                UpdateSelection();
            }
        }

        public int Depth
        {
            get => depth;
            set
            {
                depth = value;
                UpdateDepth();
            }
        }

        public DaisyTreeData Data { get; set; }
        public List<DaisyTreeAction> Actions { get; set; } = new List<DaisyTreeAction>();
        #endregion

        #region UI Elements
        private VisualElement _container;
        private VisualElement _content;
        private VisualElement _indentContainer;
        private Label _iconLabel;
        private Label _textLabel;
        private VisualElement _actionsContainer;
        private Button _mainButton;
        #endregion

        #region Template and Styling
        protected override string TemplatePath => "DaisyUI/Components/Navigation/Tree/DaisyTreeItem";
        #endregion

        #region Constructors
        public DaisyTreeItem() : base(DaisyUtilities.ComponentTypes.TreeItem)
        {
            ComponentType = "tree-item";
            InitializeComponent();
        }

        public DaisyTreeItem(string id, string text, string icon = null) : this()
        {
            ItemId = id;
            Text = text;
            Icon = icon;
        }

        public DaisyTreeItem(DaisyTreeData data) : this()
        {
            Data = data;
            ItemId = data.Id;
            Text = data.Text;
            Icon = data.Icon;
            Selected = data.IsSelected;
            Actions = data.Actions;
        }
        #endregion

        #region Factory Methods
        public static DaisyTreeItem Create(string id, string text, string icon = null)
        {
            return new DaisyTreeItem(id, text, icon);
        }

        public static DaisyTreeItem Create(DaisyTreeData data)
        {
            return new DaisyTreeItem(data);
        }
        #endregion

        #region Initialization
        private void InitializeComponent()
        {
            AddToClassList("daisy-tree-item");
        }

        protected override void OnInitialize()
        {
            base.OnInitialize();
            CacheElements();
            UpdateDisplay();
        }

        protected override void SetupEventHandlers()
        {
            base.SetupEventHandlers();

            if (_mainButton != null)
            {
                _mainButton.clicked += OnMainButtonClicked;
            }

            this.RegisterCallback<KeyDownEvent>(OnKeyDown);
            this.RegisterCallback<FocusInEvent>(OnFocusIn);
            this.RegisterCallback<FocusOutEvent>(OnFocusOut);
        }

        private void CacheElements()
        {
            _container = this.Q("container");
            _content = this.Q("content");
            _indentContainer = this.Q("indent-container");
            _iconLabel = this.Q<Label>("icon");
            _textLabel = this.Q<Label>("text");
            _actionsContainer = this.Q("actions-container");
            _mainButton = this.Q<Button>("main-button");
        }

        #endregion

        #region Event Handlers
        private void OnMainButtonClicked()
        {
            OnItemClicked?.Invoke(ItemId);

            if (!Selected)
            {
                SetSelected(true);
            }
        }

        private void OnKeyDown(KeyDownEvent evt)
        {
            switch (evt.keyCode)
            {
                case KeyCode.Return:
                case KeyCode.KeypadEnter:
                case KeyCode.Space:
                    OnMainButtonClicked();
                    evt.StopPropagation();
                    break;
            }
        }

        private void OnFocusIn(FocusInEvent evt)
        {
            AddToClassList("focus");
        }

        private void OnFocusOut(FocusOutEvent evt)
        {
            RemoveFromClassList("focus");
        }

        private void OnActionButtonClicked(string actionId)
        {
            OnActionTriggered?.Invoke(ItemId, actionId);
            Data?.TriggerAction(actionId);
        }
        #endregion

        #region Display Updates
        private void UpdateDisplay()
        {
            UpdateText();
            UpdateIcon();
            UpdateSelection();
            UpdateDepth();
            UpdateActions();
        }

        private void UpdateText()
        {
            if (_textLabel != null && !string.IsNullOrEmpty(Text))
            {
                _textLabel.text = Text;
                _textLabel.style.display = DisplayStyle.Flex;
            }
            else if (_textLabel != null)
            {
                _textLabel.style.display = DisplayStyle.None;
            }
        }

        private void UpdateIcon()
        {
            if (_iconLabel != null && !string.IsNullOrEmpty(Icon))
            {
                _iconLabel.text = Icon;
                _iconLabel.style.display = DisplayStyle.Flex;
                _iconLabel.AddToClassList("daisy-icon");
            }
            else if (_iconLabel != null)
            {
                _iconLabel.style.display = DisplayStyle.None;
            }
        }

        private void UpdateSelection()
        {
            if (Selected)
            {
                AddToClassList("selected");
                RemoveFromClassList("deselected");
            }
            else
            {
                RemoveFromClassList("selected");
                AddToClassList("deselected");
            }
        }

        private void UpdateDepth()
        {
            if (_indentContainer != null)
            {
                _indentContainer.Clear();

                for (int i = 0; i < Depth; i++)
                {
                    var indent = new VisualElement();
                    indent.AddToClassList("tree-indent");
                    _indentContainer.Add(indent);
                }
            }

            // Update depth CSS class
            RemoveFromClassList("depth-0");
            RemoveFromClassList("depth-1");
            RemoveFromClassList("depth-2");
            RemoveFromClassList("depth-3");
            RemoveFromClassList("depth-4");
            RemoveFromClassList("depth-5");

            AddToClassList($"depth-{Math.Min(Depth, 5)}");
        }

        private void UpdateActions()
        {
            if (_actionsContainer == null) return;

            _actionsContainer.Clear();

            foreach (var action in Actions)
            {
                if (!action.IsVisible) continue;

                var actionButton = new Button();
                actionButton.name = $"action-{action.Id}";
                actionButton.AddToClassList("tree-action-button");

                if (!string.IsNullOrEmpty(action.Icon))
                {
                    actionButton.text = action.Icon;
                    actionButton.AddToClassList("daisy-icon");
                }

                if (!string.IsNullOrEmpty(action.Tooltip))
                {
                    actionButton.tooltip = action.Tooltip;
                }

                actionButton.SetEnabled(action.IsEnabled);
                actionButton.clicked += () => OnActionButtonClicked(action.Id);

                _actionsContainer.Add(actionButton);
            }
        }
        #endregion

        #region Public Methods
        public DaisyTreeItem SetSelected(bool selected)
        {
            if (Selected != selected)
            {
                Selected = selected;

                if (selected)
                {
                    OnItemSelected?.Invoke(ItemId);
                }
                else
                {
                    OnItemDeselected?.Invoke(ItemId);
                }
            }

            return this;
        }

        public DaisyTreeItem SetText(string text)
        {
            Text = text;
            return this;
        }

        public DaisyTreeItem SetIcon(string icon)
        {
            Icon = icon;
            return this;
        }

        public DaisyTreeItem SetDepth(int depth)
        {
            Depth = depth;
            return this;
        }

        public DaisyTreeItem AddAction(string actionId, string tooltip = null, string icon = null)
        {
            var action = new DaisyTreeAction(actionId, tooltip, icon);
            Actions.Add(action);
            UpdateActions();
            return this;
        }

        public DaisyTreeItem AddAction(DaisyTreeAction action)
        {
            if (action != null)
            {
                Actions.Add(action);
                UpdateActions();
            }
            return this;
        }

        public DaisyTreeItem RemoveAction(string actionId)
        {
            var action = Actions.Find(a => a.Id == actionId);
            if (action != null)
            {
                Actions.Remove(action);
                UpdateActions();
            }
            return this;
        }

        public DaisyTreeItem ClearActions()
        {
            Actions.Clear();
            UpdateActions();
            return this;
        }

        public DaisyTreeItem OnClick(Action<string> callback)
        {
            OnItemClicked += callback;
            return this;
        }

        public DaisyTreeItem OnSelected(Action<string> callback)
        {
            OnItemSelected += callback;
            return this;
        }

        public DaisyTreeItem OnDeselected(Action<string> callback)
        {
            OnItemDeselected += callback;
            return this;
        }

        public DaisyTreeItem OnAction(Action<string, string> callback)
        {
            OnActionTriggered += callback;
            return this;
        }

        public DaisyTreeItem Focus()
        {
            _mainButton?.Focus();
            return this;
        }

        public DaisyTreeItem Blur()
        {
            _mainButton?.Blur();
            return this;
        }
        #endregion

        #region Theme and Styling
        public DaisyTreeItem SetTheme(DaisyTheme theme)
        {
            Theme = theme;
            return this;
        }

        public DaisyTreeItem SetSize(string size)
        {
            Size = size;
            return this;
        }

        public DaisyTreeItem SetVariant(string variant)
        {
            Variant = variant;
            return this;
        }

        public DaisyTreeItem SetCompact(bool compact = true)
        {
            SetModifier("compact", compact);
            return this;
        }

        public DaisyTreeItem SetBordered(bool bordered = true)
        {
            SetModifier("bordered", bordered);
            return this;
        }

        public DaisyTreeItem SetRounded(bool rounded = true)
        {
            SetModifier("rounded", rounded);
            return this;
        }
        #endregion

        #region Data Binding
        public DaisyTreeItem BindData(DaisyTreeData data)
        {
            if (data != null)
            {
                Data = data;
                ItemId = data.Id;
                Text = data.Text;
                Icon = data.Icon;
                Selected = data.IsSelected;
                Actions = data.Actions;

                // Subscribe to data events
                data.OnItemSelected += (id) => SetSelected(true);
                data.OnItemAction += (id, actionId) => OnActionTriggered?.Invoke(id, actionId);

                UpdateDisplay();
            }

            return this;
        }

        public DaisyTreeItem UnbindData()
        {
            Data = null;
            return this;
        }
        #endregion

        #region Utility Methods
        public override void Reset()
        {
            base.Reset();

            ItemId = null;
            Text = null;
            Icon = null;
            Selected = false;
            Depth = 0;
            Actions.Clear();
            Data = null;

            OnItemClicked = null;
            OnItemSelected = null;
            OnItemDeselected = null;
            OnActionTriggered = null;
        }

        public override string ToString()
        {
            return $"DaisyTreeItem(Id: {ItemId}, Text: {Text}, Selected: {Selected})";
        }
        #endregion
    }
}