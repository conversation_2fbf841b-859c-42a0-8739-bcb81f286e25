# 图标配置示例

## 工具栏图标配置示例

### 在ToolbarConfig.asset中的配置
```yaml
toolGroups:
- name: selection
  displayName: "选择工具"
  buttons:
  - name: select-tool
    displayName: "选择"
    iconName: select-icon          # 对应CSS类名
    tooltip: "选择工具"
  - name: move-tool
    displayName: "移动"
    iconName: move-icon            # 对应CSS类名
    tooltip: "移动工具"
  - name: rotate-tool
    displayName: "旋转"
    iconName: rotate-icon          # 对应CSS类名
    tooltip: "旋转工具"
  - name: scale-tool
    displayName: "缩放"
    iconName: scale-icon           # 对应CSS类名
    tooltip: "缩放工具"

- name: view
  displayName: "视图工具"
  buttons:
  - name: pan-tool
    displayName: "平移"
    iconName: pan-icon             # 对应CSS类名
    tooltip: "平移视图"
  - name: zoom-tool
    displayName: "缩放"
    iconName: zoom-icon            # 对应CSS类名
    tooltip: "缩放视图"

- name: playback
  displayName: "播放控制"
  buttons:
  - name: play-button
    displayName: "播放"
    iconName: play-icon            # 对应CSS类名
    tooltip: "播放/暂停"
    isToggle: true
```

### 对应的CSS样式 (Toolbar.uss)
```css
/* 基础图标样式 */
.tool-icon {
    width: 16px;
    height: 16px;
    background-color: rgb(210, 210, 210);
    margin-bottom: 2px;
    flex-shrink: 0;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 选择工具图标 */
.select-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/select-icon.png');
}

/* 移动工具图标 */
.move-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/move-icon.png');
}

/* 旋转工具图标 */
.rotate-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/rotate-icon.png');
}

/* 缩放工具图标 */
.scale-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/scale-icon.png');
}

/* 平移工具图标 */
.pan-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/pan-icon.png');
}

/* 缩放视图图标 */
.zoom-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/zoom-icon.png');
}

/* 播放图标 */
.play-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/play-icon.png');
}

/* 暂停图标 */
.pause-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Toolbar/pause-icon.png');
}

/* 选中状态样式 */
.tool-button.selected .tool-icon {
    background-color: rgb(255, 255, 255);
    filter: brightness(1.2);
}

/* 禁用状态样式 */
.tool-button:disabled .tool-icon {
    background-color: rgb(128, 128, 128);
    opacity: 0.5;
}

/* 悬停效果 */
.tool-button:hover .tool-icon {
    background-color: rgb(230, 230, 230);
}
```

## 菜单栏图标配置示例

### 在MenuBarConfig.asset中的配置
```yaml
categories:
- name: file
  displayName: "文件"
  items:
  - name: new-scene
    displayName: "新建场景"
    iconName: file-new-icon        # 对应CSS类名
    shortcut: Ctrl+N
    callbackName: NewScene
  - name: open-scene
    displayName: "打开场景"
    iconName: file-open-icon       # 对应CSS类名
    shortcut: Ctrl+O
    callbackName: OpenScene
  - name: save-scene
    displayName: "保存场景"
    iconName: file-save-icon       # 对应CSS类名
    shortcut: Ctrl+S
    callbackName: SaveScene
  - name: separator-1
    isSeparator: true
  - name: exit
    displayName: "退出"
    iconName: file-exit-icon       # 对应CSS类名
    shortcut: Alt+F4
    callbackName: ExitApplication

- name: edit
  displayName: "编辑"
  items:
  - name: undo
    displayName: "撤销"
    iconName: edit-undo-icon       # 对应CSS类名
    shortcut: Ctrl+Z
    callbackName: Undo
  - name: redo
    displayName: "重做"
    iconName: edit-redo-icon       # 对应CSS类名
    shortcut: Ctrl+Y
    callbackName: Redo
  - name: separator-2
    isSeparator: true
  - name: copy
    displayName: "复制"
    iconName: edit-copy-icon       # 对应CSS类名
    shortcut: Ctrl+C
    callbackName: Copy
  - name: paste
    displayName: "粘贴"
    iconName: edit-paste-icon      # 对应CSS类名
    shortcut: Ctrl+V
    callbackName: Paste

- name: view
  displayName: "视图"
  items:
  - name: zoom-in
    displayName: "放大"
    iconName: view-zoom-in-icon    # 对应CSS类名
    shortcut: Ctrl+Plus
    callbackName: ZoomIn
  - name: zoom-out
    displayName: "缩小"
    iconName: view-zoom-out-icon   # 对应CSS类名
    shortcut: Ctrl+Minus
    callbackName: ZoomOut
  - name: fit-view
    displayName: "适合视图"
    iconName: view-fit-icon        # 对应CSS类名
    shortcut: F
    callbackName: FitView
```

### 对应的CSS样式 (MenuBar.uss)
```css
/* 菜单项图标基础样式 */
.menu-item-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-color: rgb(210, 210, 210);
    flex-shrink: 0;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 文件菜单图标 */
.file-new-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/file-new.png');
}

.file-open-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/file-open.png');
}

.file-save-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/file-save.png');
}

.file-exit-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/file-exit.png');
}

/* 编辑菜单图标 */
.edit-undo-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/edit-undo.png');
}

.edit-redo-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/edit-redo.png');
}

.edit-copy-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/edit-copy.png');
}

.edit-paste-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/edit-paste.png');
}

/* 视图菜单图标 */
.view-zoom-in-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/view-zoom-in.png');
}

.view-zoom-out-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/view-zoom-out.png');
}

.view-fit-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/MenuBar/view-fit.png');
}

/* 菜单项悬停效果 */
.dropdown-item:hover .menu-item-icon {
    background-color: rgb(255, 255, 255);
}

/* 菜单项禁用状态 */
.dropdown-item:disabled .menu-item-icon {
    background-color: rgb(128, 128, 128);
    opacity: 0.5;
}
```

## 通用图标样式

### 状态栏和面板图标
```css
/* 状态栏图标 */
.status-icon {
    width: 12px;
    height: 12px;
    background-color: rgb(190, 190, 190);
    margin-right: 4px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 面板按钮图标 */
.panel-icon {
    width: 12px;
    height: 12px;
    background-color: rgb(190, 190, 190);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* 通用图标样式 */
.close-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Common/close.png');
}

.settings-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Common/settings.png');
}

.refresh-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Common/refresh.png');
}

.collapse-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Common/collapse.png');
}

.expand-icon {
    background-image: url('project://database/Assets/UI%20Toolkit/Icons/Common/expand.png');
}
```

## 响应式图标配置

### 不同屏幕尺寸的图标适配
```css
/* 默认尺寸 (大屏幕) */
.tool-icon {
    width: 16px;
    height: 16px;
}

.menu-item-icon {
    width: 16px;
    height: 16px;
}

/* 中等屏幕 */
@media (max-width: 1200px) {
    .tool-icon {
        width: 14px;
        height: 14px;
    }
    
    .menu-item-icon {
        width: 14px;
        height: 14px;
    }
}

/* 小屏幕 */
@media (max-width: 800px) {
    .tool-icon {
        width: 12px;
        height: 12px;
    }
    
    .menu-item-icon {
        width: 12px;
        height: 12px;
    }
}
```

## 动态图标切换示例

### C# 代码中的图标管理
```csharp
// 工具栏中动态切换播放/暂停图标
private void UpdatePlayButtonIcon(bool isPlaying)
{
    var playButton = toolGroups.Q<Button>("play-button");
    var icon = playButton?.Q<VisualElement>("tool-icon");
    
    if (icon != null)
    {
        // 移除所有播放相关的图标类
        icon.RemoveFromClassList("play-icon");
        icon.RemoveFromClassList("pause-icon");
        
        // 根据状态添加对应的图标类
        if (isPlaying)
        {
            icon.AddToClassList("pause-icon");
            playButton.tooltip = "暂停";
        }
        else
        {
            icon.AddToClassList("play-icon");
            playButton.tooltip = "播放";
        }
    }
}

// 菜单项中根据状态显示不同图标
private void UpdateMenuItemIcon(string itemName, string iconName)
{
    var menuItem = FindMenuItemByName(itemName);
    var icon = menuItem?.Q<VisualElement>("menu-item-icon");
    
    if (icon != null)
    {
        // 移除所有图标类
        foreach (var className in icon.GetClasses().ToList())
        {
            if (className.EndsWith("-icon"))
            {
                icon.RemoveFromClassList(className);
            }
        }
        
        // 添加新的图标类
        icon.AddToClassList(iconName);
    }
}
```

---

*此示例展示了完整的图标配置流程，从ScriptableObject配置到CSS样式定义，再到运行时的动态管理。*
