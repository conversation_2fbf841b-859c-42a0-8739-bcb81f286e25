<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <ui:Template name="DaisyModal" src="project://database/Assets/Resources/DaisyUI/Components/Navigation/Modal/DaisyModal.uxml">
        <Style src="project://database/Assets/Resources/DaisyUI/Components/Navigation/Modal/DaisyModal.uss" />
        <ui:VisualElement name="daisy-modal" class="daisy-modal">
            <ui:VisualElement name="modal-backdrop" class="modal-backdrop" />
            <ui:VisualElement name="modal-box" class="modal-box">
                <ui:Label text="Modal Title" name="modal-title" class="modal-title" />
                <ui:Label text="Modal content goes here..." name="modal-content" class="modal-content" />
                <ui:VisualElement name="modal-actions" class="modal-actions">
                    <ui:Button text="Cancel" name="modal-cancel" class="btn btn-ghost" />
                    <ui:Button text="OK" name="modal-ok" class="btn btn-primary" />
                </ui:VisualElement>
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:Template>
</ui:UXML>