using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 模态窗口调整尺寸功能测试
    /// </summary>
    public class ModalWindowResizeTest : MonoBehaviour
    {
        [Header("Test Settings")]
        public bool enableTest = true;
        public KeyCode testKey = KeyCode.F1;

        private ModalWindow testWindow;
        private ModalWindowManager windowManager;

        void Start()
        {
            if (!enableTest) return;

            // 获取窗口管理器
            windowManager = ModalWindowManager.Instance;
            if (windowManager == null)
            {
                Debug.LogError("ModalWindowManager not found!");
                return;
            }
        }

        void Update()
        {
            if (!enableTest) return;

            if (Input.GetKeyDown(testKey))
            {
                TestResizableWindow();
            }

            // 添加ESC键关闭窗口
            if (Input.GetKeyDown(KeyCode.Escape) && testWindow != null)
            {
                testWindow.CloseWindow();
                testWindow = null;
            }
        }

        /// <summary>
        /// 测试可调整尺寸的窗口
        /// </summary>
        private void TestResizableWindow()
        {
            if (testWindow != null)
            {
                testWindow.CloseWindow();
                testWindow = null;
                return;
            }

            // 创建测试窗口
            testWindow = windowManager.CreateWindow("调整尺寸测试", new Vector2(500, 400));

            if (testWindow != null)
            {
                // 确保窗口可以调整尺寸
                testWindow.IsResizable = true;

                // 设置尺寸限制
                testWindow.MinSize = new Vector2(300, 200);
                testWindow.MaxSize = new Vector2(800, 600);

                // 添加测试内容
                CreateTestContent(testWindow);

                Debug.Log("调整尺寸测试窗口已创建。拖拽窗口边框或右下角来调整尺寸。");
            }
        }

        /// <summary>
        /// 创建测试内容
        /// </summary>
        private void CreateTestContent(ModalWindow window)
        {
            var content = new VisualElement();
            content.style.paddingTop = 20;
            content.style.paddingBottom = 20;
            content.style.paddingLeft = 20;
            content.style.paddingRight = 20;
            content.style.flexDirection = FlexDirection.Column;

            // 标题
            var title = new Label("窗口尺寸调整测试");
            title.style.fontSize = 16;
            title.style.unityFontStyleAndWeight = FontStyle.Bold;
            title.style.color = new Color(0.9f, 0.9f, 0.9f);
            title.style.marginBottom = 10;
            content.Add(title);

            // 说明文字
            var description = new Label("拖拽窗口的边框或右下角来调整窗口尺寸：\n" +
                                      "• 右边框：水平调整\n" +
                                      "• 底边框：垂直调整\n" +
                                      "• 右下角：双向调整");
            description.style.color = new Color(0.8f, 0.8f, 0.8f);
            description.style.whiteSpace = WhiteSpace.Normal;
            description.style.marginBottom = 10;
            content.Add(description);

            // 当前尺寸显示
            var sizeLabel = new Label($"当前尺寸: {window.Size.x:F0} x {window.Size.y:F0}");
            sizeLabel.style.color = new Color(0.7f, 0.9f, 0.7f);
            sizeLabel.style.marginBottom = 10;
            sizeLabel.name = "size-label";
            content.Add(sizeLabel);

            // 尺寸限制显示
            var limitsLabel = new Label($"尺寸限制: {window.MinSize.x:F0}-{window.MaxSize.x:F0} x {window.MinSize.y:F0}-{window.MaxSize.y:F0}");
            limitsLabel.style.color = new Color(0.9f, 0.7f, 0.7f);
            limitsLabel.style.marginBottom = 10;
            content.Add(limitsLabel);

            // 状态显示
            var statusLabel = new Label("状态: 正常");
            statusLabel.style.color = new Color(0.7f, 0.7f, 0.9f);
            statusLabel.style.marginBottom = 10;
            statusLabel.name = "status-label";
            content.Add(statusLabel);

            // 测试按钮
            var buttonContainer = new VisualElement();
            buttonContainer.style.flexDirection = FlexDirection.Row;
            buttonContainer.style.marginTop = 20;

            var resetSizeButton = new Button(() =>
            {
                window.Size = new Vector2(500, 400);
                UpdateSizeLabel(window, sizeLabel);
            })
            { text = "重置尺寸" };
            resetSizeButton.style.minWidth = 80;
            resetSizeButton.style.marginRight = 10;
            buttonContainer.Add(resetSizeButton);

            var toggleResizeButton = new Button { text = "禁用调整" };
            toggleResizeButton.clicked += () =>
            {
                window.IsResizable = !window.IsResizable;
                toggleResizeButton.text = window.IsResizable ? "禁用调整" : "启用调整";
            };
            toggleResizeButton.style.minWidth = 80;
            toggleResizeButton.style.marginRight = 10;
            buttonContainer.Add(toggleResizeButton);

            // 紧急重置按钮
            var emergencyResetButton = new Button { text = "紧急重置" };
            emergencyResetButton.clicked += () =>
            {
                // 强制重置窗口状态
                window.Size = new Vector2(500, 400);
                window.Position = new Vector2(100, 100);
                window.IsResizable = true;
                toggleResizeButton.text = "禁用调整";
                UpdateSizeLabel(window, sizeLabel);
                Debug.Log("窗口状态已紧急重置");
            };
            emergencyResetButton.style.minWidth = 80;
            emergencyResetButton.style.backgroundColor = new Color(0.8f, 0.3f, 0.3f);
            buttonContainer.Add(emergencyResetButton);

            content.Add(buttonContainer);

            // 设置内容
            window.SetContent(content);

            // 定期更新尺寸显示
            var statusLabelRef = content.Q<Label>("status-label");
            StartCoroutine(UpdateSizeDisplay(window, sizeLabel, statusLabelRef));
        }

        /// <summary>
        /// 更新尺寸显示
        /// </summary>
        private System.Collections.IEnumerator UpdateSizeDisplay(ModalWindow window, Label sizeLabel, Label statusLabel)
        {
            while (window != null && sizeLabel != null)
            {
                UpdateSizeLabel(window, sizeLabel);
                UpdateStatusLabel(window, statusLabel);
                yield return new WaitForSeconds(0.1f);
            }
        }

        /// <summary>
        /// 更新尺寸标签
        /// </summary>
        private void UpdateSizeLabel(ModalWindow window, Label sizeLabel)
        {
            if (window != null && sizeLabel != null)
            {
                sizeLabel.text = $"当前尺寸: {window.Size.x:F0} x {window.Size.y:F0}";
            }
        }

        /// <summary>
        /// 更新状态标签
        /// </summary>
        private void UpdateStatusLabel(ModalWindow window, Label statusLabel)
        {
            if (window == null || statusLabel == null) return;

            string status = "状态: 正常";
            var color = new Color(0.7f, 0.9f, 0.7f);

            // 这里可以添加更多状态检查
            // 由于无法直接访问私有字段，我们通过其他方式判断状态
            if (!window.IsResizable)
            {
                status = "状态: 调整尺寸已禁用";
                color = new Color(0.9f, 0.9f, 0.7f);
            }

            statusLabel.text = status;
            statusLabel.style.color = color;
        }

        void OnDestroy()
        {
            testWindow?.CloseWindow();
        }
    }
}
