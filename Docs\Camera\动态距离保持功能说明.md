# 动态距离保持功能说明

## 功能概述

动态距离保持功能实现了相机与屏幕中心射线交点的恒定距离保持。当用户平移相机时，系统会自动调整相机位置，确保相机始终与屏幕中心指向的地面/物体表面保持相同的距离。

## 核心特性

### 1. 距离计算与存储

**功能描述**：
- 实时计算并存储相机到屏幕中心射线与地面/物体表面交点的距离
- 基于优先级射线检测：Cesium模型 → GridPlane → 物理物体 → 数学基面
- 动态更新存储距离值

**配置参数**：
- `useDynamicDistanceKeeping` (bool): 是否启用动态距离保持功能，默认为 true
- `distanceKeepingSmoothness` (float): 距离保持的平滑度，默认为 0.2

### 2. 平移时的距离保持

**功能描述**：
- 平移时动态调整相机位置以保持与新交点的恒定距离
- 确保平移过程中相机始终保持正确的高度和距离关系
- 支持平滑插值避免位置变化过于突兀

**工作原理**：
1. 计算平移后的基础相机位置
2. 获取新位置的屏幕中心射线交点
3. 根据存储的距离计算应该的相机位置
4. 应用平滑插值得到最终位置

### 3. 距离值的更新时机

**自动更新场景**：
- **缩放操作时**：根据缩放量更新存储的距离值
- **QE键垂直移动时**：重新计算并更新相机到交点的距离
- **系统初始化时**：设置初始的存储距离

**保持不变场景**：
- **旋转操作时**：保持当前距离不变，只改变相机朝向
- **水平平移时**：通过调整高度保持距离恒定

## 技术实现

### 新增方法

#### InputManager.RaycastUtils.cs
- `UpdateStoredScreenCenterDistance()`: 计算并更新存储的屏幕中心距离
- `GetScreenCenterHitPoint()`: 获取屏幕中心射线的交点
- `CalculateDistanceKeepingPosition()`: 计算为了保持距离，相机应该调整到的位置
- `ForceUpdateDistanceKeeping()`: 强制更新距离保持系统

#### InputManager.ViewportControl.cs
- `HandlePanningWithDistanceKeeping()`: 带动态距离保持的平移处理
- `HandlePanningLegacy()`: 原有的平移逻辑（向后兼容）

### 核心算法

```csharp
// 距离保持计算
Vector3 directionToCamera = (currentCameraPosition - newHitPoint).normalized;
Vector3 targetPosition = newHitPoint + directionToCamera * storedScreenCenterDistance;

// 平滑插值
if (distanceKeepingSmoothness > 0)
{
    finalPosition = Vector3.Lerp(basePosition, targetPosition, 
                                distanceKeepingSmoothness * Time.deltaTime * 10f);
}
```

## 使用方法

### 在 Inspector 中配置

1. 找到 InputManager 组件
2. 在 "缩放设置" 部分：
   - 勾选 `Use Dynamic Distance Keeping` 启用动态距离保持
   - 调整 `Distance Keeping Smoothness` 控制平滑度

### 代码中控制

```csharp
// 启用/禁用动态距离保持
inputManager.useDynamicDistanceKeeping = true;

// 调整距离保持平滑度
inputManager.distanceKeepingSmoothness = 0.3f;

// 强制更新存储距离
inputManager.ForceUpdateDistanceKeeping();
```

## 测试工具

### DynamicDistanceKeepingTester

专门用于测试动态距离保持功能的测试组件：

**快捷键控制**：
- `F5` - 切换动态距离保持功能
- `F6` - 重置存储的距离值
- `F7` - 创建测试对象

**GUI控制面板**：
- 实时显示存储距离和当前距离
- 滑块调节平滑度参数
- 按钮控制功能开关

**测试对象**：
- 自动创建多个不同高度的立方体
- 用于测试距离保持在复杂地形上的效果

## 使用场景

### 适用情况

1. **地形浏览**：在复杂地形上平移时保持一致的观察距离
2. **建筑检查**：围绕建筑物平移时保持恒定的观察距离
3. **Cesium模型**：在倾斜摄影模型上保持稳定的视角距离
4. **精确控制**：需要精确控制相机与目标距离的应用

### 不适用情况

1. **自由飞行**：需要完全自由移动的场景
2. **快速浏览**：需要快速大范围移动的场景
3. **2D视角**：固定俯视角度的应用

## 配置建议

### 基础配置

```csharp
// 启用动态距离保持
inputManager.useDynamicDistanceKeeping = true;

// 设置适中的平滑度
inputManager.distanceKeepingSmoothness = 0.2f;

// 确保地形自适应功能也启用
inputManager.useTerrainAdaptiveZoom = true;
inputManager.useTerrainAdaptivePan = true;
```

### 针对不同场景的调优

**复杂地形场景**：
```csharp
inputManager.distanceKeepingSmoothness = 0.3f; // 更平滑的跟随
inputManager.terrainFollowSmoothness = 0.15f;  // 地形跟随也要平滑
```

**精确控制场景**：
```csharp
inputManager.distanceKeepingSmoothness = 0.1f; // 更快的响应
inputManager.adaptiveMinZoomDistance = 0.3f;   // 允许更近距离
```

**Cesium模型场景**：
```csharp
inputManager.distanceKeepingSmoothness = 0.25f; // 平衡响应和稳定性
inputManager.showDebugRaycast = true;           // 启用调试信息
```

## 调试功能

### 调试信息

启用 `showDebugRaycast = true` 可以查看：
- 存储距离的更新过程
- 屏幕中心射线的交点计算
- 距离保持位置的计算结果

### 测试验证

1. **基础测试**：
   - 在平坦地面上平移，观察相机高度是否保持恒定
   - 缩放后平移，验证新距离是否正确保持

2. **复杂地形测试**：
   - 在高度变化的地形上平移
   - 验证相机是否跟随地形高度变化

3. **性能测试**：
   - 在复杂场景中测试射线检测性能
   - 观察平滑插值的效果

## 注意事项

1. **性能考虑**：每次平移都会进行射线检测，复杂场景可能影响性能
2. **平滑度设置**：过高的平滑度可能导致响应迟缓，过低可能产生抖动
3. **射线检测失败**：在没有有效交点的区域，功能会自动禁用
4. **与其他功能的兼容性**：与地形自适应功能配合使用效果最佳

## 故障排除

### 常见问题

1. **距离保持不生效**
   - 检查 `useDynamicDistanceKeeping` 是否启用
   - 确认射线检测能够找到有效交点

2. **相机移动过于突兀**
   - 增加 `distanceKeepingSmoothness` 值
   - 检查地形高度变化是否过于剧烈

3. **距离计算不准确**
   - 确认图层掩码设置正确
   - 检查Cesium模型是否在正确图层

4. **性能问题**
   - 考虑降低射线检测频率
   - 在复杂场景中适当调整参数

## 总结

动态距离保持功能为相机控制系统提供了更加智能和用户友好的平移体验。通过自动保持相机与目标表面的恒定距离，用户可以更轻松地在复杂地形上进行精确的视角控制，特别适用于Cesium倾斜摄影模型等需要精确距离控制的应用场景。
