/* 顶部工具栏根容器 */
.top-toolbar {
    flex-direction: row;
    background-color: rgba(64, 64, 64, 0.95);
    border-bottom-width: 1px;
    border-bottom-color: rgb(32, 32, 32);
    height: 40px;
    min-height: 40px;
    align-items: center;
    padding: 0 8px;
}

/* 菜单栏容器 */
.menu-bar-container {
    flex-direction: row;
    align-items: center;
    height: 100%;
}

/* 工具栏容器 */
.toolbar-container {
    flex-direction: row;
    align-items: center;
    height: 100%;
    flex-grow: 1;
}


/* 菜单按钮 */
.menu-button {
    background-color: transparent;
    border-width: 0;
    color: rgb(210, 210, 210);
    padding: 6px 12px;
    margin: 0 2px;
    -unity-text-align: middle-center;
    font-size: 12px;
    border-radius: 3px;
    min-width: 50px;
    /* 添加过渡效果 */
    transition-duration: 0.1s;
    transition-property: background-color, color;
}

.menu-button:hover {
    background-color: rgba(96, 96, 96, 0.8);
}

.menu-button:active {
    background-color: rgba(48, 48, 48, 0.8);
}

/* 菜单按钮激活状态（下拉菜单打开时） */
.menu-button.active {
    background-color: rgba(62, 95, 150, 0.8);
    color: rgb(255, 255, 255);
}

/* 工具栏分隔符 */
.toolbar-separator {
    width: 1px;
    height: 24px;
    background-color: rgb(96, 96, 96);
    margin: 0 8px;
}

/* 工具组容器 */
.tool-groups {
    flex-direction: row;
    align-items: center;
    flex-grow: 1;
}

/* 工具组 */
.tool-group {
    flex-direction: row;
    align-items: center;
    margin: 0 4px;
}

/* 工具按钮 */
.tool-button {
    background-color: transparent;
    border-width: 1px;
    border-color: transparent;
    border-radius: 4px;
    padding: 4px 6px;
    margin: 0 2px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 32px;
    color: rgb(210, 210, 210);
}

.tool-button:hover {
    background-color: rgba(96, 96, 96, 0.6);
    border-color: rgba(128, 128, 128, 0.8);
}

.tool-button:active {
    background-color: rgba(48, 48, 48, 0.8);
    border-color: rgb(58, 121, 187);
}

/* 选中状态的工具按钮 */
.tool-button.selected {
    background-color: rgba(58, 121, 187, 0.3);
    border-color: rgb(58, 121, 187);
}

/* 工具图标 */
.tool-icon {
    width: 16px;
    height: 16px;
    background-color: rgb(210, 210, 210);
    margin-bottom: 2px;
}

/* 具体工具图标样式 */
.select-icon {
    /* 这里可以设置选择工具的图标背景图片 */
    background-image: none;
}

.move-icon {
    /* 移动工具图标 */
    background-image: none;
}

.rotate-icon {
    /* 旋转工具图标 */
    background-image: none;
}

.scale-icon {
    /* 缩放工具图标 */
    background-image: none;
}

.cube-icon {
    /* 立方体图标 */
    background-image: none;
}

.sphere-icon {
    /* 球体图标 */
    background-image: none;
}

.plane-icon {
    /* 平面图标 */
    background-image: none;
}

.camera-icon {
    /* 相机图标 */
    background-image: none;
}

.focus-icon {
    /* 聚焦图标 */
    background-image: none;
}

.play-icon {
    /* 播放图标 */
    background-image: none;
}

/* 工具标签 */
.tool-label {
    font-size: 9px;
    color: rgb(190, 190, 190);
    -unity-text-align: middle-center;
    margin: 0;
    padding: 0;
}

/* 右侧工具区域 */
.right-tools {
    flex-direction: row;
    align-items: center;
}

/* 播放按钮特殊样式 */
.play-button {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.5);
}

.play-button:hover {
    background-color: rgba(76, 175, 80, 0.4);
    border-color: rgb(76, 175, 80);
}

.play-button.playing {
    background-color: rgba(244, 67, 54, 0.3);
    border-color: rgb(244, 67, 54);
}

/* 下拉菜单容器 */
.dropdown-container {
    position: absolute;
    top: 40px;
    left: 0;
    right: 0;
    display: none;
}

/* 下拉菜单 */
.dropdown-menu {
    position: absolute;
    background-color: rgb(56, 56, 56); /* Unity编辑器风格的深灰色背景 */
    border-width: 1px;
    border-color: rgb(35, 35, 35); /* 更深的边框色，增强对比度 */
    border-radius: 2px; /* Unity风格的小圆角 */
    padding: 4px 0; /* Unity风格的内边距 */
    min-width: 180px; /* 适中的宽度，符合Unity菜单标准 */
    display: none;
    margin-top: 2px; /* 与工具栏的间距 */
    /* Unity风格的阴影效果 - 使用多重阴影模拟深度 */
    -unity-background-image-tint-color: rgba(0, 0, 0, 0.15);
    /* 添加过渡效果 */
    transition-duration: 0.15s;
    transition-property: opacity;
}

/* 下拉菜单项 */
.dropdown-item {
    background-color: transparent;
    border-width: 0;
    color: rgb(210, 210, 210); /* Unity风格的浅灰色文字 */
    padding: 7px 16px; /* Unity风格的内边距，略增加垂直空间 */
    margin: 0 2px; /* 左右留小边距，符合Unity设计 */
    -unity-text-align: middle-left;
    font-size: 12px; /* Unity风格的字体大小 */
    border-radius: 2px; /* 小圆角，与整体风格一致 */
    width: auto;
    min-height: 22px; /* 统一的行高，符合Unity标准 */
    flex-shrink: 0;
    /* 添加过渡效果 */
    transition-duration: 0.1s;
    transition-property: background-color, color;
}

.dropdown-item:hover {
    background-color: rgb(62, 95, 150); /* Unity风格的蓝色高亮 */
    color: rgb(255, 255, 255); /* 高亮时白色文字 */
}

.dropdown-item:active {
    background-color: rgb(51, 80, 128); /* 点击时稍深的蓝色 */
    color: rgb(255, 255, 255);
}

/* 下拉菜单分隔符 */
.dropdown-separator {
    height: 1px;
    background-color: rgb(35, 35, 35); /* 与边框颜色一致 */
    margin: 4px 8px; /* Unity风格的边距 */
}

/* 特定下拉菜单的定位 */
#file-dropdown {
    left: 8px; /* 对应文件菜单的位置 */
}

#edit-dropdown {
    left: 58px; /* 对应编辑菜单的位置 */
}

#view-dropdown {
    left: 108px; /* 对应视图菜单的位置 */
}

#tools-dropdown {
    left: 158px; /* 对应工具菜单的位置 */
}

#help-dropdown {
    left: 208px; /* 对应帮助菜单的位置 */
}

/* 禁用状态 */
.tool-button:disabled {
    opacity: 0.5;
    color: rgb(128, 128, 128);
}

.tool-button:disabled .tool-icon {
    background-color: rgb(128, 128, 128);
}

.menu-button:disabled {
    opacity: 0.5;
    color: rgb(128, 128, 128);
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 800px) {
    .tool-label {
        display: none;
    }
    
    .tool-button {
        width: 32px;
        height: 32px;
    }
    
    .tool-icon {
        margin-bottom: 0;
    }
}
