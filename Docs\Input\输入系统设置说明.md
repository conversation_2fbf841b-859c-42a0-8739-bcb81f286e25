# 视角控制系统说明

## 概述

本项目实现了类似 Blender 的视角控制系统，提供直观的 3D 视角操作体验。

## 视角控制功能

### 1. 旋转视角

- **操作方式**: 按住鼠标中键拖动
- **功能**: 围绕旋转中心自由旋转视角
- **默认旋转中心**: 世界坐标原点 (0, 0, 0)
- **特点**: 支持水平和垂直旋转，提供 360 度全方位视角

### 2. 平移视角

- **操作方式**: 按住 Shift + 鼠标中键拖动
- **功能**: 上下左右平移视角
- **特点**: 同时移动相机和旋转中心，保持相对位置关系

### 3. 缩放视角

- **操作方式**: 滚动鼠标滚轮
- **功能**: 快速放大/缩小视图
- **限制**: 支持最小和最大缩放距离限制
- **特点**: 响应迅速，适合快速调整视角距离

### 4. 平滑缩放

- **操作方式**: 按住 Ctrl + 鼠标中键拖动
- **功能**: 平滑的拖动缩放视图
- **特点**: 提供更精确的缩放控制，适合细节调整

### 5. 第一人称漫游模式

- **操作方式**: 按住鼠标右键
- **功能**: 进入第一人称漫游状态，类似Unity编辑器的Scene视图控制
- **移动控制**:
  - **WASD**: 前后左右移动
  - **Q键**: 向下移动
  - **E键**: 向上移动
  - **鼠标移动**: 控制视角方向
- **特点**:
  - 自动锁定鼠标光标到屏幕中心
  - 支持俯仰角度限制（默认±90度）
  - 释放右键立即退出第一人称模式
  - 与传统视角控制模式互斥，确保操作不冲突

### 6. 鼠标包裹功能

- **功能**: 当鼠标到达屏幕边缘时自动跳转到对面边缘
- **适用场景**: 仅在旋转视角模式下生效
- **特点**: 类似 Unity Scene 视图的无限旋转体验
- **可配置**: 支持运行时启用/禁用

## 输入系统架构

### InputManager 脚本功能

- **双模式切换**: Tab 键切换视角控制模式和 UI 模式
- **智能状态管理**: 自动处理不同操作模式的切换
- **实时状态显示**: 屏幕上显示当前操作模式和状态

### 参数设置

#### 旋转设置

- `rotationSensitivity`: 旋转灵敏度 (默认: 2.0)
- `rotationCenter`: 旋转中心对象 (可选，默认使用世界原点)

#### 平移设置

- `panSensitivity`: 平移灵敏度 (默认: 1.0)

#### 鼠标包裹设置
- `enableMouseWrapping`: 是否启用鼠标包裹功能 (默认: true)
- `wrapBorderSize`: 距离屏幕边缘多少像素时触发包裹 (默认: 50.0)
- `wrapCooldown`: 包裹冷却时间，防止频繁触发 (默认: 0.1秒)
- `wrapDeadZone`: 包裹后的安全区域，防止立即再次触发 (默认: 20.0像素)

#### 缩放设置

- `zoomSensitivity`: 滚轮缩放灵敏度 (默认: 2.0)
- `minZoomDistance`: 最小缩放距离 (默认: 1.0)
- `maxZoomDistance`: 最大缩放距离 (默认: 50.0)
- `smoothZoomSensitivity`: 平滑缩放灵敏度 (默认: 0.1)

#### 平滑设置

- `smoothTime`: 平滑过渡时间 (默认: 0.1)

#### 第一人称漫游设置

- `firstPersonMoveSensitivity`: 第一人称移动灵敏度 (默认: 5.0)
- `firstPersonLookSensitivity`: 第一人称视角灵敏度 (默认: 2.0)
- `firstPersonVerticalSensitivity`: 第一人称垂直移动灵敏度 (默认: 5.0)
- `maxLookAngle`: 最大仰俯角度限制 (默认: 90.0度)

## 使用说明

### 基本操作

1. **启动**: 默认进入视角控制模式
2. **视角旋转**: 按住鼠标中键拖动进行旋转
3. **视角平移**: 按住 Shift + 鼠标中键拖动进行平移
4. **快速缩放**: 滚动鼠标滚轮
5. **精确缩放**: 按住 Ctrl + 鼠标中键拖动
6. **模式切换**: 按 Tab 键切换到 UI 模式

### 高级功能

- **设置旋转中心**: 使用 `SetRotationCenter(Vector3)` 方法
- **重置视角**: 使用 `ResetView()` 方法
- **状态查询**: 使用 `IsViewportControlActive()` 检查是否在操作中

### 鼠标包裹功能

#### 工作原理
- 当鼠标在旋转模式下接近屏幕边缘时，系统会自动将鼠标位置重置到屏幕对面
- 水平包裹：左边缘 ↔ 右边缘
- 垂直包裹：上边缘 ↔ 下边缘
- 包裹触发距离由 `wrapBorderSize` 参数控制

#### 使用场景
- 长时间的视角旋转操作
- 需要大幅度旋转视角的场景
- 类似Unity Scene视图的专业级操作体验

#### API接口
```csharp
// 启用鼠标包裹
inputManager.EnableMouseWrapping();

// 禁用鼠标包裹
inputManager.DisableMouseWrapping();

// 切换鼠标包裹状态
inputManager.ToggleMouseWrapping();

// 检查鼠标包裹是否启用
bool isEnabled = inputManager.IsMouseWrappingEnabled();

// 运行时调整参数
inputManager.SetWrapBorderSize(60f);    // 设置边缘触发距离
inputManager.SetWrapDeadZone(25f);      // 设置安全区域大小
inputManager.SetWrapCooldown(0.15f);    // 设置冷却时间
```

## 设置步骤

### 在 Unity 中设置

1. 将 `InputManager` 脚本添加到场景中的 GameObject 上
2. 在 Inspector 中设置：
   - `Input Actions`: 拖拽 `InputSystem_Actions.inputactions` 文件
   - `Main Camera`: 设置要控制的相机
   - 调整各种灵敏度参数

### 测试场景

1. 添加 `ViewportTestScene` 脚本到场景中
2. 运行场景会自动创建测试几何体
3. 使用各种视角控制操作进行测试

## 输入配置

### Player Action Map 包含

- `Move`: WASD 键移动 (用于第一人称水平移动和传统功能)
- `Look`: 鼠标视角 (用于第一人称视角控制和传统功能)
- `Scroll`: 鼠标滚轮输入 (缩放功能)
- `FirstPersonMode`: 鼠标右键 (进入/退出第一人称漫游模式) **[需要手动添加]**
- `VerticalMove`: Q/E 键 (第一人称垂直移动) **[需要手动添加]**

### UI Action Map 包含

- `Navigate`: 方向键导航
- `ScrollWheel`: UI 滚轮支持

## 优势特点

1. **直观操作**: 类似 Blender 的操作方式，学习成本低
2. **精确控制**: 支持多种缩放模式，满足不同精度需求
3. **平滑体验**: 内置平滑过渡，操作流畅自然
4. **模块化设计**: 易于扩展和自定义
5. **状态管理**: 智能的模式切换和状态显示
6. **性能优化**: 高效的输入处理和相机控制

## 扩展建议

### 可添加功能

- 视角预设位置 (前视图、侧视图、顶视图等)
- 焦点跟踪功能
- 视角动画过渡
- 自定义快捷键绑定
- 视角状态保存/加载

### 自定义参数

- 根据项目需求调整各种灵敏度参数
- 设置适合的缩放距离限制
- 配置合适的平滑过渡时间

## 故障排除

### 常见问题

1. **滚轮不响应**: 检查 Player Action Map 中是否正确配置了 Scroll Action
2. **旋转中心不正确**: 确保 rotationCenter 设置正确或使用 SetRotationCenter 方法
3. **操作不流畅**: 调整相关的灵敏度参数
4. **模式切换失效**: 检查 Tab 键绑定和 InputManager 的状态

### 调试信息

- 运行时屏幕左上角显示当前模式和操作状态
- 控制台输出详细的操作日志
- 使用 ViewportTestScene 进行功能测试
- 使用 MouseWrappingTester 脚本测试鼠标包裹功能

## 使用建议

1. **旋转中心设置**: 建议将旋转中心设置为场景中的关键物体，这样旋转操作会更加直观
2. **灵敏度调整**: 根据用户习惯调整各项灵敏度参数
3. **输入模式切换**: 合理使用Tab键在视角控制和UI模式间切换
4. **组合操作**: 熟练掌握不同修饰键的组合使用，提高操作效率
5. **鼠标包裹**: 对于需要频繁大幅度旋转的用户，建议启用鼠标包裹功能
6. **边缘距离**: 可以根据屏幕尺寸调整 `wrapBorderSize` 参数，较大屏幕可以使用更大的值
