using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - 核心功能
    /// 包含主要的测试流程控制和基础配置
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region 字段和属性

        [Header("UI配置")]
        public UIDocument uiDocument;
        public DaisyTheme testTheme;
        public bool enableDebugMode = true;

        [Header("测试配置")]
        public bool autoRunTests = true;
        public bool logDetailedResults = true;

        private VisualElement root;
        private int testsPassed = 0;
        private int testsFailed = 0;

        #endregion

        #region Unity生命周期

        void Start()
        {
            if (autoRunTests)
            {
                RunValidationTests();
            }
        }

        #endregion

        #region 主要测试流程

        /// <summary>
        /// 运行验证测试
        /// </summary>
        [ContextMenu("Run Validation Tests")]
        public void RunValidationTests()
        {
            Logging.LogInfo("DaisyValidation", "开始DaisyUI基础架构验证测试...");

            testsPassed = 0;
            testsFailed = 0;

            try
            {
                // 1. 测试UI Document设置
                TestUIDocumentSetup();

                // 2. 测试主题系统
                TestThemeSystem();

                // 3. 测试DaisyComponent基类
                TestDaisyComponent();

                // 4. 测试DaisyButton组件
                TestDaisyButton();

                // 5. 测试DaisyCard组件
                TestDaisyCard();

                // 6. 测试DaisyInput组件
                TestDaisyInput();

                // 6.1. 测试DaisyInput占位符功能
                TestDaisyInputPlaceholder();

                // 7. 测试DaisySelect组件
                TestDaisySelect();

                // 8. 测试DaisyTree组件
                TestDaisyTree();

                // 9. 测试DaisyBuilder
                TestDaisyBuilder();

                // 10. 测试DaisyExtensions
                TestDaisyExtensions();

                // 11. 测试样式系统
                TestStyleSystem();

                // 12. 创建综合UI示例
                CreateComprehensiveExample();

                // 输出测试结果
                LogTestResults();
            }
            catch (System.Exception ex)
            {
                Logging.LogError("DaisyValidation", $"验证测试过程中发生错误: {ex.Message}");
                testsFailed++;
            }
        }

        #endregion

        #region 基础测试方法

        /// <summary>
        /// 测试UI Document设置
        /// </summary>
        private void TestUIDocumentSetup()
        {
            LogTest("UI Document 设置测试");

            try
            {
                if (uiDocument == null)
                {
                    throw new System.Exception("UIDocument 未设置");
                }

                root = uiDocument.rootVisualElement;
                if (root == null)
                {
                    throw new System.Exception("无法获取根元素");
                }

                // 清空现有内容
                root.Clear();

                // 设置基础样式
                root.AddToClassList("daisy-validation-root");
                root.style.paddingTop = 20;
                root.style.paddingBottom = 20;
                root.style.paddingLeft = 20;
                root.style.paddingRight = 20;
                root.style.backgroundColor = Color.white;

                LogTestPass("UI Document 设置测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"UI Document 设置测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试主题系统
        /// </summary>
        private void TestThemeSystem()
        {
            LogTest("主题系统测试");

            try
            {
                if (testTheme == null)
                {
                    LogTestWarning("测试主题未设置，使用默认主题");
                    testTheme = DaisyTheme.Current;
                }

                if (testTheme == null)
                {
                    throw new System.Exception("无法获取主题");
                }

                // 验证主题变量是否正确应用
                var testElement = new VisualElement();
                testElement.AddToClassList("daisy-theme-test");
                root.Add(testElement);

                if (testTheme != null)
                {
                    testTheme.Apply(root);
                }

                LogTestPass("主题系统测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"主题系统测试失败: {ex.Message}");
            }
        }

        #endregion

        #region 综合示例创建

        /// <summary>
        /// 创建综合UI示例
        /// </summary>
        private void CreateComprehensiveExample()
        {
            LogTest("创建综合UI示例");

            try
            {
                // 创建综合示例容器
                var exampleContainer = new VisualElement();
                exampleContainer.AddToClassList("daisy-comprehensive-example");
                exampleContainer.style.marginTop = 40;
                exampleContainer.style.paddingTop = 20;
                exampleContainer.style.paddingBottom = 20;
                exampleContainer.style.paddingLeft = 20;
                exampleContainer.style.paddingRight = 20;
                exampleContainer.style.backgroundColor = new Color(0.95f, 0.95f, 0.95f, 1f);
                exampleContainer.style.borderTopLeftRadius = 8;
                exampleContainer.style.borderTopRightRadius = 8;
                exampleContainer.style.borderBottomLeftRadius = 8;
                exampleContainer.style.borderBottomRightRadius = 8;

                // 添加标题
                var title = new Label("DaisyUI 综合示例");
                title.AddToClassList("daisy-example-title");
                title.style.fontSize = 24;
                title.style.unityFontStyleAndWeight = FontStyle.Bold;
                title.style.marginBottom = 20;
                title.style.color = Color.black;
                exampleContainer.Add(title);

                // 添加描述
                var description = new Label("以下是DaisyUI组件库的综合使用示例，展示了各种组件的组合使用效果。");
                description.style.fontSize = 14;
                description.style.marginBottom = 20;
                description.style.color = new Color(0.4f, 0.4f, 0.4f, 1f);
                exampleContainer.Add(description);

                root.Add(exampleContainer);

                LogTestPass("综合UI示例创建成功");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"综合UI示例创建失败: {ex.Message}");
            }
        }

        #endregion
    }
}
