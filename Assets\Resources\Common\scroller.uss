
/* Scroller Reset */
<PERSON>roller {
    background-color: transparent;
}
ScrollerSlider {
    min-width: 0;
    width: 100%;
    margin: 0;
}


.unity-scroller {
    background-color: transparent;
    overflow: hidden;
}

.unity-scroller--vertical {
    position: absolute;
    top:0;
    bottom:0;
    right:0;
    width:10px;
}

.unity-scroller__slider .unity-base-slider__dragger {
    margin: 0;
    border-radius: 3px;
    left: 1px;
    right: 2px;
}

/* 垂直滚动条特定样式 */
.unity-scroller--vertical .unity-base-slider__dragger {
    width: auto;
    min-height: 20px;
}

/* 水平滚动条特定样式 */
.unity-scroller--horizontal .unity-base-slider__dragger {
    height: 100%
    min-width: 20px;
}




/* =============================================================================
   深色主题滚动条 (Unity Inspector 风格)
   ============================================================================= */

ScrollView {
    /* 继承基础样式 */
}

/* 滚动条尺寸 */
ScrollView .unity-scroller--vertical {
    width: 10px;
}

ScrollView .unity-scroller--horizontal {
    height: 16px;
}

/* 滚动条轨道 */
ScrollView .unity-scroller__slider {
    background-color: transparent;
    border-radius: 0;
}

ScrollView .unity-scroller__slider .unity-base-slider__tracker {
    background-color: transparent;
    border-radius: 0;
    border-width: 0;
}

/* 滚动条滑块 */
ScrollView .unity-scroller__slider .unity-base-slider__dragger {
    background-color: rgba(96, 96, 96, 0.4);
}

ScrollView .unity-scroller__slider .unity-base-slider__dragger:hover {
    background-color: rgba(128, 128, 128, 0.7);
}

ScrollView .unity-scroller__slider .unity-base-slider__dragger:active {
    background-color: rgba(160, 160, 160, 0.9);
}

/* 滚动条按钮 */
ScrollView .unity-scroller__low-button,
ScrollView .unity-scroller__high-button {
    background-color: rgba(42, 42, 42, 0.8);
    width: 10px;
    height: 10px;
    right: 0;
    border-radius: 0;
    border-width: 0;
}

ScrollView .unity-scroller__low-button:hover,
ScrollView .unity-scroller__high-button:hover {
    background-color: rgba(96, 96, 96, 0.9);
}

ScrollView .unity-scroller__low-button:active,
ScrollView .unity-scroller__high-button:active {
    background-color: rgba(48, 48, 48, 0.9);
}

/* 滚动条箭头图标 */
ScrollView .unity-scroller__low-button .unity-scroller__low-button-icon,
ScrollView .unity-scroller__high-button .unity-scroller__high-button-icon {
    background-color: rgba(190, 190, 190, 0.8);
    width: 8px;
    height: 8px;
}

/* =============================================================================
   功能性CSS类
   ============================================================================= */

/* 隐藏箭头按钮 */
.unity-scroller__low-button,
.unity-scroller__high-button {
    display: none;
}

/* 平滑滚动动画 */
.smooth-scroll .unity-scroller__slider .unity-base-slider__dragger {
    transition-duration: 0.3s;
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 自动隐藏滚动条 */
.auto-hide .unity-scroller {
    opacity: 0;
    transition-property: opacity;
    transition-duration: 0.3s;
    transition-timing-function: ease-out;
}

.auto-hide:hover .unity-scroller {
    opacity: 1;
}

/* 圆角滚动条 */
.rounded-scrollbar .unity-scroller__slider {
    border-radius: 8px;
}

.rounded-scrollbar .unity-scroller__slider .unity-base-slider__tracker {
    border-radius: 8px;
}

.rounded-scrollbar .unity-scroller__slider .unity-base-slider__dragger {
    border-radius: 8px;
}

/* 无边框滚动条 */
.no-border .unity-scroller--vertical {
    border-left-width: 0;
}

.no-border .unity-scroller--horizontal {
    border-top-width: 0;
}