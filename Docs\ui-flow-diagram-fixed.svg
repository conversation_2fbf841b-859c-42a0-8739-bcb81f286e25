<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="#f8fafc"/>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#1a202c">
    UI Flow and Component Architecture
  </text>
  
  <!-- Main Layout Container -->
  <rect x="50" y="60" width="1100" height="680" fill="#e2e8f0" stroke="#cbd5e0" stroke-width="2" rx="10"/>
  <text x="70" y="85" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2d3748">MainLayout.uxml Structure</text>
  
  <!-- Top Toolbar -->
  <rect x="80" y="100" width="1040" height="50" fill="#fed7aa" stroke="#f97316" stroke-width="2" rx="5"/>
  <text x="600" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2d3748">Top Toolbar</text>
  <text x="600" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2d3748">Tool Selection, View Options, Mode Controls</text>
  
  <!-- Menu Bar -->
  <rect x="80" y="160" width="1040" height="30" fill="#a7c957" stroke="#6b7280" stroke-width="2" rx="5"/>
  <text x="600" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Menu Bar</text>
  
  <!-- Main Content Area -->
  <rect x="80" y="200" width="1040" height="440" fill="#f3f4f6" stroke="#d1d5db" stroke-width="2" rx="5"/>
  
  <!-- Left Panel -->
  <rect x="100" y="220" width="200" height="400" fill="#dbeafe" stroke="#3b82f6" stroke-width="2" rx="5"/>
  <text x="200" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1e3a8a">Left Panel</text>
  
  <!-- Left Panel Content -->
  <rect x="120" y="260" width="160" height="40" fill="#bfdbfe" stroke="#2563eb" stroke-width="1" rx="3"/>
  <text x="200" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1e3a8a">Tool Properties</text>
  <text x="200" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e3a8a">Dynamic based on selected tool</text>
  
  <rect x="120" y="310" width="160" height="40" fill="#bfdbfe" stroke="#2563eb" stroke-width="1" rx="3"/>
  <text x="200" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1e3a8a">Object Inspector</text>
  <text x="200" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e3a8a">Selected object properties</text>
  
  <rect x="120" y="360" width="160" height="40" fill="#bfdbfe" stroke="#2563eb" stroke-width="1" rx="3"/>
  <text x="200" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1e3a8a">Layer Management</text>
  <text x="200" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1e3a8a">Visibility and organization</text>
  
  <!-- 3D Viewport -->
  <rect x="320" y="220" width="600" height="400" fill="#1f2937" stroke="#374151" stroke-width="2" rx="5"/>
  <text x="620" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">3D Viewport</text>
  
  <!-- Viewport Content -->
  <rect x="340" y="260" width="560" height="300" fill="#111827" stroke="#4b5563" stroke-width="1" rx="3"/>
  <text x="620" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#d1d5db">3D Scene Rendering</text>
  
  <!-- Scene Elements -->
  <circle cx="500" cy="380" r="25" fill="#059669" stroke="#10b981" stroke-width="2"/>
  <text x="500" y="385" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">Terrain</text>
  
  <rect x="650" y="320" width="30" height="30" fill="#dc2626" stroke="#ef4444" stroke-width="2" rx="3"/>
  <text x="665" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">Drill</text>
  
  <rect x="580" y="440" width="60" height="15" fill="#7c3aed" stroke="#8b5cf6" stroke-width="1" rx="3"/>
  <text x="610" y="452" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">Blast Area</text>
  
  <!-- Grid overlay -->
  <g stroke="#6b7280" stroke-width="0.5" opacity="0.3">
    <line x1="340" y1="300" x2="900" y2="300"/>
    <line x1="340" y1="340" x2="900" y2="340"/>
    <line x1="340" y1="380" x2="900" y2="380"/>
    <line x1="340" y1="420" x2="900" y2="420"/>
    <line x1="340" y1="460" x2="900" y2="460"/>
    <line x1="340" y1="500" x2="900" y2="500"/>
    <line x1="400" y1="260" x2="400" y2="560"/>
    <line x1="500" y1="260" x2="500" y2="560"/>
    <line x1="600" y1="260" x2="600" y2="560"/>
    <line x1="700" y1="260" x2="700" y2="560"/>
    <line x1="800" y1="260" x2="800" y2="560"/>
  </g>
  
  <!-- Viewport Controls -->
  <rect x="340" y="570" width="560" height="40" fill="#374151" stroke="#4b5563" stroke-width="1" rx="3"/>
  <text x="620" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#d1d5db">Viewport Controls</text>
  <text x="620" y="600" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#9ca3af">Camera: WASD + Mouse | Selection: Click/Drag | View: F to focus</text>
  
  <!-- Right Panel -->
  <rect x="940" y="220" width="160" height="400" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="5"/>
  <text x="1020" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#92400e">Right Panel</text>
  
  <!-- Right Panel Content -->
  <rect x="960" y="260" width="120" height="40" fill="#fde68a" stroke="#f59e0b" stroke-width="1" rx="3"/>
  <text x="1020" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Settings</text>
  <text x="1020" y="290" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#92400e">Application settings</text>
  
  <rect x="960" y="310" width="120" height="40" fill="#fde68a" stroke="#f59e0b" stroke-width="1" rx="3"/>
  <text x="1020" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Information</text>
  <text x="1020" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#92400e">Context information</text>
  
  <rect x="960" y="360" width="120" height="40" fill="#fde68a" stroke="#f59e0b" stroke-width="1" rx="3"/>
  <text x="1020" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#92400e">Database</text>
  <text x="1020" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#92400e">Connection &amp; queries</text>
  
  <!-- Status Bar -->
  <rect x="80" y="650" width="1040" height="30" fill="#d1fae5" stroke="#10b981" stroke-width="2" rx="5"/>
  <text x="600" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#065f46">Status Bar</text>
  
  <!-- Global Dropdown Container -->
  <rect x="80" y="690" width="1040" height="20" fill="#f3f4f6" stroke="#d1d5db" stroke-width="1" rx="3" stroke-dasharray="5,5"/>
  <text x="600" y="705" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#6b7280">Global Dropdown Container (Overlay)</text>
  
  <!-- Event Flow -->
  <ellipse cx="100" cy="450" rx="40" ry="20" fill="#ef4444" stroke="#dc2626" stroke-width="2"/>
  <text x="100" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">User Input</text>
  
  <rect x="170" y="430" width="80" height="30" fill="#38a169" stroke="#2f855a" stroke-width="2" rx="5"/>
  <text x="210" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">Input Processing</text>
  
  <!-- Arrows -->
  <line x1="140" y1="450" x2="170" y2="450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="250" y1="450" x2="320" y2="450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Legend -->
  <rect x="50" y="750" width="600" height="30" fill="white" stroke="#cbd5e0" stroke-width="1" rx="5"/>
  <text x="60" y="765" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#2d3748">Legend:</text>
  <rect x="100" y="760" width="15" height="10" fill="#fed7aa"/>
  <text x="120" y="769" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">UI Components</text>
  <rect x="200" y="760" width="15" height="10" fill="#dbeafe"/>
  <text x="220" y="769" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Panels</text>
  <rect x="270" y="760" width="15" height="10" fill="#1f2937"/>
  <text x="290" y="769" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Viewport</text>
  <rect x="340" y="760" width="15" height="10" fill="#38a169"/>
  <text x="360" y="769" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Input System</text>
  <line x1="430" y1="765" x2="450" y2="765" stroke="#333" stroke-width="2" stroke-dasharray="3,3"/>
  <text x="460" y="769" font-family="Arial, sans-serif" font-size="9" fill="#2d3748">Event Flow</text>
</svg>