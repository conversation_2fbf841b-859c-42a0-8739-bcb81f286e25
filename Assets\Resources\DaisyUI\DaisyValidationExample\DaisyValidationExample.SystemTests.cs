using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.Actions;
using BlastingDesign.UI.DaisyUI.Components.DataDisplay;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.Extensions;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - 系统测试
    /// 包含构建器、扩展方法、样式系统等测试
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region DaisyBuilder测试

        /// <summary>
        /// 测试DaisyBuilder
        /// </summary>
        private void TestDaisyBuilder()
        {
            LogTest("DaisyBuilder 构建器测试");

            try
            {
                // 测试基础容器
                var container = DaisyBuilder.Container();
                if (container == null)
                {
                    throw new System.Exception("Container 创建失败");
                }

                // 测试Row和Column
                var row = DaisyBuilder.Row();
                var column = DaisyBuilder.Column();

                if (row == null || column == null)
                {
                    throw new System.Exception("Row 或 Column 创建失败");
                }

                // 测试网格
                var grid = DaisyBuilder.Grid(2);
                if (grid == null)
                {
                    throw new System.Exception("Grid 创建失败");
                }

                // 测试Card构建方法
                var builderCard1 = DaisyBuilder.Card();
                var builderCard2 = DaisyBuilder.Card("构建器卡片");
                var builderCard3 = DaisyBuilder.Card("完整卡片", "这是通过构建器创建的卡片内容",
                    DaisyButton.Primary("确定"),
                    DaisyButton.Ghost("取消")
                );

                if (builderCard1 == null || builderCard2 == null || builderCard3 == null)
                {
                    throw new System.Exception("Card 构建方法创建失败");
                }

                // 测试Input构建方法
                var builderInput1 = DaisyBuilder.Input("基础输入框");
                var builderInput2 = DaisyBuilder.Input("用户名", "请输入用户名");
                var builderInput3 = DaisyBuilder.PasswordInput();
                var builderInput4 = DaisyBuilder.EmailInput();
                var builderInput5 = DaisyBuilder.NumberInput();

                if (builderInput1 == null || builderInput2 == null || builderInput3 == null ||
                    builderInput4 == null || builderInput5 == null)
                {
                    throw new System.Exception("Input 构建方法创建失败");
                }

                // 测试Select构建方法
                var builderSelect1 = DaisyBuilder.Select("选项1", "选项2", "选项3");
                var builderSelect2 = DaisyBuilder.Select("选择类型", "类型A", "类型B", "类型C");
                var builderSelect3 = DaisyBuilder.MultiSelect("多选1", "多选2", "多选3");
                var builderSelect4 = DaisyBuilder.MultiSelect("多选类型", "类型X", "类型Y", "类型Z");

                if (builderSelect1 == null || builderSelect2 == null ||
                    builderSelect3 == null || builderSelect4 == null)
                {
                    throw new System.Exception("Select 构建方法创建失败");
                }

                // 测试表单
                var form = DaisyBuilder.Form(
                    DaisyBuilder.FormGroup("用户名", DaisyBuilder.Text("输入框占位符")),
                    DaisyBuilder.FormGroup("密码", DaisyBuilder.Text("密码输入框")),
                    DaisyBuilder.ButtonGroup(
                        DaisyButton.Primary("登录"),
                        DaisyButton.Ghost("取消")
                    )
                );

                // 添加Card测试到容器
                var cardTestContainer = DaisyBuilder.Container(
                    DaisyBuilder.Heading("DaisyBuilder Card 测试", 4),
                    DaisyBuilder.Row(builderCard1, builderCard2),
                    builderCard3
                );

                // 添加Input测试到容器
                var inputTestContainer = DaisyBuilder.Container(
                    DaisyBuilder.Heading("DaisyBuilder Input 测试", 4),
                    DaisyBuilder.Row(builderInput1, builderInput2),
                    DaisyBuilder.Row(builderInput3, builderInput4),
                    builderInput5
                );

                // 添加Select测试到容器
                var selectTestContainer = DaisyBuilder.Container(
                    DaisyBuilder.Heading("DaisyBuilder Select 测试", 4),
                    DaisyBuilder.Row(builderSelect1, builderSelect2),
                    DaisyBuilder.Row(builderSelect3, builderSelect4)
                );

                container.Add(form);
                container.Add(cardTestContainer);
                container.Add(inputTestContainer);
                container.Add(selectTestContainer);
                root.Add(container);

                LogTestPass("DaisyBuilder 构建器测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisyBuilder 构建器测试失败: {ex.Message}");
            }
        }

        #endregion

        #region DaisyExtensions测试

        /// <summary>
        /// 测试DaisyExtensions
        /// </summary>
        private void TestDaisyExtensions()
        {
            LogTest("DaisyExtensions 扩展方法测试");

            try
            {
                var testElement = new VisualElement();

                // 测试链式调用
                testElement
                    .AddDaisyClass("test-class")
                    .AddDaisyClass("component")
                    .WithSize("lg")
                    .WithVariant("primary")
                    .WithSpacing(4)
                    .WithAnimation("fade-in")
                    .WithConditionalClass(true, "conditional-class")
                    .When(true, e => e.AddToClassList("when-true"))
                    .LogDebug("扩展方法测试完成");

                // 验证类名是否正确添加
                if (!testElement.ClassListContains("daisy-test-class") ||
                    !testElement.ClassListContains("daisy-component") ||
                    !testElement.ClassListContains("conditional-class") ||
                    !testElement.ClassListContains("when-true"))
                {
                    throw new System.Exception("扩展方法类名添加失败");
                }

                // 测试响应式扩展
                testElement
                    .WithResponsive("sm", "hidden")
                    .WithResponsive("md", "block")
                    .WithResponsive("lg", "flex");

                if (!testElement.ClassListContains("daisy-sm-hidden") ||
                    !testElement.ClassListContains("daisy-md-block") ||
                    !testElement.ClassListContains("daisy-lg-flex"))
                {
                    throw new System.Exception("响应式扩展方法失败");
                }

                // 添加到UI中
                var extensionContainer = new VisualElement();
                extensionContainer.AddToClassList("daisy-extension-test");
                extensionContainer.style.paddingTop = 20;
                extensionContainer.style.paddingBottom = 20;
                extensionContainer.style.paddingLeft = 20;
                extensionContainer.style.paddingRight = 20;
                extensionContainer.style.marginBottom = 20;

                var heading = new Label("DaisyExtensions 扩展方法测试");
                heading.style.fontSize = 18;
                heading.style.unityFontStyleAndWeight = FontStyle.Bold;
                heading.style.marginBottom = 10;
                extensionContainer.Add(heading);

                extensionContainer.Add(testElement);
                root.Add(extensionContainer);

                LogTestPass("DaisyExtensions 扩展方法测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisyExtensions 扩展方法测试失败: {ex.Message}");
            }
        }

        #endregion

        #region 样式系统测试

        /// <summary>
        /// 测试样式系统
        /// </summary>
        private void TestStyleSystem()
        {
            LogTest("样式系统测试");

            try
            {
                // 创建样式测试容器
                var styleContainer = new VisualElement();
                styleContainer.AddToClassList("daisy-style-test");
                styleContainer.style.paddingTop = 20;
                styleContainer.style.paddingBottom = 20;
                styleContainer.style.paddingLeft = 20;
                styleContainer.style.paddingRight = 20;
                styleContainer.style.marginBottom = 20;

                var heading = new Label("样式系统测试");
                heading.style.fontSize = 18;
                heading.style.unityFontStyleAndWeight = FontStyle.Bold;
                heading.style.marginBottom = 10;
                styleContainer.Add(heading);

                // 测试工具类样式
                var utilityTest = new VisualElement();
                utilityTest.AddToClassList("daisy-p-4");
                utilityTest.AddToClassList("daisy-m-2");
                utilityTest.AddToClassList("daisy-bg-primary");
                utilityTest.AddToClassList("daisy-text-white");
                utilityTest.AddToClassList("daisy-rounded");
                utilityTest.style.width = 200;
                utilityTest.style.height = 100;

                var utilityLabel = new Label("工具类样式测试");
                utilityLabel.style.color = Color.white;
                utilityTest.Add(utilityLabel);
                styleContainer.Add(utilityTest);

                // 测试Flexbox工具类
                var flexTest = new VisualElement();
                flexTest.AddToClassList("daisy-flex");
                flexTest.AddToClassList("daisy-justify-between");
                flexTest.AddToClassList("daisy-items-center");
                flexTest.AddToClassList("daisy-gap-4");
                flexTest.style.width = 300;
                flexTest.style.height = 60;
                flexTest.style.backgroundColor = new Color(0.9f, 0.9f, 0.9f, 1f);
                flexTest.style.marginTop = 10;

                var flexItem1 = new Label("项目1");
                var flexItem2 = new Label("项目2");
                var flexItem3 = new Label("项目3");
                flexTest.Add(flexItem1);
                flexTest.Add(flexItem2);
                flexTest.Add(flexItem3);
                styleContainer.Add(flexTest);

                // 测试网格工具类
                var gridTest = new VisualElement();
                gridTest.AddToClassList("daisy-grid");
                gridTest.AddToClassList("daisy-grid-cols-3");
                gridTest.AddToClassList("daisy-gap-2");
                gridTest.style.width = 300;
                gridTest.style.marginTop = 10;

                for (int i = 1; i <= 6; i++)
                {
                    var gridItem = new Label($"网格{i}");
                    gridItem.AddToClassList("daisy-p-2");
                    gridItem.AddToClassList("daisy-bg-secondary");
                    gridItem.AddToClassList("daisy-text-center");
                    gridItem.style.backgroundColor = new Color(0.8f, 0.8f, 0.8f, 1f);
                    gridTest.Add(gridItem);
                }
                styleContainer.Add(gridTest);

                root.Add(styleContainer);

                LogTestPass("样式系统测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"样式系统测试失败: {ex.Message}");
            }
        }

        #endregion
    }
}
