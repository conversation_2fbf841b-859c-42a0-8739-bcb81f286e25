# src/models/ 数据库模型类模块技术规格文档

## 模块概述

数据库模型类模块是系统的数据持久化层核心组件，基于SQLAlchemy ORM框架定义了所有数据库表结构和关系映射。该模块提供了完整的CRUD操作接口，支持双数据库架构（SQLite + SQL Server），确保数据的持久化存储和高效访问。

## 文件清单及功能

### 核心模型文件

| 文件名 | 功能描述 | 对应表名 | 业务领域 |
|--------|----------|----------|----------|
| `__init__.py` | 模块初始化和导出 | - | 模块管理 |
| `base_model.py` | 基础模型类定义 | - | 通用基础设施 |
| `element_base.py` | 元素基类定义 | - | 3D场景元素 |
| `project_info.py` | 项目信息模型 | `t_project_info` | 项目管理 |
| `blast_area.py` | 爆破区域模型 | `t_blast_area` | 爆破区域管理 |
| `measure_point.py` | 测量点模型 | `t_measure_point` | 测量数据管理 |
| `border_line.py` | 边界线模型 | `t_border_line` | 区域边界定义 |
| `hole_info.py` | 钻孔信息模型 | `t_hole_info` | 钻孔数据管理 |
| `mission_info.py` | 任务信息模型 | `t_mission_info` | 任务管理 |
| `layer_info.py` | 图层信息模型 | `t_layer_info` | 图层管理 |

## 核心数据结构

### 1. 基础模型类 (BaseModel)

```python
class BaseModel(Base):
    """基础模型类，所有数据库模型的父类"""
    __abstract__ = True
    
    # 基础标识字段
    id = Column(String(50), primary_key=True, comment="主键")
    name = Column(String(50), nullable=False, comment="名称")
    code = Column(String(50), comment="编码")
    
    # 时间戳字段
    create_time = Column(DateTime, default=datetime.now, comment="创建时间")
    update_time = Column(DateTime, onupdate=datetime.now, comment="更新时间")
    
    # 状态字段
    remarks = Column(String(200), comment="备注")
    is_deleted = Column(Boolean, default=False, comment="是否删除")
    version = Column(Integer, default=1, comment="版本号")
```

### 2. 元素基类 (ElementBase)

```python
class ElementBase(BaseModel):
    """3D场景元素基类"""
    __abstract__ = True
    
    # 3D场景坐标
    x = Column(Float, comment="原点世界坐标x")
    y = Column(Float, comment="原点世界坐标y")
    z = Column(Float, comment="原点世界坐标z")
    
    # 显示状态
    is_visible = Column(Boolean, default=True, comment="可见性")
    is_locked = Column(Boolean, default=False, comment="是否锁定")
    style = Column(String(1000), comment="样式")
    
    # 项目关联
    project_id = Column(String(50), ForeignKey("t_project_info.id"), comment="项目id")
```

### 3. 项目信息模型 (ProjectInfo)

```python
class ProjectInfo(BaseModel):
    """项目信息表"""
    __tablename__ = "t_project_info"
    
    # 地理参考点
    init_lng = Column(Float, nullable=False, comment="初始经度")
    init_lat = Column(Float, nullable=False, comment="初始纬度")
    init_alt = Column(Float, nullable=False, comment="初始高程")
    
    # 坐标系统
    coord_system = Column(String(32), nullable=False, comment="坐标系统")
```

### 4. 爆破区域模型 (BlastArea)

```python
class BlastArea(ElementBase):
    """爆破区域表"""
    __tablename__ = "t_blast_area"
    
    # 项目关联
    project_id = Column(String(50), comment="项目id")
    
    # 钻孔基本参数
    aperture = Column(Float, comment="孔径（mm）")
    hole_depth = Column(Float, comment="孔深（m)")
    step_elevation_z = Column(Float, comment="台阶海拔（m)")
    hole_bottom_z = Column(Float, comment="孔底海拔（m)")
    dril_hole_deep = Column(Float, comment="钻孔超深(m)")
    angle = Column(Float, comment="角度（°）")
    hole_length = Column(Float, comment="孔长（m)")
    
    # 爆破参数
    goods_type = Column(String(50), comment="货物")
    hole_pattern = Column(String(50), comment="布孔模式")
    row_space = Column(Integer, comment="排距（m)")
    hole_space = Column(Integer, comment="孔距(m)")
    
    # 高级参数
    hole_gradient_change = Column(Boolean, default=False, comment="孔距梯度变化")
    depth_gradient_change = Column(Boolean, default=False, comment="深度梯度变化")
    allow_adjust_row = Column(Float, comment="允许调整排范围(m)")
    rear_border_line_distance = Column(Float, comment="距离后部边境线距离（m)")
    row_min_distance = Column(Float, comment="排间最小距离（m)")
    
    # 近远点坐标
    near_lng = Column(Float, comment="近点经度")
    near_lat = Column(Float, comment="近点纬度")
    near_alt = Column(Float, comment="近点海拔")
    far_lng = Column(Float, comment="远点经度")
    far_lat = Column(Float, comment="远点纬度")
    far_alt = Column(Float, comment="远点海拔")
    
    # 近远点标识
    near_point_name = Column(String(50), comment="近点名称")
    far_point_name = Column(String(50), comment="远点名称")
    near_point_id = Column(String(50), comment="近点id")
    far_point_id = Column(String(50), comment="远点id")
    
    # 关系映射
    border_lines = relationship("BorderLine", back_populates="blast_area")
    measure_points = relationship("MeasurePoint", back_populates="blast_area")
    hole_infos = relationship("HoleInfo", back_populates="blast_area")
    mission_infos = relationship("MissionInfo", secondary="t_mission_blast_area", 
                                back_populates="blast_areas")
```

### 5. 测量点模型 (MeasurePoint)

```python
class MeasurePoint(ElementBase):
    """测量点表"""
    __tablename__ = "t_measure_point"
    
    # 点类型和位置
    point_type = Column(String(50), comment="测量点类型")
    lng = Column(Float, comment="经度")
    lat = Column(Float, comment="纬度")
    alt = Column(Float, comment="高程")
    
    # 创建方式
    create_type = Column(String(50), comment="创建类型")
    
    # 关联关系
    blast_area_id = Column(String(50), ForeignKey("t_blast_area.id"), comment="爆区id")
    blast_area = relationship("BlastArea", back_populates="measure_points")
```

### 6. 钻孔信息模型 (HoleInfo)

```python
class HoleInfo(ElementBase):
    """钻孔信息表"""
    __tablename__ = "t_hole_info"
    
    # 位置索引
    row_index = Column(Integer, comment="所在排")
    col_index = Column(Integer, comment="所在列")
    
    # 地理坐标
    lng = Column(Float, comment="经度")
    lat = Column(Float, comment="纬度")
    alt = Column(Float, comment="高程")
    
    # 钻孔参数
    aperture = Column(Float, comment="孔径（mm）")
    hole_depth = Column(Float, comment="孔深（m)")
    step_elevation_z = Column(Float, comment="台阶海拔（m)")
    hole_bottom_z = Column(Float, comment="孔底海拔（m)")
    dril_hole_deep = Column(Float, comment="钻孔超深(m)")
    angle = Column(Float, comment="角度（°）")
    hole_length = Column(Float, comment="孔长（m)")
    goods_type = Column(String(50), comment="货物")
    
    # 关联关系
    blast_area_id = Column(String(50), ForeignKey("t_blast_area.id"), comment="爆区id")
    blast_area = relationship("BlastArea", back_populates="hole_infos")
```

### 7. 任务信息模型 (MissionInfo)

```python
class MissionInfo(BaseModel):
    """钻孔作业任务书"""
    __tablename__ = "t_mission_info"
    
    # 基本信息
    project_id = Column(String(50), comment="项目id")
    begin_time = Column(DateTime, nullable=False, comment="开始时间")
    end_time = Column(DateTime, nullable=False, comment="结束时间")
    location = Column(String(100), nullable=False, comment="作业地点")
    
    # 设备和参数
    dril_list = Column(Text, comment="钻机列表")  # JSON格式
    hole_net = Column(Text, comment="孔网参数")  # JSON格式
    hole_depth = Column(Float, comment="孔深")
    top_height = Column(Float, comment="上部标高")
    bottom_height = Column(Float, comment="底部标高")
    distance = Column(Float, comment="边孔距")
    
    # 详细信息
    notes = Column(Text, comment="注")
    mission_info = Column(Text, comment="任务详情")
    risk = Column(Text, comment="风险")
    solution = Column(Text, comment="措施")
    
    # 多对多关系
    blast_areas = relationship("BlastArea", secondary="t_mission_blast_area", 
                              back_populates="mission_infos")
```

## 接口规范

### BaseModel CRUD接口

#### 创建操作
```python
@classmethod
def create(cls, data: Dict[str, Any]) -> Optional[T]
    """
    创建记录到双数据库
    
    参数:
        data: 记录数据字典
        
    返回:
        创建的模型实例，失败返回None
        
    功能:
        - 同时写入SQLite和SQL Server
        - 自动生成UUID主键
        - 事务回滚处理
        - 错误日志记录
    """
```

#### 查询操作
```python
@classmethod
def get_by_id(cls, id: str) -> Optional[T]
    """
    根据ID获取记录
    
    参数:
        id: 记录ID
        
    返回:
        模型实例或None
    """

@classmethod
def get_all(cls) -> List[T]
    """
    获取所有记录
    
    返回:
        模型实例列表
    """

@classmethod
def get_by_condition(cls, **conditions) -> List[T]
    """
    根据条件查询记录
    
    参数:
        **conditions: 查询条件
        
    返回:
        符合条件的模型实例列表
    """
```

#### 更新操作
```python
@classmethod
def update(cls, id: str, data: Dict[str, Any]) -> bool
    """
    更新双数据库中的记录
    
    参数:
        id: 记录ID
        data: 更新数据字典
        
    返回:
        更新是否成功
    """
```

#### 删除操作
```python
@classmethod
def delete(cls, id: str) -> bool
    """
    从双数据库删除记录
    
    参数:
        id: 记录ID
        
    返回:
        删除是否成功
        
    功能:
        - 物理删除记录
        - 双数据库同步删除
        - 事务安全保证
    """

@classmethod
def soft_delete(cls, id: str) -> bool
    """
    软删除记录（标记删除）
    
    参数:
        id: 记录ID
        
    返回:
        删除是否成功
    """
```

### 关系查询接口

```python
# 爆破区域相关查询
def get_measure_points(self) -> List[MeasurePoint]
    """获取爆区下的所有测量点"""

def get_border_lines(self) -> List[BorderLine]
    """获取爆区下的所有边界线"""

def get_hole_infos(self) -> List[HoleInfo]
    """获取爆区下的所有钻孔信息"""

# 项目相关查询
def get_blast_areas_by_project(project_id: str) -> List[BlastArea]
    """根据项目ID获取所有爆区"""

def get_elements_by_project(project_id: str) -> Dict[str, List]
    """根据项目ID获取所有元素"""
```

## 数据库关系设计

### 主要关系映射

```
ProjectInfo (1) ←→ (N) BlastArea
    ↓
BlastArea (1) ←→ (N) MeasurePoint
BlastArea (1) ←→ (N) BorderLine  
BlastArea (1) ←→ (N) HoleInfo
BlastArea (N) ←→ (N) MissionInfo (通过关联表)

MeasurePoint (1) ←→ (N) BorderLine (起始点/终止点)
```

### 关联表设计

```python
# 任务书-爆区关联表
mission_blast_area = Table(
    "t_mission_blast_area",
    Base.metadata,
    Column("mission_id", String(50), ForeignKey("t_mission_info.id"), primary_key=True),
    Column("blast_area_id", String(50), ForeignKey("t_blast_area.id"), primary_key=True),
)
```

## 业务规则

### 数据完整性规则
- **主键约束**: 所有表使用String类型UUID主键
- **外键约束**: 严格的外键关联关系
- **非空约束**: 关键业务字段不允许为空
- **默认值**: 合理的字段默认值设置

### 数据同步规则
- **双写策略**: 数据同时写入SQLite和SQL Server
- **事务一致性**: 单个数据库内保证事务一致性
- **最佳努力**: 跨数据库采用最佳努力一致性
- **错误隔离**: 一个数据库失败不影响另一个

### 软删除规则
- **标记删除**: 通过is_deleted字段标记删除状态
- **查询过滤**: 默认查询过滤已删除记录
- **数据恢复**: 支持软删除数据的恢复
- **物理清理**: 定期清理软删除数据

### 版本控制规则
- **版本递增**: 每次更新自动递增版本号
- **并发控制**: 基于版本号的乐观锁机制
- **冲突检测**: 更新时检测版本冲突
- **冲突解决**: 提供冲突解决策略

## 关键约束条件

### 数据类型约束
- 坐标数据使用Float类型，精度满足工程需求
- 时间字段使用DateTime类型，支持时区
- 文本字段根据内容长度选择String或Text
- 布尔字段使用Boolean类型，提供默认值

### 业务逻辑约束
- 项目初始坐标必须在有效地理范围内
- 钻孔参数必须符合工程标准
- 测量点坐标必须在合理范围内
- 时间字段必须符合逻辑顺序

### 性能约束
- 大文本字段使用Text类型，避免性能问题
- 关键查询字段建立索引
- 关联查询使用懒加载避免N+1问题
- 批量操作使用事务提高性能

### 安全约束
- 敏感字段不记录详细日志
- 数据库连接使用安全认证
- SQL注入防护通过ORM实现
- 数据访问权限控制

## 扩展点设计

### 新模型扩展
```python
class NewModel(BaseModel):
    """新模型类"""
    __tablename__ = "t_new_model"
    
    # 继承基础字段
    # 添加特有字段
    specific_field = Column(String(100), comment="特有字段")
```

### 关系扩展
```python
# 在现有模型中添加新关系
new_relation = relationship("NewModel", back_populates="parent_model")
```

### 索引优化
```python
# 添加复合索引
__table_args__ = (
    Index('idx_project_blast', 'project_id', 'blast_area_id'),
)
```

## 性能优化

### 查询优化
- 使用索引优化常用查询
- 避免N+1查询问题
- 合理使用懒加载和预加载
- 批量操作减少数据库交互

### 连接优化
- 使用连接池管理数据库连接
- 配置合适的连接池大小
- 及时释放不用的连接
- 监控连接使用情况

### 缓存策略
- 查询结果缓存
- 会话级别缓存
- 应用级别缓存
- 分布式缓存支持
