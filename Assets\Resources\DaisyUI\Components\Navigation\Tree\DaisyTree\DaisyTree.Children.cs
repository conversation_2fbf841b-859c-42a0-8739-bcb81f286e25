using System.Collections.Generic;
using System.Linq;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.DaisyUI.Components.Navigation.Tree
{
    public partial class DaisyTree
    {
        #region Children Management (Simplified for TreeView)
        // 这些方法现在主要是为了API兼容性
        // 实际的树管理由TreeView和DaisyTreeData处理

        public DaisyTreeData AddNode(string id, string text, string icon = null)
        {
            var node = new DaisyTreeData(id, text, icon);
            AddItem(node);
            return node;
        }

        public new void Clear()
        {
            TreeData.Clear();
            FilteredData.Clear();
            
            if (_treeView != null)
            {
                var itemDataList = TreeData.ConvertAll(item => new TreeViewItemData<DaisyTreeData>(item.Id.GetHashCode(), item));
                _treeView.SetRootItems(itemDataList);
                _treeView.Rebuild();
            }
            
            UpdateEmptyState();
        }

        public List<DaisyTreeData> GetAllNodes()
        {
            return GetAllItems();
        }

        public List<DaisyTreeData> GetRootItems()
        {
            return TreeData.Where(item => string.IsNullOrEmpty(item.ParentId)).ToList();
        }

        public List<DaisyTreeData> GetChildItems(string parentId)
        {
            return TreeData.Where(item => item.ParentId == parentId).ToList();
        }
        #endregion
    }
}