# Cesium地形控制功能说明

## 功能概述

新增了通过快捷键控制场景中CesiumGeoreference地形启用和禁用的功能。这个功能集成在InputManager中，提供了便捷的地形切换操作。

## 功能特性

### 1. 快捷键控制
- **默认快捷键**: `T` 键
- **功能**: 切换Cesium地形的启用/禁用状态
- **可自定义**: 可在Inspector中修改快捷键

### 2. 自动查找
- 如果未手动设置CesiumGeoreference对象引用，系统会自动查找名为"CesiumGeoreference"的GameObject
- 提供友好的错误提示和警告信息

### 3. 状态反馈
- 每次切换都会在控制台显示当前状态
- 显示使用的快捷键信息

## 设置方法

### 1. 自动设置（推荐）
1. 确保场景中有名为"CesiumGeoreference"的GameObject
2. 系统会自动找到并控制该对象
3. 直接使用T键进行切换

### 2. 手动设置
1. 在场景中选择包含InputManager组件的GameObject
2. 在Inspector中找到"Cesium地形控制"部分
3. 将CesiumGeoreference对象拖拽到"Cesium Georeference"字段
4. 可选择修改"Terrain Toggle Key"快捷键

## 使用方法

### 基本操作
1. **切换地形**: 按 `T` 键（或自定义的快捷键）
2. **查看状态**: 观察控制台输出的状态信息

### API接口
```csharp
// 获取InputManager实例
InputManager inputManager = FindObjectOfType<InputManager>();

// 切换地形状态
inputManager.ToggleCesiumTerrain();

// 启用地形
inputManager.EnableCesiumTerrain();

// 禁用地形
inputManager.DisableCesiumTerrain();

// 检查地形状态
bool isEnabled = inputManager.IsCesiumTerrainEnabled();
```

## 控制台输出示例

```
Cesium地形已启用 (按T键切换)
Cesium地形已禁用 (按T键切换)
```

## 错误处理

### 常见问题及解决方案

1. **"未找到CesiumGeoreference对象"警告**
   - **原因**: 场景中没有名为"CesiumGeoreference"的GameObject，且Inspector中未设置引用
   - **解决**: 确保场景中存在CesiumGeoreference对象，或在Inspector中手动设置引用

2. **"CesiumGeoreference对象引用为空"警告**
   - **原因**: 手动设置的引用被删除或为空
   - **解决**: 重新在Inspector中设置正确的对象引用

## 技术实现

### 代码结构
- **字段定义**: 在InputManager.Core.cs中添加了cesiumGeoreference和terrainToggleKey字段
- **快捷键检测**: 在Update方法中检测快捷键按下事件
- **控制方法**: 提供了完整的启用、禁用、切换和状态查询API

### 集成方式
- 完全集成在现有的InputManager系统中
- 不影响其他输入功能
- 遵循现有的代码组织结构

## 扩展建议

### 可能的增强功能
1. **UI按钮控制**: 在UI界面添加地形切换按钮
2. **状态指示器**: 在UI中显示当前地形状态
3. **快捷键提示**: 在帮助界面显示快捷键说明
4. **多地形支持**: 支持控制多个不同的地形对象

### 自定义快捷键
可以通过修改Inspector中的"Terrain Toggle Key"字段来使用其他按键：
- `KeyCode.G` - G键
- `KeyCode.H` - H键  
- `KeyCode.F5` - F5键
- 等等...

## 兼容性

- **Unity版本**: 兼容当前项目使用的Unity版本
- **输入系统**: 与现有的Input System完全兼容
- **其他功能**: 不影响相机控制、选择系统等其他功能

## 总结

这个功能为用户提供了便捷的Cesium地形控制方式，通过简单的快捷键操作就能快速切换地形的显示状态。功能设计考虑了易用性和扩展性，既支持自动查找也支持手动配置，为不同的使用场景提供了灵活的解决方案。
