using UnityEngine;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 预览系统测试类
    /// 用于验证预览提供者系统是否正常工作
    /// </summary>
    public static class PreviewSystemTest
    {
        /// <summary>
        /// 运行预览系统测试
        /// </summary>
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
        public static void RunTests()
        {
            try
            {
                Logging.LogInfo("PreviewSystemTest", "开始预览系统测试...");

                // 测试管理器初始化
                TestManagerInitialization();

                // 测试预览提供者注册
                TestProviderRegistration();

                // 测试预览创建
                TestPreviewCreation();

                // 测试代码示例获取
                TestCodeExampleRetrieval();

                Logging.LogInfo("PreviewSystemTest", "预览系统测试完成");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("PreviewSystemTest", $"预览系统测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试管理器初始化
        /// </summary>
        private static void TestManagerInitialization()
        {
            var manager = ComponentPreviewManager.Instance;
            if (manager != null)
            {
                Logging.LogInfo("PreviewSystemTest", "✓ 管理器初始化成功");
                Logging.LogInfo("PreviewSystemTest", manager.GetStatusInfo());
            }
            else
            {
                Logging.LogError("PreviewSystemTest", "✗ 管理器初始化失败");
            }
        }

        /// <summary>
        /// 测试预览提供者注册
        /// </summary>
        private static void TestProviderRegistration()
        {
            var manager = ComponentPreviewManager.Instance;
            var componentIds = new[] { "buttons-item", "input-item", "card-item", "select-item", "modal-item", "dropdown-item" };

            foreach (var componentId in componentIds)
            {
                if (manager.HasProvider(componentId))
                {
                    var provider = manager.GetProvider(componentId);
                    Logging.LogInfo("PreviewSystemTest", $"✓ {componentId} 预览提供者已注册: {provider.ComponentName}");
                }
                else
                {
                    Logging.LogWarning("PreviewSystemTest", $"✗ {componentId} 预览提供者未找到");
                }
            }
        }

        /// <summary>
        /// 测试预览创建
        /// </summary>
        private static void TestPreviewCreation()
        {
            var manager = ComponentPreviewManager.Instance;
            var testComponentId = "buttons-item";

            var provider = manager.GetProvider(testComponentId);
            if (provider != null)
            {
                try
                {
                    var preview = provider.CreatePreview();
                    if (preview != null)
                    {
                        Logging.LogInfo("PreviewSystemTest", $"✓ {testComponentId} 预览创建成功");
                    }
                    else
                    {
                        Logging.LogWarning("PreviewSystemTest", $"✗ {testComponentId} 预览创建返回null");
                    }
                }
                catch (System.Exception ex)
                {
                    Logging.LogError("PreviewSystemTest", $"✗ {testComponentId} 预览创建失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 测试代码示例获取
        /// </summary>
        private static void TestCodeExampleRetrieval()
        {
            var manager = ComponentPreviewManager.Instance;
            var testComponentId = "buttons-item";

            var provider = manager.GetProvider(testComponentId);
            if (provider != null)
            {
                try
                {
                    var codeExample = provider.GetCodeExample();
                    if (!string.IsNullOrEmpty(codeExample))
                    {
                        Logging.LogInfo("PreviewSystemTest", $"✓ {testComponentId} 代码示例获取成功 (长度: {codeExample.Length})");
                    }
                    else
                    {
                        Logging.LogWarning("PreviewSystemTest", $"✗ {testComponentId} 代码示例为空");
                    }
                }
                catch (System.Exception ex)
                {
                    Logging.LogError("PreviewSystemTest", $"✗ {testComponentId} 代码示例获取失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 手动运行测试（用于调试）
        /// </summary>
        [System.Diagnostics.Conditional("UNITY_EDITOR")]
        public static void ManualTest()
        {
            RunTests();
        }
    }
}
