using UnityEngine.UIElements;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 组件预览提供者基类
    /// 提供默认实现和通用功能
    /// </summary>
    public abstract class BaseComponentPreviewProvider : IComponentPreviewProvider
    {
        /// <summary>
        /// 组件ID，用于标识组件类型
        /// </summary>
        public abstract string ComponentId { get; }

        /// <summary>
        /// 组件名称
        /// </summary>
        public abstract string ComponentName { get; }

        /// <summary>
        /// 组件描述
        /// </summary>
        public abstract string ComponentDescription { get; }

        /// <summary>
        /// 创建组件预览的可视化元素
        /// </summary>
        /// <returns>包含组件预览的VisualElement</returns>
        public abstract VisualElement CreatePreview();

        /// <summary>
        /// 获取组件的代码使用示例
        /// </summary>
        /// <returns>代码示例字符串</returns>
        public abstract string GetCodeExample();

        /// <summary>
        /// 获取组件的详细使用说明
        /// </summary>
        /// <returns>使用说明字符串</returns>
        public virtual string GetUsageInstructions()
        {
            return $"使用 {ComponentName} 组件的基本说明：\n\n" +
                   "1. 引入必要的命名空间\n" +
                   "2. 创建组件实例\n" +
                   "3. 配置组件属性\n" +
                   "4. 添加到父容器中\n\n" +
                   "详细代码示例请参考下方的代码块。";
        }

        /// <summary>
        /// 获取组件支持的变体列表
        /// 默认返回空数组，子类可以重写以提供变体支持
        /// </summary>
        /// <returns>变体名称数组</returns>
        public virtual string[] GetSupportedVariants()
        {
            return new string[0];
        }

        /// <summary>
        /// 创建指定变体的预览
        /// 默认返回null，子类可以重写以提供变体支持
        /// </summary>
        /// <param name="variant">变体名称</param>
        /// <returns>变体预览的VisualElement，如果不支持则返回null</returns>
        public virtual VisualElement CreateVariantPreview(string variant)
        {
            return null;
        }

        /// <summary>
        /// 获取指定变体的代码示例
        /// 默认返回null，子类可以重写以提供变体支持
        /// </summary>
        /// <param name="variant">变体名称</param>
        /// <returns>变体代码示例，如果不支持则返回null</returns>
        public virtual string GetVariantCodeExample(string variant)
        {
            return null;
        }

        /// <summary>
        /// 创建预览容器的辅助方法
        /// </summary>
        /// <param name="cssClasses">要添加的CSS类名</param>
        /// <returns>配置好的容器元素</returns>
        protected VisualElement CreatePreviewContainer(params string[] cssClasses)
        {
            var container = new VisualElement();
            
            // 添加默认的预览容器类
            container.AddToClassList("component-preview-container");
            
            // 添加自定义类
            if (cssClasses != null)
            {
                foreach (var cssClass in cssClasses)
                {
                    if (!string.IsNullOrEmpty(cssClass))
                    {
                        container.AddToClassList(cssClass);
                    }
                }
            }
            
            return container;
        }

        /// <summary>
        /// 创建预览标题的辅助方法
        /// </summary>
        /// <param name="title">标题文本</param>
        /// <returns>标题标签元素</returns>
        protected Label CreatePreviewTitle(string title)
        {
            var titleLabel = new Label(title);
            titleLabel.AddToClassList("component-preview-section-title");
            return titleLabel;
        }

        /// <summary>
        /// 创建预览描述的辅助方法
        /// </summary>
        /// <param name="description">描述文本</param>
        /// <returns>描述标签元素</returns>
        protected Label CreatePreviewDescription(string description)
        {
            var descLabel = new Label(description);
            descLabel.AddToClassList("component-preview-description");
            return descLabel;
        }
    }
}
