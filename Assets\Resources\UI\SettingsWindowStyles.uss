@import url("../TailwindUss/index.uss");

/* ===== 主窗口容器 ===== */
.settings-window {
    flex-direction: column;
    width: 800px;
    height: 600px;
    background-color: var(--gray-800);
    border-radius: 8px;
    border-width: 1px;
    border-color: var(--gray-600);
    padding: 0;
    margin: 0;
}

/* ===== 顶部标题栏 ===== */
.settings-header {
    padding: 16px 20px;
    background-color: var(--gray-900);
    border-bottom-width: 1px;
    border-bottom-color: var(--gray-600);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    min-height: 50px;
}

.header-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.title-section {
    flex-direction: row;
    align-items: center;
}

.settings-icon {
    font-size: 18px;
    color: var(--blue-400);
    margin-right: 10px;
}

.settings-title {
    font-size: 16px;
    color: var(--gray-100);
    margin: 0;
}

.search-section {
    flex-direction: row;
    align-items: center;
}

.search-container {
    flex-direction: row;
    align-items: center;
    background-color: var(--gray-750);
    border-radius: 16px;
    padding: 6px 12px;
    border-width: 1px;
    border-color: var(--gray-600);
}

.search-container:focus {
    border-color: var(--blue-500);
}

.search-icon {
    font-size: 12px;
    color: var(--gray-400);
    margin-right: 6px;
}

.settings-search {
    width: 180px;
    padding: 4px 8px;
    background-color: transparent;
    border-width: 0;
    color: var(--gray-100);
    font-size: 13px;
}

/* ===== 主体内容区域 ===== */
.settings-body {
    flex: 1;
    flex-direction: row;
    background-color: var(--gray-800);
}

/* ===== 左侧导航栏 ===== */
.settings-sidebar {
    width: 200px;
    background-color: var(--gray-850);
    flex-shrink: 0;
}

.sidebar-header {
    padding: 12px 16px;
    border-bottom-width: 1px;
    border-bottom-color: var(--gray-700);
}

.sidebar-title {
    font-size: 11px;
    -unity-font-style: bold;
    color: var(--gray-400);
    letter-spacing: 0.5px;
}

.category-list {
    flex: 1;
    padding: 8px;
}

.category-button {
    flex-direction: row;
    align-items: center;
    padding: 10px 12px;
    background-color: transparent;
    border-width: 0;
    border-radius: 6px;
    color: var(--gray-300);
    font-size: 13px;
    -unity-font-style: normal;
    -unity-text-align: middle-left;
    min-height: 32px;
    margin: 1px 0;
    transition: all 0.15s ease;
}

.category-button:hover {
    background-color: var(--gray-700);
    color: var(--gray-100);
}

.category-button.selected {
    background-color: var(--blue-600);
    color: var(--white);
    -unity-font-style: bold;
}

.category-button.selected:hover {
    background-color: var(--blue-500);
}

/* ===== 分隔线 ===== */
.sidebar-separator {
    width: 1px;
    background-color: var(--gray-600);
    flex-shrink: 0;
}

/* ===== 右侧内容区域 ===== */
.settings-content-area {
    flex: 1;
    background-color: var(--gray-800);
    flex-direction: column;
}

.content-header {
    padding: 16px 20px;
    border-bottom-width: 1px;
    border-bottom-color: var(--gray-600);
    background-color: var(--gray-750);
}

.current-category-title {
    font-size: 18px;
    -unity-font-style: bold;
    color: var(--gray-100);
    margin-bottom: 4px;
}

.current-category-description {
    font-size: 13px;
    color: var(--gray-400);
}

.settings-content {
    flex: 1;
    padding: 20px;
    background-color: var(--gray-800);
}

/* ===== 设置模块样式 ===== */
.settings-module {
    flex-direction: column;
    width: 100%;
}

.settings-group {
    flex-direction: column;
    margin-bottom: 24px;
    padding: 16px;
    background-color: var(--gray-750);
    border-radius: 8px;
    border-width: 1px;
    border-color: var(--gray-600);
}

.group-title {
    font-size: 14px;
    -unity-font-style: bold;
    color: var(--gray-100);
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom-width: 1px;
    border-bottom-color: var(--gray-600);
}

.settings-sub-group {
    margin-left: 16px;
    margin-top: 8px;
    padding-left: 16px;
    border-left-width: 2px;
    border-left-color: var(--blue-600);
}

/* ===== 设置项样式 ===== */
.setting-item {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    min-height: 40px;
    border-bottom-width: 1px;
    border-bottom-color: var(--gray-700);
}

/* Unity UI Toolkit 不支持 :last-child 伪类选择器 */
.setting-item.last-item {
    border-bottom-width: 0;
}

.setting-label {
    font-size: 13px;
    -unity-font-style: normal;
    color: var(--gray-200);
    flex-shrink: 0;
    margin-right: 16px;
    min-width: 120px;
}

/* ===== 表单控件样式 ===== */
.setting-textfield {
    flex: 1;
    max-width: 200px;
    padding: 8px 12px;
    background-color: var(--gray-900);
    border-width: 1px;
    border-color: var(--gray-600);
    border-radius: 4px;
    color: var(--gray-100);
    font-size: 13px;
    transition: border-color 0.2s ease;
}

.setting-textfield:focus {
    border-color: var(--blue-500);
    background-color: var(--gray-850);
}

.setting-intfield {
    flex: 1;
    max-width: 100px;
    padding: 8px 12px;
    background-color: var(--gray-900);
    border-width: 1px;
    border-color: var(--gray-600);
    border-radius: 4px;
    color: var(--gray-100);
    font-size: 13px;
    -unity-text-align: middle-center;
    transition: border-color 0.2s ease;
}

.setting-intfield:focus {
    border-color: var(--blue-500);
    background-color: var(--gray-850);
}

.setting-dropdown {
    flex: 1;
    max-width: 180px;
    padding: 8px 12px;
    background-color: var(--gray-900);
    border-width: 1px;
    border-color: var(--gray-600);
    border-radius: 4px;
    color: var(--gray-100);
    font-size: 13px;
    transition: border-color 0.2s ease;
}

.setting-dropdown:focus {
    border-color: var(--blue-500);
    background-color: var(--gray-850);
}

/* ===== 开关按钮样式 ===== */
.setting-toggle {
    flex-shrink: 0;
}

.setting-toggle .unity-toggle__input {
    background-color: var(--gray-600);
    border-width: 1px;
    border-color: var(--gray-500);
    border-radius: 10px;
    width: 36px;
    height: 18px;
    transition: all 0.2s ease;
}

.setting-toggle .unity-toggle__input:checked {
    background-color: var(--blue-600);
    border-color: var(--blue-500);
}

.setting-toggle .unity-toggle__checkmark {
    background-color: var(--white);
    border-radius: 50%;
    width: 12px;
    height: 12px;
    transition: all 0.2s ease;
}

.setting-toggle .unity-toggle__input:checked .unity-toggle__checkmark {
    translate: 16px 0;
}

/* ===== 滑块样式 ===== */
.setting-slider {
    flex: 1;
    max-width: 140px;
    margin-right: 12px;
}

.setting-slider .unity-base-slider__tracker {
    background-color: var(--gray-600);
    border-radius: 2px;
    height: 4px;
}

.setting-slider .unity-base-slider__dragger {
    background-color: var(--blue-600);
    border-radius: 50%;
    width: 14px;
    height: 14px;
    transition: background-color 0.2s ease;
}

.setting-slider .unity-base-slider__dragger:hover {
    background-color: var(--blue-500);
}

.setting-value-label {
    font-size: 12px;
    -unity-font-style: bold;
    color: var(--blue-400);
    min-width: 30px;
    -unity-text-align: middle-center;
    background-color: var(--gray-700);
    padding: 2px 6px;
    border-radius: 3px;
}

.setting-sub-container {
    flex-direction: row;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
}

/* ===== 底部状态栏 ===== */
.settings-footer {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background-color: var(--gray-900);
    border-top-width: 1px;
    border-top-color: var(--gray-600);
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    min-height: 50px;
}

.footer-left {
    flex-direction: row;
    align-items: center;
}

.footer-right {
    flex-direction: row;
    align-items: center;
}

.status-indicator {
    font-size: 11px;
    -unity-font-style: bold;
    margin-right: 6px;
}

.status-ready { color: var(--green-400); }
.status-warning { color: var(--yellow-400); }
.status-error { color: var(--red-400); }

.status-message {
    font-size: 12px;
    color: var(--gray-400);
    -unity-font-style: italic;
}

/* ===== 按钮样式 ===== */
.settings-button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    -unity-font-style: normal;
    border-width: 1px;
    transition: all 0.2s ease;
    margin-left: 8px;
    min-width: 80px;
}

/* Unity UI Toolkit 不支持 :first-child 伪类选择器 */
.settings-button.first-item {
    margin-left: 0;
}

.settings-button:hover {
    translate: 0 -1px;
}

.settings-button-primary {
    background-color: var(--blue-600);
    color: var(--white);
    border-color: var(--blue-500);
}

.settings-button-primary:hover {
    background-color: var(--blue-500);
    border-color: var(--blue-400);
}

.settings-button-secondary {
    background-color: var(--gray-700);
    color: var(--gray-300);
    border-color: var(--gray-600);
}

.settings-button-secondary:hover {
    background-color: var(--gray-600);
    color: var(--gray-100);
    border-color: var(--gray-500);
}

/* ===== 错误提示样式 ===== */
.error-label {
    color: var(--red-400);
    font-size: 13px;
    -unity-font-style: normal;
    padding: 12px 16px;
    -unity-text-align: middle-center;
    background-color: var(--red-900);
    border-radius: 6px;
    border-width: 1px;
    border-color: var(--red-700);
    margin: 16px;
}

/* ===== 自定义颜色变量 ===== */
:root {
    --gray-750: #374151;
    --gray-850: #1f2937;
    --green-400: #4ade80;
    --yellow-400: #facc15;
    --red-400: #f87171;
    --red-700: #b91c1c;
    --red-900: #7f1d1d;
}

/* ===== 滚动条样式 ===== */
.settings-content .unity-scroller--vertical {
    width: 6px;
    background-color: transparent;
}

.settings-content .unity-scroller--vertical .unity-slider .unity-base-slider__dragger {
    background-color: var(--gray-600);
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.settings-content .unity-scroller--vertical .unity-slider .unity-base-slider__dragger:hover {
    background-color: var(--gray-500);
}

.category-list .unity-scroller--vertical {
    width: 4px;
    background-color: transparent;
}

.category-list .unity-scroller--vertical .unity-slider .unity-base-slider__dragger {
    background-color: var(--gray-700);
    border-radius: 2px;
}

.category-list .unity-scroller--vertical .unity-slider .unity-base-slider__dragger:hover {
    background-color: var(--gray-600);
}