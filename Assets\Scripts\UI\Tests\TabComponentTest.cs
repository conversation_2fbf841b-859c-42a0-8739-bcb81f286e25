using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.Navigation;
using BlastingDesign.UI.Components.Previews;

namespace BlastingDesign.UI.Tests
{
    /// <summary>
    /// Tab组件测试类
    /// </summary>
    public class TabComponentTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runTestOnStart = false;

        private void Start()
        {
            if (runTestOnStart)
            {
                RunTabTests();
            }
        }

        [ContextMenu("运行Tab组件测试")]
        public void RunTabTests()
        {
            Debug.Log("=== Tab组件测试开始 ===");

            TestBasicTabCreation();
            TestTabVariants();
            TestTabSizes();
            TestTabDirections();
            TestDynamicOperations();
            TestPreviewProvider();

            Debug.Log("=== Tab组件测试完成 ===");
        }

        private void TestBasicTabCreation()
        {
            Debug.Log("测试基本Tab创建...");

            // 测试指定activeTabId的情况
            var tab1 = DaisyTab.Create("tab2")
                .AddTab("tab1", "首页", CreateTestContent("首页内容"))
                .AddTab("tab2", "关于", CreateTestContent("关于内容"))
                .OnTabChange(tabId => Debug.Log($"Tab切换到: {tabId}"));

            Debug.Assert(tab1.ActiveTabId == "tab2", "初始激活Tab应该是tab2");
            Debug.Log("✓ 指定ActiveTabId测试通过");

            // 测试默认激活第一个tab的情况
            var tab2 = DaisyTab.Create("")
                .AddTab("first", "第一个", CreateTestContent("第一个内容"))
                .AddTab("second", "第二个", CreateTestContent("第二个内容"));

            Debug.Assert(tab2.ActiveTabId == "first", "默认应该激活第一个添加的Tab");
            Debug.Log("✓ 默认激活第一个Tab测试通过");

            // 测试多次点击同一个tab
            tab2.SetActiveTab("first");
            Debug.Assert(tab2.ActiveTabId == "first", "多次点击同一个tab应该保持激活状态");
            Debug.Log("✓ 重复点击同一tab测试通过");

            // 测试指定不存在的activeTabId时的回退行为
            var tab3 = DaisyTab.Create("nonexistent")
                .AddTab("existing", "存在的", CreateTestContent("存在的内容"));

            Debug.Assert(tab3.ActiveTabId == "existing", "指定不存在的activeTabId时应该激活第一个tab");
            Debug.Log("✓ 不存在activeTabId回退测试通过");

            Debug.Log("✓ 基本Tab创建测试通过");
        }

        private void TestTabVariants()
        {
            Debug.Log("测试Tab样式变体...");

            var borderedTab = DaisyTab.Bordered("bordered1");
            var liftedTab = DaisyTab.Lifted("lifted1");
            var boxedTab = DaisyTab.Boxed("boxed1");

            Debug.Assert(borderedTab.Variant == TabVariant.Bordered, "Bordered变体应该正确设置");
            Debug.Assert(liftedTab.Variant == TabVariant.Lifted, "Lifted变体应该正确设置");
            Debug.Assert(boxedTab.Variant == TabVariant.Boxed, "Boxed变体应该正确设置");

            Debug.Log("✓ Tab样式变体测试通过");
        }

        private void TestTabSizes()
        {
            Debug.Log("测试Tab尺寸...");

            var tab = DaisyTab.Create("size1");

            tab.SetExtraSmall();
            Debug.Assert(tab.Size == TabSize.ExtraSmall, "ExtraSmall尺寸应该正确设置");

            tab.SetLarge();
            Debug.Assert(tab.Size == TabSize.Large, "Large尺寸应该正确设置");

            Debug.Log("✓ Tab尺寸测试通过");
        }

        private void TestTabDirections()
        {
            Debug.Log("测试Tab布局方向...");

            var tab = DaisyTab.Create("direction1");

            tab.SetDirection(TabDirection.Row);
            Debug.Assert(tab.Direction == TabDirection.Row, "Row方向应该正确设置");

            tab.SetDirection(TabDirection.Column);
            Debug.Assert(tab.Direction == TabDirection.Column, "Column方向应该正确设置");

            Debug.Log("✓ Tab布局方向测试通过");
        }

        private void TestDynamicOperations()
        {
            Debug.Log("测试Tab动态操作...");

            var tab = DaisyTab.Create("dynamic1")
                .AddTab("tab1", "Tab 1", CreateTestContent("内容1"));

            // 测试添加Tab
            tab.AddTab("tab2", "Tab 2", CreateTestContent("内容2"));

            // 测试切换Tab
            tab.SetActiveTab("tab2");
            Debug.Assert(tab.ActiveTabId == "tab2", "ActiveTab应该切换到tab2");

            // 测试移除Tab
            tab.RemoveTab("tab2");
            Debug.Assert(tab.ActiveTabId == "tab1", "移除当前Tab后应该切换到其他Tab");

            Debug.Log("✓ Tab动态操作测试通过");
        }

        private void TestPreviewProvider()
        {
            Debug.Log("测试TabPreviewProvider...");

            var provider = new TabPreviewProvider();

            Debug.Assert(provider.ComponentId == "tab-item", "ComponentId应该是tab-item");
            Debug.Assert(!string.IsNullOrEmpty(provider.ComponentName), "ComponentName不应该为空");
            Debug.Assert(!string.IsNullOrEmpty(provider.ComponentDescription), "ComponentDescription不应该为空");
            Debug.Assert(!string.IsNullOrEmpty(provider.GetCodeExample()), "CodeExample不应该为空");
            Debug.Assert(!string.IsNullOrEmpty(provider.GetUsageInstructions()), "UsageInstructions不应该为空");

            var preview = provider.CreatePreview();
            Debug.Assert(preview != null, "CreatePreview应该返回有效的VisualElement");

            var variants = provider.GetSupportedVariants();
            Debug.Assert(variants != null && variants.Length > 0, "应该支持多个变体");

            Debug.Log("✓ TabPreviewProvider测试通过");
        }

        private VisualElement CreateTestContent(string text)
        {
            var content = new VisualElement();
            var label = new Label(text);
            content.Add(label);
            return content;
        }

        [ContextMenu("测试Tab组件在UI中的显示")]
        public void TestTabInUI()
        {
            // 查找或创建一个UIDocument来测试
            var uiDocument = FindFirstObjectByType<UIDocument>();
            if (uiDocument == null)
            {
                Debug.LogWarning("没有找到UIDocument，无法测试UI显示");
                return;
            }

            var root = uiDocument.rootVisualElement;
            if (root == null)
            {
                Debug.LogWarning("UIDocument根元素为空，无法测试UI显示");
                return;
            }

            // 清除现有内容
            root.Clear();

            // 创建测试Tab
            var testTab = DaisyTab.Create("ui-test1")
                .AddTab("ui-test1", "首页", CreateRichTestContent("首页", "欢迎来到首页"))
                .AddTab("ui-test2", "产品", CreateRichTestContent("产品页面", "查看我们的产品"))
                .AddTab("ui-test3", "联系", CreateRichTestContent("联系我们", "获取联系信息"))
                .SetVariant(TabVariant.Bordered)
                .OnTabChange(tabId => Debug.Log($"UI测试 - 切换到Tab: {tabId}"));

            // 添加到UI
            root.Add(testTab);

            Debug.Log("Tab组件已添加到UI中进行测试");
        }

        private VisualElement CreateRichTestContent(string title, string description)
        {
            var content = new VisualElement();
            content.style.paddingLeft = 20;
            content.style.paddingRight = 20;
            content.style.paddingTop = 20;
            content.style.paddingBottom = 20;
            content.style.minHeight = 200;

            var titleLabel = new Label(title);
            titleLabel.style.fontSize = 18;
            titleLabel.style.marginBottom = 10;
            content.Add(titleLabel);

            var descLabel = new Label(description);
            descLabel.style.fontSize = 14;
            descLabel.style.opacity = 0.7f;
            content.Add(descLabel);

            return content;
        }
    }
}