%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 198ab63ac919a4b74a2591215a77711d, type: 3}
  m_Name: MenuBarConfig
  m_EditorClassIdentifier: 
  categories:
  - name: file
    displayName: "\u6587\u4EF6"
    enabled: 1
    sortOrder: 0
    items:
    - name: new-scene
      displayName: "\u65B0\u5EFA\u573A\u666F"
      enabled: 1
      visible: 1
      callbackName: NewScene
      parameters: []
      iconName: 
      shortcut: Ctrl+N
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: open-scene
      displayName: "\u6253\u5F00\u573A\u666F"
      enabled: 1
      visible: 1
      callbackName: OpenScene
      parameters: []
      iconName: 
      shortcut: Ctrl+O
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: save-scene
      displayName: "\u4FDD\u5B58\u573A\u666F"
      enabled: 1
      visible: 1
      callbackName: SaveScene
      parameters: []
      iconName: 
      shortcut: Ctrl+S
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: "\u65B0\u83DC\u5355\u9879"
      displayName: "\u65B0\u83DC\u5355\u9879"
      enabled: 1
      visible: 1
      callbackName: 
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 1
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: import
      displayName: "\u5BFC\u5165"
      enabled: 1
      visible: 1
      callbackName: Import
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: export
      displayName: "\u5BFC\u51FA"
      enabled: 1
      visible: 1
      callbackName: Export
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: setting
      displayName: "\u8BBE\u7F6E"
      enabled: 1
      visible: 1
      callbackName: Setting
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 6
    customCssClass: 
  - name: edit
    displayName: "\u7F16\u8F91"
    enabled: 1
    sortOrder: 1
    items:
    - name: undo
      displayName: "\u64A4\u9500"
      enabled: 1
      visible: 1
      callbackName: Undo
      parameters: []
      iconName: 
      shortcut: Ctrl+Z
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: redo
      displayName: "\u91CD\u505A"
      enabled: 1
      visible: 1
      callbackName: Redo
      parameters: []
      iconName: 
      shortcut: Ctrl+Y
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: "\u65B0\u83DC\u5355\u9879"
      displayName: "\u65B0\u83DC\u5355\u9879"
      enabled: 1
      visible: 1
      callbackName: 
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 1
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: copy
      displayName: "\u590D\u5236"
      enabled: 1
      visible: 1
      callbackName: Copy
      parameters: []
      iconName: 
      shortcut: Ctrl+C
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: paste
      displayName: "\u7C98\u8D34"
      enabled: 1
      visible: 1
      callbackName: Paste
      parameters: []
      iconName: 
      shortcut: Ctrl+V
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    customCssClass: 
  - name: view
    displayName: "\u89C6\u56FE"
    enabled: 1
    sortOrder: 2
    items:
    - name: zoom-in
      displayName: "\u653E\u5927"
      enabled: 1
      visible: 1
      callbackName: ZoomIn
      parameters: []
      iconName: 
      shortcut: Ctrl++
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: zoom-out
      displayName: "\u7F29\u5C0F"
      enabled: 1
      visible: 1
      callbackName: ZoomOut
      parameters: []
      iconName: 
      shortcut: Ctrl+-
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: fit-view
      displayName: "\u9002\u5E94\u89C6\u56FE"
      enabled: 1
      visible: 1
      callbackName: FitView
      parameters: []
      iconName: 
      shortcut: F
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    customCssClass: 
  - name: tools
    displayName: "\u5DE5\u5177"
    enabled: 1
    sortOrder: 3
    items:
    - name: measure-tool
      displayName: "\u6D4B\u91CF\u5DE5\u5177"
      enabled: 1
      visible: 1
      callbackName: SelectMeasureTool
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: annotation-tool
      displayName: "\u6807\u6CE8\u5DE5\u5177"
      enabled: 1
      visible: 1
      callbackName: SelectAnnotationTool
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: screenshot-tool
      displayName: "\u622A\u56FE\u5DE5\u5177"
      enabled: 1
      visible: 1
      callbackName: SelectScreenshotTool
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: "\u65B0\u83DC\u5355\u9879"
      displayName: "\u65B0\u83DC\u5355\u9879"
      enabled: 1
      visible: 1
      callbackName: 
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 1
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: database-connection-test
      displayName: "\u6570\u636E\u5E93\u8FDE\u63A5\u6D4B\u8BD5"
      enabled: 1
      visible: 1
      callbackName: ShowDatabaseConnectionTest
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: component-library
      displayName: "\u7EC4\u4EF6\u5E93"
      enabled: 1
      visible: 1
      callbackName: ShowComponentLibrary
      parameters: []
      iconName: 
      shortcut: 
      tooltip: "\u6253\u5F00DaisyUI\u7EC4\u4EF6\u5E93"
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    customCssClass: 
  - name: help
    displayName: "\u5E2E\u52A9"
    enabled: 1
    sortOrder: 4
    items:
    - name: user-manual
      displayName: "\u7528\u6237\u624B\u518C"
      enabled: 1
      visible: 1
      callbackName: ShowUserManual
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: shortcuts
      displayName: "\u5FEB\u6377\u952E"
      enabled: 1
      visible: 1
      callbackName: ShowShortcuts
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    - name: about
      displayName: "\u5173\u4E8E"
      enabled: 1
      visible: 1
      callbackName: ShowAbout
      parameters: []
      iconName: 
      shortcut: 
      tooltip: 
      isSeparator: 0
      isSubmenu: 0
      submenuItems: []
      customCssClass: 
      sortOrder: 0
    customCssClass: 
  enableKeyboardShortcuts: 1
  showIcons: 1
  dropdownWidth: 180
  references:
    version: 2
    RefIds: []
