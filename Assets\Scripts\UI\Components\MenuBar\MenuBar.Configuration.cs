using System;
using UnityEngine;
using BlastingDesign.UI.Config;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// MenuBar配置管理类 - 负责配置加载和菜单生成
    /// </summary>
    public partial class MenuBar : UIElementBase, IMenuComponent
    {
        #region 配置管理

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                config = Resources.Load<MenuBarConfig>(configPath);
                if (config == null)
                {
                    Logging.LogWarning("MenuBar", $"未找到配置文件: {configPath}，将创建默认配置");
                    CreateDefaultConfig();
                }
                else
                {
                    Logging.LogInfo("MenuBar", "配置文件加载成功");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("MenuBar", $"加载配置文件失败: {ex.Message}");
                CreateDefaultConfig();
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private void CreateDefaultConfig()
        {
            config = ScriptableObject.CreateInstance<MenuBarConfig>();
            config.CreateDefaultConfig();
        }

        /// <summary>
        /// 根据配置生成菜单
        /// </summary>
        private void GenerateMenuFromConfig()
        {
            if (menuCategories == null || config == null) return;

            // 清空现有菜单
            menuCategories.Clear();
            menuButtons.Clear();
            dropdownMenus.Clear();

            // 按排序顺序生成菜单分类
            config.SortCategories();

            foreach (var category in config.Categories)
            {
                if (!category.enabled) continue;

                CreateMenuCategory(category);
            }

            Logging.LogInfo("MenuBar", $"根据配置生成了 {config.Categories.Count} 个菜单分类");
        }

        #endregion
    }
}