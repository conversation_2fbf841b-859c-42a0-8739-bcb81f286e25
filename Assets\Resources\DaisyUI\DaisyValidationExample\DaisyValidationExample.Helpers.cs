using UnityEngine;
using BlastingDesign.Utils;
using BlastingDesign.UI.DaisyUI.Components.Actions;
using BlastingDesign.UI.DaisyUI.Components.DataDisplay;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.DaisyUI.Builders;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - 测试辅助方法
    /// 包含测试日志记录和结果统计等辅助功能
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region 测试日志方法

        /// <summary>
        /// 记录测试开始
        /// </summary>
        /// <param name="testName">测试名称</param>
        private void LogTest(string testName)
        {
            if (logDetailedResults)
            {
                Logging.LogInfo("DaisyValidation", $"开始测试: {testName}");
            }
        }

        /// <summary>
        /// 记录测试通过
        /// </summary>
        /// <param name="message">成功消息</param>
        private void LogTestPass(string message)
        {
            testsPassed++;
            if (logDetailedResults)
            {
                Logging.LogInfo("DaisyValidation", $"✓ {message}");
            }
        }

        /// <summary>
        /// 记录测试失败
        /// </summary>
        /// <param name="message">失败消息</param>
        private void LogTestFail(string message)
        {
            testsFailed++;
            Logging.LogError("DaisyValidation", $"✗ {message}");
        }

        /// <summary>
        /// 记录测试警告
        /// </summary>
        /// <param name="message">警告消息</param>
        private void LogTestWarning(string message)
        {
            if (logDetailedResults)
            {
                Logging.LogWarning("DaisyValidation", $"⚠ {message}");
            }
        }

        /// <summary>
        /// 输出测试结果
        /// </summary>
        private void LogTestResults()
        {
            var totalTests = testsPassed + testsFailed;
            var successRate = totalTests > 0 ? (float)testsPassed / totalTests * 100 : 0;

            Logging.LogInfo("DaisyValidation", "=== DaisyUI 验证测试结果 ===");
            Logging.LogInfo("DaisyValidation", $"总测试数: {totalTests}");
            Logging.LogInfo("DaisyValidation", $"通过: {testsPassed}");
            Logging.LogInfo("DaisyValidation", $"失败: {testsFailed}");
            Logging.LogInfo("DaisyValidation", $"成功率: {successRate:F1}%");

            if (testsFailed == 0)
            {
                Logging.LogInfo("DaisyValidation", "🎉 所有测试都通过了！DaisyUI基础架构验证成功！");
            }
            else
            {
                Logging.LogError("DaisyValidation", $"❌ 有 {testsFailed} 个测试失败，请检查相关组件实现。");
            }

            Logging.LogInfo("DaisyValidation", "================================");
        }

        #endregion

        #region 调试和工具方法

        /// <summary>
        /// 清空测试结果
        /// </summary>
        [ContextMenu("Clear Test Results")]
        public void ClearTestResults()
        {
            if (root != null)
            {
                root.Clear();
            }

            testsPassed = 0;
            testsFailed = 0;

            Logging.LogInfo("DaisyValidation", "测试结果已清空");
        }

        /// <summary>
        /// 重新运行测试
        /// </summary>
        [ContextMenu("Rerun Tests")]
        public void RerunTests()
        {
            ClearTestResults();
            RunValidationTests();
        }

        /// <summary>
        /// 切换调试模式
        /// </summary>
        [ContextMenu("Toggle Debug Mode")]
        public void ToggleDebugMode()
        {
            enableDebugMode = !enableDebugMode;
            logDetailedResults = enableDebugMode;

            Logging.LogInfo("DaisyValidation", $"调试模式: {(enableDebugMode ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 导出测试报告
        /// </summary>
        [ContextMenu("Export Test Report")]
        public void ExportTestReport()
        {
            var report = GenerateTestReport();

            // 这里可以将报告保存到文件或发送到其他地方
            Logging.LogInfo("DaisyValidation", "测试报告:");
            Logging.LogInfo("DaisyValidation", report);
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        /// <returns>测试报告字符串</returns>
        private string GenerateTestReport()
        {
            var totalTests = testsPassed + testsFailed;
            var successRate = totalTests > 0 ? (float)testsPassed / totalTests * 100 : 0;

            var report = $@"
DaisyUI 验证测试报告
==================
测试时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}
Unity版本: {Application.unityVersion}
平台: {Application.platform}

测试结果:
- 总测试数: {totalTests}
- 通过: {testsPassed}
- 失败: {testsFailed}
- 成功率: {successRate:F1}%

测试组件:
- DaisyComponent 基类
- DaisyButton 按钮组件
- DaisyCard 卡片组件
- DaisyInput 输入框组件
- DaisySelect 选择器组件
- DaisyBuilder 构建器
- DaisyExtensions 扩展方法
- 样式系统

状态: {(testsFailed == 0 ? "✓ 全部通过" : "✗ 存在失败")}
";

            return report;
        }

        /// <summary>
        /// 验证组件完整性
        /// </summary>
        /// <returns>是否完整</returns>
        public bool ValidateComponentIntegrity()
        {
            try
            {
                // 检查核心组件是否存在
                var button = new DaisyButton("测试");
                var card = DaisyCard.Create();
                var input = DaisyInput.Create();
                var select = DaisySelect.Create();

                // 检查构建器是否工作
                var builderButton = DaisyBuilder.Button("构建器测试");
                var builderCard = DaisyBuilder.Card("构建器卡片");
                var builderInput = DaisyBuilder.Input("构建器输入框");
                var builderSelect = DaisyBuilder.Select("选项1", "选项2");

                // 检查主题系统
                if (testTheme != null)
                {
                    testTheme.Apply(root);
                }

                return true;
            }
            catch (System.Exception ex)
            {
                Logging.LogError("DaisyValidation", $"组件完整性验证失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取组件统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public string GetComponentStats()
        {
            var stats = $@"
DaisyUI 组件统计
===============
已实现组件:
- Actions: DaisyButton
- DataDisplay: DaisyCard
- DataInput: DaisyInput, DaisySelect
- Layout: (待实现)
- Navigation: (待实现)

核心系统:
- DaisyComponent 基类 ✓
- DaisyTheme 主题系统 ✓
- DaisyBuilder 构建器 ✓
- DaisyExtensions 扩展方法 ✓
- 样式系统 ✓

测试覆盖率: {(testsPassed + testsFailed > 0 ? "已覆盖" : "未测试")}
";

            return stats;
        }

        #endregion

        #region 性能测试方法

        /// <summary>
        /// 性能测试 - 创建大量组件
        /// </summary>
        [ContextMenu("Performance Test - Create Many Components")]
        public void PerformanceTestCreateComponents()
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            const int componentCount = 1000;

            for (int i = 0; i < componentCount; i++)
            {
                var button = new DaisyButton($"按钮{i}");
                var card = DaisyCard.Create($"卡片{i}");
                var input = DaisyInput.Create($"输入框{i}");
                var select = DaisySelect.Create($"选项{i}A", $"选项{i}B");
            }

            stopwatch.Stop();

            Logging.LogInfo("DaisyValidation",
                $"性能测试完成: 创建 {componentCount * 4} 个组件耗时 {stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// 性能测试 - 构建器性能
        /// </summary>
        [ContextMenu("Performance Test - Builder Performance")]
        public void PerformanceTestBuilder()
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            const int builderCount = 500;

            for (int i = 0; i < builderCount; i++)
            {
                var container = DaisyBuilder.Container(
                    DaisyBuilder.Card($"卡片{i}", $"内容{i}"),
                    DaisyBuilder.Row(
                        DaisyBuilder.Input($"输入{i}"),
                        DaisyBuilder.Select($"选项{i}A", $"选项{i}B")
                    ),
                    DaisyBuilder.ButtonGroup(
                        DaisyButton.Primary($"确定{i}"),
                        DaisyButton.Ghost($"取消{i}")
                    )
                );
            }

            stopwatch.Stop();

            Logging.LogInfo("DaisyValidation",
                $"构建器性能测试完成: 创建 {builderCount} 个复杂容器耗时 {stopwatch.ElapsedMilliseconds}ms");
        }

        #endregion
    }
}
