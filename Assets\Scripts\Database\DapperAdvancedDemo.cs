using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Linq;
using UnityEngine;
using Microsoft.Data.Sqlite;
using Dapper;
using BlastingDesign.Utils;

namespace BlastingDesign.Database.Demo
{
    /// <summary>
    /// Dapper 高级功能演示
    /// 展示事务、存储过程、批量操作等高级特性
    /// </summary>
    public class DapperAdvancedDemo : MonoBehaviour
    {
        [Header("高级演示配置")]
        [SerializeField] private KeyCode transactionTestKey = KeyCode.F9;
        [SerializeField] private KeyCode batchOperationKey = KeyCode.F10;
        [SerializeField] private KeyCode complexQueryKey = KeyCode.F11;
        [SerializeField] private bool showGUI = true;

        private string connectionString;
        private DapperSqliteTest dapperTest;

        void Start()
        {
            // 获取基础的 Dapper 测试组件
            dapperTest = GetComponent<DapperSqliteTest>();
            if (dapperTest == null)
            {
                dapperTest = gameObject.AddComponent<DapperSqliteTest>();
            }

            // 使用相同的连接字符串
            var dbPath = Path.Combine(Application.persistentDataPath, "DapperAdvanced.db");
            connectionString = $"Data Source={dbPath}";

            ShowInstructions();
        }

        void Update()
        {
            if (Input.GetKeyDown(transactionTestKey))
            {
                _ = TestTransactionsAsync();
            }

            if (Input.GetKeyDown(batchOperationKey))
            {
                _ = TestBatchOperationsAsync();
            }

            if (Input.GetKeyDown(complexQueryKey))
            {
                _ = TestComplexQueriesAsync();
            }
        }

        /// <summary>
        /// 显示使用说明
        /// </summary>
        private void ShowInstructions()
        {
            Logging.LogInfo("DapperAdvancedDemo", "=== Dapper 高级功能演示 ===");
            Logging.LogInfo("DapperAdvancedDemo", "");
            Logging.LogInfo("DapperAdvancedDemo", "快捷键操作：");
            Logging.LogInfo("DapperAdvancedDemo", $"{transactionTestKey} - 事务处理测试");
            Logging.LogInfo("DapperAdvancedDemo", $"{batchOperationKey} - 批量操作测试");
            Logging.LogInfo("DapperAdvancedDemo", $"{complexQueryKey} - 复杂查询测试");
            Logging.LogInfo("DapperAdvancedDemo", "");
            Logging.LogInfo("DapperAdvancedDemo", "高级特性：");
            Logging.LogInfo("DapperAdvancedDemo", "• 事务管理和回滚");
            Logging.LogInfo("DapperAdvancedDemo", "• 批量插入和更新");
            Logging.LogInfo("DapperAdvancedDemo", "• 复杂查询和联表");
            Logging.LogInfo("DapperAdvancedDemo", "• 动态参数和条件查询");
            Logging.LogInfo("DapperAdvancedDemo", "• 性能监控和优化");
        }

        /// <summary>
        /// 测试事务处理
        /// </summary>
        private async Task TestTransactionsAsync()
        {
            Logging.LogInfo("DapperAdvancedDemo", "开始事务处理测试...");

            using var connection = new SqliteConnection(connectionString);
            await connection.OpenAsync();

            // 确保表存在
            await CreateTablesIfNotExistsAsync(connection);

            // 测试成功的事务
            await TestSuccessfulTransactionAsync(connection);

            // 测试失败的事务（回滚）
            await TestFailedTransactionAsync(connection);

            Logging.LogInfo("DapperAdvancedDemo", "事务处理测试完成");
        }

        /// <summary>
        /// 测试成功的事务
        /// </summary>
        private async Task TestSuccessfulTransactionAsync(SqliteConnection connection)
        {
            using var transaction = connection.BeginTransaction();
            try
            {
                Logging.LogInfo("DapperAdvancedDemo", "开始成功事务测试...");

                // 插入用户
                var userId = await connection.QuerySingleAsync<int>(@"
                    INSERT INTO Users (Name, Email, Age) 
                    VALUES (@Name, @Email, @Age);
                    SELECT last_insert_rowid();",
                    new { Name = "事务用户", Email = "<EMAIL>", Age = 35 },
                    transaction);

                // 插入相关文章
                await connection.ExecuteAsync(@"
                    INSERT INTO Posts (UserId, Title, Content) 
                    VALUES (@UserId, @Title, @Content)",
                    new[]
                    {
                        new { UserId = userId, Title = "事务文章1", Content = "事务处理的内容1" },
                        new { UserId = userId, Title = "事务文章2", Content = "事务处理的内容2" }
                    },
                    transaction);

                await transaction.CommitAsync();
                Logging.LogInfo("DapperAdvancedDemo", $"成功事务已提交，用户ID: {userId}");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                Logging.LogError("DapperAdvancedDemo", $"事务回滚: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试失败的事务（故意触发错误）
        /// </summary>
        private async Task TestFailedTransactionAsync(SqliteConnection connection)
        {
            using var transaction = connection.BeginTransaction();
            try
            {
                Logging.LogInfo("DapperAdvancedDemo", "开始失败事务测试（故意触发错误）...");

                // 插入用户
                await connection.ExecuteAsync(@"
                    INSERT INTO Users (Name, Email, Age) 
                    VALUES (@Name, @Email, @Age)",
                    new { Name = "失败用户", Email = "<EMAIL>", Age = 40 },
                    transaction);

                // 故意插入重复邮箱（违反唯一约束）
                await connection.ExecuteAsync(@"
                    INSERT INTO Users (Name, Email, Age) 
                    VALUES (@Name, @Email, @Age)",
                    new { Name = "重复用户", Email = "<EMAIL>", Age = 41 },
                    transaction);

                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                Logging.LogInfo("DapperAdvancedDemo", $"预期的事务回滚成功: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试批量操作
        /// </summary>
        private async Task TestBatchOperationsAsync()
        {
            Logging.LogInfo("DapperAdvancedDemo", "开始批量操作测试...");

            using var connection = new SqliteConnection(connectionString);
            await connection.OpenAsync();

            await CreateTablesIfNotExistsAsync(connection);

            // 批量插入用户
            var users = new List<object>();
            for (int i = 1; i <= 100; i++)
            {
                users.Add(new
                {
                    Name = $"批量用户{i}",
                    Email = $"batch{i}@example.com",
                    Age = 20 + (i % 30)
                });
            }

            var startTime = DateTime.Now;
            var insertedCount = await connection.ExecuteAsync(@"
                INSERT INTO Users (Name, Email, Age) 
                VALUES (@Name, @Email, @Age)",
                users);
            var duration = DateTime.Now - startTime;

            Logging.LogInfo("DapperAdvancedDemo", $"批量插入 {insertedCount} 个用户，耗时: {duration.TotalMilliseconds}ms");

            // 批量更新
            startTime = DateTime.Now;
            var updatedCount = await connection.ExecuteAsync(@"
                UPDATE Users 
                SET Age = Age + 1 
                WHERE Name LIKE @Pattern",
                new { Pattern = "批量用户%" });
            duration = DateTime.Now - startTime;

            Logging.LogInfo("DapperAdvancedDemo", $"批量更新 {updatedCount} 个用户年龄，耗时: {duration.TotalMilliseconds}ms");

            Logging.LogInfo("DapperAdvancedDemo", "批量操作测试完成");
        }

        /// <summary>
        /// 测试复杂查询
        /// </summary>
        private async Task TestComplexQueriesAsync()
        {
            Logging.LogInfo("DapperAdvancedDemo", "开始复杂查询测试...");

            using var connection = new SqliteConnection(connectionString);
            await connection.OpenAsync();

            await CreateTablesIfNotExistsAsync(connection);

            // 动态条件查询
            await TestDynamicQueryAsync(connection);

            // 分页查询
            await TestPaginationAsync(connection);

            // 聚合查询
            await TestAggregationAsync(connection);

            // 联表查询
            await TestJoinQueryAsync(connection);

            Logging.LogInfo("DapperAdvancedDemo", "复杂查询测试完成");
        }

        /// <summary>
        /// 动态条件查询
        /// </summary>
        private async Task TestDynamicQueryAsync(SqliteConnection connection)
        {
            var conditions = new List<string>();
            var parameters = new DynamicParameters();

            // 动态构建查询条件
            int? minAge = 25;
            int? maxAge = 35;
            string namePattern = "批量%";

            if (minAge.HasValue)
            {
                conditions.Add("Age >= @MinAge");
                parameters.Add("MinAge", minAge.Value);
            }

            if (maxAge.HasValue)
            {
                conditions.Add("Age <= @MaxAge");
                parameters.Add("MaxAge", maxAge.Value);
            }

            if (!string.IsNullOrEmpty(namePattern))
            {
                conditions.Add("Name LIKE @NamePattern");
                parameters.Add("NamePattern", namePattern);
            }

            var whereClause = conditions.Count > 0 ? "WHERE " + string.Join(" AND ", conditions) : "";
            var sql = $"SELECT * FROM Users {whereClause} ORDER BY Age";

            var users = await connection.QueryAsync<User>(sql, parameters);
            Logging.LogInfo("DapperAdvancedDemo", $"动态查询结果: {users.Count()} 个用户");
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        private async Task TestPaginationAsync(SqliteConnection connection)
        {
            int pageSize = 10;
            int pageNumber = 2;
            int offset = (pageNumber - 1) * pageSize;

            var users = await connection.QueryAsync<User>(@"
                SELECT * FROM Users 
                ORDER BY Id 
                LIMIT @PageSize OFFSET @Offset",
                new { PageSize = pageSize, Offset = offset });

            var totalCount = await connection.QuerySingleAsync<int>("SELECT COUNT(*) FROM Users");
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            Logging.LogInfo("DapperAdvancedDemo", $"分页查询 - 第{pageNumber}页，共{totalPages}页，本页{users.Count()}条记录");
        }

        /// <summary>
        /// 聚合查询
        /// </summary>
        private async Task TestAggregationAsync(SqliteConnection connection)
        {
            var stats = await connection.QuerySingleAsync(@"
                SELECT 
                    COUNT(*) as TotalUsers,
                    AVG(Age) as AverageAge,
                    MIN(Age) as MinAge,
                    MAX(Age) as MaxAge
                FROM Users");

            Logging.LogInfo("DapperAdvancedDemo", $"用户统计 - 总数: {stats.TotalUsers}, 平均年龄: {stats.AverageAge:F1}, 年龄范围: {stats.MinAge}-{stats.MaxAge}");
        }

        /// <summary>
        /// 联表查询
        /// </summary>
        private async Task TestJoinQueryAsync(SqliteConnection connection)
        {
            var userPosts = await connection.QueryAsync(@"
                SELECT 
                    u.Name as UserName,
                    u.Email,
                    COUNT(p.Id) as PostCount,
                    MAX(p.CreatedAt) as LastPostDate
                FROM Users u
                LEFT JOIN Posts p ON u.Id = p.UserId
                GROUP BY u.Id, u.Name, u.Email
                HAVING COUNT(p.Id) > 0
                ORDER BY PostCount DESC
                LIMIT 5");

            Logging.LogInfo("DapperAdvancedDemo", "活跃用户排行榜（前5名）:");
            foreach (var item in userPosts)
            {
                Logging.LogInfo("DapperAdvancedDemo", $"  {item.UserName}: {item.PostCount} 篇文章");
            }
        }

        /// <summary>
        /// 创建表（如果不存在）
        /// </summary>
        private async Task CreateTablesIfNotExistsAsync(SqliteConnection connection)
        {
            await connection.ExecuteAsync(@"
                CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Email TEXT UNIQUE,
                    Age INTEGER,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )");

            await connection.ExecuteAsync(@"
                CREATE TABLE IF NOT EXISTS Posts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER,
                    Title TEXT NOT NULL,
                    Content TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (UserId) REFERENCES Users(Id)
                )");
        }

        /// <summary>
        /// GUI 显示
        /// </summary>
        void OnGUI()
        {
            if (!showGUI) return;

            GUILayout.BeginArea(new Rect(10, 320, 500, 200));
            GUILayout.BeginVertical("box");

            GUILayout.Label("Dapper 高级功能演示", GUI.skin.label);
            GUILayout.Space(5);

            GUILayout.BeginHorizontal();
            if (GUILayout.Button($"事务测试 ({transactionTestKey})"))
            {
                _ = TestTransactionsAsync();
            }
            if (GUILayout.Button($"批量操作 ({batchOperationKey})"))
            {
                _ = TestBatchOperationsAsync();
            }
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            if (GUILayout.Button($"复杂查询 ({complexQueryKey})"))
            {
                _ = TestComplexQueriesAsync();
            }
            if (GUILayout.Button("清空测试数据"))
            {
                ClearTestData();
            }
            GUILayout.EndHorizontal();

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        /// <summary>
        /// 清空测试数据
        /// </summary>
        [ContextMenu("清空测试数据")]
        public void ClearTestData()
        {
            try
            {
                using var connection = new SqliteConnection(connectionString);
                connection.Open();

                connection.Execute("DELETE FROM Posts");
                connection.Execute("DELETE FROM Users");

                Logging.LogInfo("DapperAdvancedDemo", "测试数据已清空");
            }
            catch (Exception ex)
            {
                Logging.LogError("DapperAdvancedDemo", $"清空数据失败: {ex.Message}");
            }
        }
    }

    #region 数据模型（如果DapperSqliteTest中的模型不可访问）

    /// <summary>
    /// 用户数据模型
    /// </summary>
    public class User
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public int Age { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 文章数据模型
    /// </summary>
    public class Post
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    #endregion
}
