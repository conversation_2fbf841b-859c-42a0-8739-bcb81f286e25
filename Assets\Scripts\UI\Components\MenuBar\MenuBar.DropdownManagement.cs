using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// MenuBar下拉菜单管理类 - 负责下拉菜单的显示、隐藏和位置管理
    /// </summary>
    public partial class MenuBar : UIElementBase, IMenuComponent
    {
        #region 下拉菜单管理

        /// <summary>
        /// 显示下拉菜单
        /// </summary>
        private void ShowDropdown(VisualElement dropdown, Button menuButton)
        {
            if (dropdown == null || menuButton == null || rootElement == null) return;

            // 确保下拉菜单在根元素中
            if (dropdown.parent != rootElement)
            {
                dropdown.RemoveFromHierarchy();
                rootElement.Add(dropdown);
                // 重新应用样式表
                ApplyStyleSheetToDropdown(dropdown);
                Logging.LogInfo("MenuBar", $"将下拉菜单 {dropdown.name} 添加到根元素并重新应用样式");
            }

            // 确保样式正确应用（防止样式丢失）
            EnsureDropdownStyles(dropdown);

            // 计算菜单按钮的位置
            var buttonRect = menuButton.worldBound;
            var rootRect = rootElement.worldBound;

            // 计算相对于根元素的位置
            var position = CalculateDropdownPosition(buttonRect, rootRect, dropdown);

            // 设置下拉菜单位置
            dropdown.style.position = Position.Absolute;
            dropdown.style.left = position.x;
            dropdown.style.top = position.y;

            // 显示菜单
            dropdown.AddToClassList("visible");
            dropdown.style.display = DisplayStyle.Flex;
            dropdown.pickingMode = PickingMode.Position;

            Logging.LogInfo("MenuBar", $"显示下拉菜单 {dropdown.name}，位置: ({position.x}, {position.y})");
        }

        /// <summary>
        /// 隐藏下拉菜单
        /// </summary>
        private void HideDropdown(VisualElement dropdown)
        {
            if (dropdown == null) return;

            dropdown.RemoveFromClassList("visible");
            dropdown.style.display = DisplayStyle.None;
        }

        /// <summary>
        /// 隐藏所有下拉菜单
        /// </summary>
        private void HideAllDropdowns()
        {
            foreach (var dropdown in dropdownMenus.Values)
            {
                HideDropdown(dropdown);
            }

            // 清除所有菜单按钮的激活状态
            foreach (var kvp in menuButtons)
            {
                SetMenuButtonActive(kvp.Key, false);
            }

            currentOpenDropdown = null;
        }

        #endregion

        #region 位置计算

        /// <summary>
        /// 计算下拉菜单位置，确保菜单不会超出屏幕边界
        /// </summary>
        private Vector2 CalculateDropdownPosition(Rect buttonRect, Rect rootRect, VisualElement dropdown)
        {
            // 计算相对于根元素的基础位置
            float x = buttonRect.x - rootRect.x;
            float y = buttonRect.yMax - rootRect.y;

            // 获取下拉菜单的预期尺寸
            var dropdownRect = dropdown.layout;
            float dropdownWidth = dropdownRect.width > 0 ? dropdownRect.width : EstimateDropdownWidth(dropdown);
            float dropdownHeight = dropdownRect.height > 0 ? dropdownRect.height : EstimateDropdownHeight(dropdown);

            // 检查右边界
            if (x + dropdownWidth > rootRect.width)
            {
                // 尝试右对齐到按钮
                x = (buttonRect.x - rootRect.x) + (buttonRect.width - dropdownWidth);
                if (x < 0) x = 0; // 确保不会超出左边界
            }

            // 检查底部边界
            if (y + dropdownHeight > rootRect.height)
            {
                // 尝试在按钮上方显示
                float topY = (buttonRect.y - rootRect.y) - dropdownHeight;
                if (topY >= 0)
                {
                    y = topY;
                }
                else
                {
                    // 如果上方也不够空间，则在可用空间内显示
                    y = Mathf.Max(0, rootRect.height - dropdownHeight);
                }
            }

            return new Vector2(x, y);
        }

        /// <summary>
        /// 估算下拉菜单宽度
        /// </summary>
        private float EstimateDropdownWidth(VisualElement dropdown)
        {
            // 如果有CSS类，可以根据类估算
            if (dropdown.ClassListContains("dropdown-menu"))
            {
                return 200; // 标准下拉菜单宽度
            }
            return 180; // 默认宽度
        }

        /// <summary>
        /// 估算下拉菜单高度
        /// </summary>
        private float EstimateDropdownHeight(VisualElement dropdown)
        {
            // 根据子元素数量估算高度
            int itemCount = dropdown.childCount;
            float estimatedItemHeight = 32; // 预估每个菜单项高度
            float padding = 8; // 内边距

            return Mathf.Min(itemCount * estimatedItemHeight + padding, 400); // 最大高度限制为400
        }

        #endregion
    }
}