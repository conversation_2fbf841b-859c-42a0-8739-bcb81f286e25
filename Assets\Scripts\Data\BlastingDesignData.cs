using System;
using System.Collections.Generic;
using UnityEngine;

namespace BlastingDesign.Data
{
    /// <summary>
    /// 爆破设计核心数据元素类型
    /// </summary>
    public enum BlastingElementType
    {
        Project,        // 项目
        BlastingArea,   // 爆区
        MeasurePoint,   // 测量点
        BoundaryLine,   // 边界线
        DrillHole,      // 钻孔
        BlastBlock,     // 爆破块体
        Geology,        // 地质
        SafetyZone,     // 安全区域
        Equipment,      // 设备
        Material        // 材料
    }

    /// <summary>
    /// 爆破设计数据元素基类
    /// </summary>
    [Serializable]
    public abstract class BlastingElement
    {
        public string id;
        public string name;
        public BlastingElementType type;
        public bool isVisible = true;
        public bool isLocked = false;
        public Vector3 position;
        public Dictionary<string, object> properties;

        protected BlastingElement(string id, string name, BlastingElementType type)
        {
            this.id = id;
            this.name = name;
            this.type = type;
            this.properties = new Dictionary<string, object>();
        }

        public virtual string GetIcon()
        {
            return type switch
            {
                BlastingElementType.Project => "🏗️",
                BlastingElementType.BlastingArea => "💥",
                BlastingElementType.MeasurePoint => "📍",
                BlastingElementType.BoundaryLine => "📏",
                BlastingElementType.DrillHole => "🕳️",
                BlastingElementType.BlastBlock => "🧱",
                BlastingElementType.Geology => "🗻",
                BlastingElementType.SafetyZone => "⚠️",
                BlastingElementType.Equipment => "⚙️",
                BlastingElementType.Material => "📦",
                _ => "📄"
            };
        }

        public virtual List<string> GetAvailableActions()
        {
            var actions = new List<string> { "visibility", "properties" };
            
            if (!isLocked)
            {
                actions.AddRange(new[] { "edit", "rename", "delete" });
            }
            
            return actions;
        }
    }

    /// <summary>
    /// 项目数据
    /// </summary>
    [Serializable]
    public class ProjectData : BlastingElement
    {
        public string description;
        public DateTime createdDate;
        public string createdBy;
        public List<BlastingAreaData> blastingAreas;

        public ProjectData(string id, string name) : base(id, name, BlastingElementType.Project)
        {
            blastingAreas = new List<BlastingAreaData>();
            createdDate = DateTime.Now;
        }
    }

    /// <summary>
    /// 爆区数据
    /// </summary>
    [Serializable]
    public class BlastingAreaData : BlastingElement
    {
        public float area;
        public string rockType;
        public List<MeasurePointData> measurePoints;
        public List<BoundaryLineData> boundaryLines;
        public List<DrillHoleData> drillHoles;
        public List<BlastBlockData> blastBlocks;

        public BlastingAreaData(string id, string name) : base(id, name, BlastingElementType.BlastingArea)
        {
            measurePoints = new List<MeasurePointData>();
            boundaryLines = new List<BoundaryLineData>();
            drillHoles = new List<DrillHoleData>();
            blastBlocks = new List<BlastBlockData>();
        }

        public override List<string> GetAvailableActions()
        {
            var actions = base.GetAvailableActions();
            actions.Add("calculate");
            actions.Add("export");
            return actions;
        }
    }

    /// <summary>
    /// 测量点数据
    /// </summary>
    [Serializable]
    public class MeasurePointData : BlastingElement
    {
        public float elevation;
        public string coordinateSystem;
        public MeasurePointType pointType;

        public MeasurePointData(string id, string name) : base(id, name, BlastingElementType.MeasurePoint)
        {
            pointType = MeasurePointType.Control;
        }

        public override string GetIcon()
        {
            return pointType switch
            {
                MeasurePointType.Control => "🎯",
                MeasurePointType.Survey => "📐",
                MeasurePointType.Reference => "📍",
                _ => "📍"
            };
        }
    }

    /// <summary>
    /// 测量点类型
    /// </summary>
    public enum MeasurePointType
    {
        Control,    // 控制点
        Survey,     // 测量点
        Reference   // 参考点
    }

    /// <summary>
    /// 边界线数据
    /// </summary>
    [Serializable]
    public class BoundaryLineData : BlastingElement
    {
        public List<Vector3> points;
        public BoundaryType boundaryType;
        public float length;

        public BoundaryLineData(string id, string name) : base(id, name, BlastingElementType.BoundaryLine)
        {
            points = new List<Vector3>();
            boundaryType = BoundaryType.Excavation;
        }

        public override string GetIcon()
        {
            return boundaryType switch
            {
                BoundaryType.Excavation => "⛏️",
                BoundaryType.Safety => "🚧",
                BoundaryType.Property => "🏡",
                BoundaryType.Geological => "🗻",
                _ => "📏"
            };
        }
    }

    /// <summary>
    /// 边界线类型
    /// </summary>
    public enum BoundaryType
    {
        Excavation,   // 开挖边界
        Safety,       // 安全边界
        Property,     // 产权边界
        Geological    // 地质边界
    }

    /// <summary>
    /// 钻孔数据
    /// </summary>
    [Serializable]
    public class DrillHoleData : BlastingElement
    {
        public float depth;
        public float diameter;
        public float angle;
        public DrillHoleType holeType;
        public List<ChargeData> charges;

        public DrillHoleData(string id, string name) : base(id, name, BlastingElementType.DrillHole)
        {
            charges = new List<ChargeData>();
            holeType = DrillHoleType.Production;
        }

        public override string GetIcon()
        {
            return holeType switch
            {
                DrillHoleType.Production => "🕳️",
                DrillHoleType.Buffer => "🔘",
                DrillHoleType.Perimeter => "⚪",
                DrillHoleType.Presplit => "🎯",
                _ => "🕳️"
            };
        }

        public override List<string> GetAvailableActions()
        {
            var actions = base.GetAvailableActions();
            actions.AddRange(new[] { "design", "charge", "timing" });
            return actions;
        }
    }

    /// <summary>
    /// 钻孔类型
    /// </summary>
    public enum DrillHoleType
    {
        Production,  // 生产孔
        Buffer,      // 缓冲孔
        Perimeter,   // 周边孔
        Presplit     // 预裂孔
    }

    /// <summary>
    /// 装药数据
    /// </summary>
    [Serializable]
    public class ChargeData
    {
        public string explosiveType;
        public float weight;
        public float startDepth;
        public float endDepth;
        public int delayNumber;
    }

    /// <summary>
    /// 爆破块体数据
    /// </summary>
    [Serializable]
    public class BlastBlockData : BlastingElement
    {
        public float volume;
        public List<DrillHoleData> assignedHoles;
        public int sequence;

        public BlastBlockData(string id, string name) : base(id, name, BlastingElementType.BlastBlock)
        {
            assignedHoles = new List<DrillHoleData>();
        }

        public override List<string> GetAvailableActions()
        {
            var actions = base.GetAvailableActions();
            actions.AddRange(new[] { "sequence", "timing", "simulate" });
            return actions;
        }
    }
}