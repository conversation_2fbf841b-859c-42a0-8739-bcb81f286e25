using UnityEngine;

namespace BlastingDesign.Core.Camera
{
    /// <summary>
    /// 相机高度自适应系统 - 核心配置和生命周期管理
    /// </summary>
    public partial class CameraHeightAdaptationSystem : MonoBehaviour
    {
        #region 配置字段

        [Header("基础设置")]
        [Tooltip("目标相机，如果为空则自动查找Camera.main")]
        public UnityEngine.Camera targetCamera;

        [Tooltip("是否启用高度自适应")]
        public bool enableHeightAdaptation = true;

        [Tooltip("更新频率（每秒检查次数）")]
        public float updateFrequency = 30f;

        [Header("距离保持设置")]
        [Tooltip("保持距离的平滑度 (0-1)")]
        [Range(0f, 1f)]
        public float distanceSmoothness = 0.2f;

        [Tooltip("最小保持距离")]
        public float minKeepDistance = 0.5f;

        [Tooltip("最大保持距离")]
        public float maxKeepDistance = 100f;

        [Tooltip("默认距离（当无法检测到地形时使用）")]
        public float defaultDistance = 10f;

        [Header("地形检测设置")]
        [Tooltip("地形检测图层掩码")]
        public LayerMask terrainLayerMask = -1;

        [Tooltip("Cesium模型检测图层掩码")]
        public LayerMask cesiumLayerMask = -1;

        [Tooltip("基面Y坐标")]
        public float groundPlaneY = 0f;

        [Tooltip("射线检测最大距离")]
        public float maxRaycastDistance = 1000f;

        [Header("自适应行为设置")]
        [Tooltip("是否在相机移动时自动更新")]
        public bool updateOnCameraMove = true;

        [Tooltip("相机移动检测阈值")]
        public float movementThreshold = 0.1f;

        [Tooltip("是否使用屏幕中心点进行检测")]
        public bool useScreenCenter = true;

        [Tooltip("屏幕中心偏移（标准化坐标 0-1）")]
        public Vector2 screenCenterOffset = new Vector2(0.5f, 0.5f);

        [Tooltip("焦点计算俯视角度（度）- 用于稳定低视角时的焦点计算")]
        [Range(0f, 90f)]
        public float focusDownwardAngle = 75f;

        [Tooltip("是否只调整Y坐标，保持水平位置不变")]
        public bool onlyAdjustYCoordinate = true;

        [Header("调试设置")]
        [Tooltip("是否显示调试信息")]
        public bool showDebugInfo = false;

        [Tooltip("是否绘制射线")]
        public bool drawDebugRays = false;

        [Tooltip("强制模式：无视其他系统干扰，强制保持距离")]
        public bool forceMode = false;

        [Header("暂停控制设置")]
        [Tooltip("手动操作时暂停自动调整的时间（秒）")]
        public float suspensionDuration = 0.5f;

        #endregion

        #region 私有字段

        private float lastUpdateTime;
        private float updateInterval;
        private Vector3 lastCameraPosition;
        private float storedDistance;
        private Vector3 lastHitPoint;
        private bool hasValidDistance;
        private bool isInitialized;
        private bool isSuspended;
        private float suspensionEndTime;

        #endregion

        #region 生命周期方法

        void Awake()
        {
            Initialize();
        }

        void Start()
        {
            if (enableHeightAdaptation)
            {
                InitializeDistanceKeeping();
            }
        }

        void Update()
        {
            if (!enableHeightAdaptation || !isInitialized)
            {
                if (showDebugInfo)
                    Debug.Log($"[CameraHeightAdaptationSystem] Update 跳过: enableHeightAdaptation={enableHeightAdaptation}, isInitialized={isInitialized}");
                return;
            }

            // 检查暂停状态
            if (isSuspended)
            {
                if (Time.time >= suspensionEndTime)
                {
                    isSuspended = false;
                    if (showDebugInfo)
                        Debug.Log($"[CameraHeightAdaptationSystem] 暂停结束，恢复自动调整");
                }
                else
                {
                    if (showDebugInfo)
                        Debug.Log($"[CameraHeightAdaptationSystem] Update 跳过: 系统暂停中，剩余时间={suspensionEndTime - Time.time:F2}s");
                    return;
                }
            }

            // 检查是否需要更新
            bool shouldUpdate = ShouldUpdate();
            if (showDebugInfo)
            {
                Debug.Log($"[CameraHeightAdaptationSystem] Update 检查: shouldUpdate={shouldUpdate}");
            }

            if (shouldUpdate)
            {
                if (showDebugInfo)
                    Debug.Log($"[CameraHeightAdaptationSystem] 执行 UpdateHeightAdaptation");
                UpdateHeightAdaptation();
            }
        }

        void OnValidate()
        {
            // 确保设置值在合理范围内
            updateFrequency = Mathf.Max(1f, updateFrequency);
            minKeepDistance = Mathf.Max(0.1f, minKeepDistance);
            maxKeepDistance = Mathf.Max(minKeepDistance, maxKeepDistance);
            defaultDistance = Mathf.Clamp(defaultDistance, minKeepDistance, maxKeepDistance);
            screenCenterOffset = new Vector2(
                Mathf.Clamp01(screenCenterOffset.x),
                Mathf.Clamp01(screenCenterOffset.y)
            );
        }

        #endregion

        #region 初始化方法

        private void Initialize()
        {
            // 设置更新间隔
            updateInterval = 1f / updateFrequency;

            // 查找目标相机
            if (targetCamera == null)
            {
                targetCamera = UnityEngine.Camera.main;
            }

            if (targetCamera == null)
            {
                Debug.LogError("[CameraHeightAdaptationSystem] 未找到目标相机！");
                return;
            }

            // 初始化位置记录
            lastCameraPosition = targetCamera.transform.position;
            lastUpdateTime = Time.time;

            isInitialized = true;

            if (showDebugInfo)
            {
                Debug.Log($"[CameraHeightAdaptationSystem] 初始化完成 - 目标相机: {targetCamera.name}");
            }
        }

        private void InitializeDistanceKeeping()
        {
            if (!isInitialized) return;

            // 强制更新一次距离
            ForceUpdateDistance();

            if (showDebugInfo)
            {
                Debug.Log($"[CameraHeightAdaptationSystem] 距离保持系统初始化完成 - 初始距离: {storedDistance:F2}m");
            }
        }

        #endregion

        #region 更新逻辑

        private bool ShouldUpdate()
        {
            float currentTime = Time.time;
            bool timeToUpdate = currentTime - lastUpdateTime >= updateInterval;

            if (!timeToUpdate)
            {
                // 检查相机是否移动
                if (updateOnCameraMove)
                {
                    float moveDistance = Vector3.Distance(targetCamera.transform.position, lastCameraPosition);
                    if (moveDistance > movementThreshold)
                    {
                        timeToUpdate = true;
                    }
                }

                // 检查是否存在距离偏差（重要：确保距离锁定正常工作）
                if (hasValidDistance)
                {
                    Vector3 currentHitPoint = GetCurrentHitPoint();
                    if (currentHitPoint != Vector3.zero)
                    {
                        float currentDistance = Vector3.Distance(targetCamera.transform.position, currentHitPoint);
                        float distanceDifference = Mathf.Abs(currentDistance - storedDistance);

                        // 如果距离偏差超过阈值，强制更新
                        if (distanceDifference > 0.1f)
                        {
                            timeToUpdate = true;
                            if (showDebugInfo)
                            {
                                Debug.Log($"[CameraHeightAdaptationSystem] 检测到距离偏差: 当前={currentDistance:F2}m, 目标={storedDistance:F2}m, 差值={distanceDifference:F3}m");
                            }
                        }
                    }
                }
            }

            return timeToUpdate;
        }

        private void UpdateHeightAdaptation()
        {
            if (!isInitialized)
            {
                if (showDebugInfo)
                    Debug.Log("[CameraHeightAdaptationSystem] UpdateHeightAdaptation 跳过: 未初始化");
                return;
            }

            Vector3 currentCameraPosition = targetCamera.transform.position;

            // 获取当前应保持的距离点
            Vector3 targetHitPoint = GetCurrentHitPoint();

            if (showDebugInfo)
            {
                Debug.Log($"[CameraHeightAdaptationSystem] UpdateHeightAdaptation: 当前位置={currentCameraPosition}, 交点={targetHitPoint}");
            }

            if (targetHitPoint != Vector3.zero)
            {
                // 计算理想相机位置
                Vector3 idealCameraPosition = CalculateIdealCameraPosition(targetHitPoint);

                if (showDebugInfo)
                {
                    float currentDistance = Vector3.Distance(currentCameraPosition, targetHitPoint);
                    Debug.Log($"[CameraHeightAdaptationSystem] 理想位置={idealCameraPosition}, 当前距离={currentDistance:F2}m, 目标距离={storedDistance:F2}m");
                }

                // 应用平滑过渡
                Vector3 newCameraPosition = ApplySmoothTransition(currentCameraPosition, idealCameraPosition);

                if (showDebugInfo)
                {
                    float moveDistance = Vector3.Distance(currentCameraPosition, newCameraPosition);
                    Debug.Log($"[CameraHeightAdaptationSystem] 相机移动: {currentCameraPosition} → {newCameraPosition}, 移动距离={moveDistance:F3}m");
                }

                // 更新相机位置
                targetCamera.transform.position = newCameraPosition;

                // 不改变存储距离，只更新交点记录
                lastHitPoint = targetHitPoint;
                // storedDistance 保持不变，这是距离锁定的关键

                if (showDebugInfo && drawDebugRays)
                {
                    Debug.DrawLine(newCameraPosition, targetHitPoint, Color.green, 0.1f);
                }

                if (showDebugInfo)
                {
                    float actualDistance = Vector3.Distance(newCameraPosition, targetHitPoint);
                    Debug.Log($"[CameraHeightAdaptationSystem] 更新完成: 实际距离={actualDistance:F2}m, 目标距离={storedDistance:F2}m");
                }
            }
            else
            {
                if (showDebugInfo)
                    Debug.LogWarning("[CameraHeightAdaptationSystem] 无法获取有效的交点！");
            }

            // 更新记录
            lastCameraPosition = targetCamera.transform.position;
            lastUpdateTime = Time.time;
        }

        #endregion
    }
}