# Unity UI Toolkit USS 属性参考文档

此文档列出了Unity UI Toolkit中USS (Unity Style Sheets) 支持的所有属性。在编写USS时，请只使用本文档中列出的属性。

## 📋 目录

- [Unity UI Toolkit USS 属性参考文档](#unity-ui-toolkit-uss-属性参考文档)
  - [📋 目录](#-目录)
  - [布局属性 (Layout Properties)](#布局属性-layout-properties)
    - [Flexbox 相关](#flexbox-相关)
    - [对齐属性](#对齐属性)
    - [尺寸属性](#尺寸属性)
    - [边距和内边距](#边距和内边距)
  - [文本属性 (Text Properties)](#文本属性-text-properties)
  - [外观属性 (Appearance Properties)](#外观属性-appearance-properties)
  - [边框属性 (Border Properties)](#边框属性-border-properties)
  - [背景属性 (Background Properties)](#背景属性-background-properties)
  - [定位属性 (Position Properties)](#定位属性-position-properties)
  - [变换属性 (Transform Properties)](#变换属性-transform-properties)
  - [动画属性 (Animation Properties)](#动画属性-animation-properties)
  - [Unity特有属性 (Unity-Specific Properties)](#unity特有属性-unity-specific-properties)
    - [九宫格切片](#九宫格切片)
    - [文本渲染](#文本渲染)
  - [🔧 支持的值类型](#-支持的值类型)
    - [长度单位](#长度单位)
    - [颜色格式](#颜色格式)
    - [角度单位](#角度单位)
    - [时间单位](#时间单位)
  - [⚠️ 重要注意事项](#️-重要注意事项)
  - [📝 最佳实践](#-最佳实践)

---

## 布局属性 (Layout Properties)

### Flexbox 相关

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `display` | `display: <value>` | `flex`, `none` | 控制元素的显示类型 |
| `flex-direction` | `flex-direction: <value>` | `row`, `row-reverse`, `column`, `column-reverse` | 设置主轴方向 |
| `flex-wrap` | `flex-wrap: <value>` | `nowrap`, `wrap`, `wrap-reverse` | 控制是否换行 |
| `flex-grow` | `flex-grow: <number>` | `<number>` (≥0) | 元素的放大比例 |
| `flex-shrink` | `flex-shrink: <number>` | `<number>` (≥0) | 元素的缩小比例 |
| `flex-basis` | `flex-basis: <length>` | `<length>`, `auto` | 元素的基础大小 |
| `flex` | `flex: <grow> <shrink> <basis>` | 复合值 | flex-grow, flex-shrink, flex-basis 的简写 |

### 对齐属性

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `justify-content` | `justify-content: <value>` | `flex-start`, `flex-end`, `center`, `space-between`, `space-around`, `space-evenly` | 主轴对齐方式 |
| `align-items` | `align-items: <value>` | `flex-start`, `flex-end`, `center`, `stretch` | 交叉轴对齐方式 |
| `align-self` | `align-self: <value>` | `auto`, `flex-start`, `flex-end`, `center`, `stretch` | 单个元素交叉轴对齐 |
| `align-content` | `align-content: <value>` | `flex-start`, `flex-end`, `center`, `stretch`, `space-between`, `space-around` | 多行内容对齐 |

### 尺寸属性

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `width` | `width: <length>` | `<length>`, `<percentage>`, `auto` | 元素宽度 |
| `height` | `height: <length>` | `<length>`, `<percentage>`, `auto` | 元素高度 |
| `min-width` | `min-width: <length>` | `<length>`, `<percentage>`, `auto` | 最小宽度 |
| `min-height` | `min-height: <length>` | `<length>`, `<percentage>`, `auto` | 最小高度 |
| `max-width` | `max-width: <length>` | `<length>`, `<percentage>`, `none` | 最大宽度 |
| `max-height` | `max-height: <length>` | `<length>`, `<percentage>`, `none` | 最大高度 |

### 边距和内边距

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `margin` | `margin: <length>` | `<length>`, `<percentage>`, `auto` | 外边距简写 |
| `margin-top` | `margin-top: <length>` | `<length>`, `<percentage>`, `auto` | 上外边距 |
| `margin-right` | `margin-right: <length>` | `<length>`, `<percentage>`, `auto` | 右外边距 |
| `margin-bottom` | `margin-bottom: <length>` | `<length>`, `<percentage>`, `auto` | 下外边距 |
| `margin-left` | `margin-left: <length>` | `<length>`, `<percentage>`, `auto` | 左外边距 |
| `padding` | `padding: <length>` | `<length>`, `<percentage>` | 内边距简写 |
| `padding-top` | `padding-top: <length>` | `<length>`, `<percentage>` | 上内边距 |
| `padding-right` | `padding-right: <length>` | `<length>`, `<percentage>` | 右内边距 |
| `padding-bottom` | `padding-bottom: <length>` | `<length>`, `<percentage>` | 下内边距 |
| `padding-left` | `padding-left: <length>` | `<length>`, `<percentage>` | 左内边距 |

---

## 文本属性 (Text Properties)

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `color` | `color: <color>` | `<color>` | 文本颜色 |
| `font-size` | `font-size: <length>` | `<length>`, `<percentage>` | 字体大小 |
| `-unity-font` | `-unity-font: <font>` | Unity Font 资源 | 字体资源 |
| `-unity-font-style` | `-unity-font-style: <value>` | `normal`, `italic`, `bold`, `bold-and-italic` | 字体样式和粗细 |
| `-unity-font-definition` | `-unity-font-definition: <value>` | Unity FontDefinition | 字体定义 |
| `-unity-text-align` | `-unity-text-align: <value>` | `upper-left`, `upper-center`, `upper-right`, `middle-left`, `middle-center`, `middle-right`, `lower-left`, `lower-center`, `lower-right` | 文本对齐方式 |
| `letter-spacing` | `letter-spacing: <length>` | `<length>` | 字符间距 |
| `word-spacing` | `word-spacing: <length>` | `<length>` | 单词间距 |
| `white-space` | `white-space: <value>` | `normal`, `nowrap` | 空白符处理 |
| `text-overflow` | `text-overflow: <value>` | `clip`, `ellipsis` | 文本溢出处理 |
| `-unity-text-outline` | `-unity-text-outline: <width> <color>` | `<length> <color>` | 文本描边 |
| `-unity-text-outline-width` | `-unity-text-outline-width: <length>` | `<length>` | 文本描边宽度 |
| `-unity-text-outline-color` | `-unity-text-outline-color: <color>` | `<color>` | 文本描边颜色 |

---

## 外观属性 (Appearance Properties)

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `opacity` | `opacity: <number>` | `0` - `1` | 透明度 |
| `visibility` | `visibility: <value>` | `visible`, `hidden` | 可见性 |
| `overflow` | `overflow: <value>` | `visible`, `hidden` | 内容溢出处理 |
| `cursor` | `cursor: <value>` | Unity Cursor 资源 | 鼠标光标 |

---

## 边框属性 (Border Properties)

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `border-width` | `border-width: <length>` | `<length>` | 边框宽度简写 |
| `border-top-width` | `border-top-width: <length>` | `<length>` | 上边框宽度 |
| `border-right-width` | `border-right-width: <length>` | `<length>` | 右边框宽度 |
| `border-bottom-width` | `border-bottom-width: <length>` | `<length>` | 下边框宽度 |
| `border-left-width` | `border-left-width: <length>` | `<length>` | 左边框宽度 |
| `border-color` | `border-color: <color>` | `<color>` | 边框颜色简写 |
| `border-top-color` | `border-top-color: <color>` | `<color>` | 上边框颜色 |
| `border-right-color` | `border-right-color: <color>` | `<color>` | 右边框颜色 |
| `border-bottom-color` | `border-bottom-color: <color>` | `<color>` | 下边框颜色 |
| `border-left-color` | `border-left-color: <color>` | `<color>` | 左边框颜色 |
| `border-radius` | `border-radius: <length>` | `<length>`, `<percentage>` | 圆角半径简写 |
| `border-top-left-radius` | `border-top-left-radius: <length>` | `<length>`, `<percentage>` | 左上圆角 |
| `border-top-right-radius` | `border-top-right-radius: <length>` | `<length>`, `<percentage>` | 右上圆角 |
| `border-bottom-right-radius` | `border-bottom-right-radius: <length>` | `<length>`, `<percentage>` | 右下圆角 |
| `border-bottom-left-radius` | `border-bottom-left-radius: <length>` | `<length>`, `<percentage>` | 左下圆角 |

---

## 背景属性 (Background Properties)

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `background-color` | `background-color: <color>` | `<color>` | 背景颜色 |
| `background-image` | `background-image: <image>` | Unity Texture2D 资源, `none` | 背景图像 |
| `-unity-background-scale-mode` | `-unity-background-scale-mode: <value>` | `stretch-to-fill`, `scale-and-crop`, `scale-to-fit` | 背景图缩放模式 |
| `-unity-background-image-tint-color` | `-unity-background-image-tint-color: <color>` | `<color>` | 背景图着色 |

---

## 定位属性 (Position Properties)

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `position` | `position: <value>` | `relative`, `absolute` | 定位类型 |
| `top` | `top: <length>` | `<length>`, `<percentage>`, `auto` | 顶部偏移 |
| `right` | `right: <length>` | `<length>`, `<percentage>`, `auto` | 右侧偏移 |
| `bottom` | `bottom: <length>` | `<length>`, `<percentage>`, `auto` | 底部偏移 |
| `left` | `left: <length>` | `<length>`, `<percentage>`, `auto` | 左侧偏移 |

---

## 变换属性 (Transform Properties)

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `transform-origin` | `transform-origin: <position>` | `<length>`, `<percentage>`, 关键字 | 变换原点 |
| `translate` | `translate: <x> <y>` | `<length>`, `<percentage>` | 平移变换 |
| `rotate` | `rotate: <angle>` | `<angle>` (deg, rad) | 旋转变换 |
| `scale` | `scale: <number> [<number>]` | `<number>` | 缩放变换 |

---

## 动画属性 (Animation Properties)

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `transition` | `transition: <property> <duration> <timing> <delay>` | 复合值 | 过渡动画简写 |
| `transition-property` | `transition-property: <property>` | CSS 属性名, `all` | 过渡属性 |
| `transition-duration` | `transition-duration: <time>` | `<time>` (s, ms) | 过渡持续时间 |
| `transition-timing-function` | `transition-timing-function: <value>` | `ease`, `ease-in`, `ease-out`, `ease-in-out`, `linear` | 过渡时间函数 |
| `transition-delay` | `transition-delay: <time>` | `<time>` (s, ms) | 过渡延迟 |

---

## Unity特有属性 (Unity-Specific Properties)

### 九宫格切片

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `-unity-slice-left` | `-unity-slice-left: <length>` | `<length>` (像素) | 左侧切片 |
| `-unity-slice-top` | `-unity-slice-top: <length>` | `<length>` (像素) | 顶部切片 |
| `-unity-slice-right` | `-unity-slice-right: <length>` | `<length>` (像素) | 右侧切片 |
| `-unity-slice-bottom` | `-unity-slice-bottom: <length>` | `<length>` (像素) | 底部切片 |

### 文本渲染

| 属性 | 语法 | 支持值 | 描述 |
|-----|------|--------|------|
| `-unity-text-generator` | `-unity-text-generator: <value>` | `legacy`, `advanced` | 文本生成器类型 |

---

## 🔧 支持的值类型

### 长度单位
- `px` - 像素（推荐）
- `%` - 百分比（相对于父元素）

### 颜色格式
- 十六进制：`#RGB`, `#RRGGBB`, `#RRGGBBAA`
- RGB/RGBA：`rgb(r, g, b)`, `rgba(r, g, b, a)`
- 关键字：`transparent`, `initial`

### 角度单位
- `deg` - 度数
- `rad` - 弧度

### 时间单位
- `s` - 秒
- `ms` - 毫秒

---

## ⚠️ 重要注意事项

1. **不支持 CSS 简写属性**：
   - ❌ `border: 1px solid red` 
   - ✅ `border-width: 1px; border-color: red;`

2. **不支持 `gap` 属性**：
   - 使用 `margin` 替代

3. **不支持 CSS Grid**：
   - 使用 Flexbox 进行布局

4. **伪类支持有限**：
   - 仅支持 `:hover`, `:active`, `:focus`, `:checked`, `:disabled`

5. **变量支持**：
   - 支持 CSS 自定义属性：`var(--custom-property)`

6. **继承属性**：
   - 文本相关属性会自动继承
   - 布局属性不会继承

7. **更多不支持的属性**：
   - `pointer-events`
   - `box-shadow`
   - `filter`
   - `transform`
   - `animation`

---

## 📝 最佳实践

1. **优先使用像素单位**：更精确的控制
2. **合理使用百分比**：实现响应式布局
3. **避免过度嵌套**：保持简洁的选择器
4. **使用CSS变量**：便于主题切换和维护
5. **遵循命名约定**：使用语义化的类名

---

*此文档基于 Unity 6000.1 版本的 UI Toolkit，后续版本可能会有所变化。*