using UnityEngine;
using BlastingDesign.Utils;
using System.Threading.Tasks;

namespace BlastingDesign.Database.Demo
{
    /// <summary>
    /// Microsoft.Data.Sqlite 测试演示
    /// 展示如何使用更新后的 SqliteTest 脚本
    /// </summary>
    public class SqliteTestDemo : MonoBehaviour
    {
        [Header("演示配置")]
        [SerializeField] private KeyCode testKey = KeyCode.F6;
        [SerializeField] private KeyCode asyncTestKey = KeyCode.F7;
        [SerializeField] private KeyCode infoKey = KeyCode.F8;
        [SerializeField] private bool showGUI = true;

        private SqliteTest sqliteTest;

        void Start()
        {
            // 获取或添加 SqliteTest 组件
            sqliteTest = GetComponent<SqliteTest>();
            if (sqliteTest == null)
            {
                sqliteTest = gameObject.AddComponent<SqliteTest>();
                Logging.LogInfo("SqliteTestDemo", "已自动添加 SqliteTest 组件");
            }

            ShowInstructions();
        }

        void Update()
        {
            if (Input.GetKeyDown(testKey))
            {
                TestSyncOperations();
            }

            if (Input.GetKeyDown(asyncTestKey))
            {
                _ = TestAsyncOperations();
            }

            if (Input.GetKeyDown(infoKey))
            {
                ShowDatabaseInfo();
            }
        }

        /// <summary>
        /// 显示使用说明
        /// </summary>
        private void ShowInstructions()
        {
            Logging.LogInfo("SqliteTestDemo", "=== Microsoft.Data.Sqlite 测试演示 ===");
            Logging.LogInfo("SqliteTestDemo", "");
            Logging.LogInfo("SqliteTestDemo", "快捷键操作：");
            Logging.LogInfo("SqliteTestDemo", $"{testKey} - 执行同步数据库操作测试");
            Logging.LogInfo("SqliteTestDemo", $"{asyncTestKey} - 执行异步数据库操作测试");
            Logging.LogInfo("SqliteTestDemo", $"{infoKey} - 显示数据库信息");
            Logging.LogInfo("SqliteTestDemo", "");
            Logging.LogInfo("SqliteTestDemo", "特性：");
            Logging.LogInfo("SqliteTestDemo", "• 使用 Microsoft.Data.Sqlite NuGet 包");
            Logging.LogInfo("SqliteTestDemo", "• 支持同步和异步操作");
            Logging.LogInfo("SqliteTestDemo", "• 内存数据库和文件数据库支持");
            Logging.LogInfo("SqliteTestDemo", "• 现代化的连接字符串格式");
            Logging.LogInfo("SqliteTestDemo", "• 完整的错误处理和日志记录");
            Logging.LogInfo("SqliteTestDemo", "");
            Logging.LogInfo("SqliteTestDemo", "如果遇到 SQLitePCL 错误，请：");
            Logging.LogInfo("SqliteTestDemo", "1. 安装 SQLitePCLRaw.bundle_e_sqlite3 NuGet 包");
            Logging.LogInfo("SqliteTestDemo", "2. 或查看 SQLitePCL错误解决方案.md 文档");
        }

        /// <summary>
        /// 测试同步操作
        /// </summary>
        private void TestSyncOperations()
        {
            if (sqliteTest == null)
            {
                Logging.LogError("SqliteTestDemo", "SqliteTest 组件未找到");
                return;
            }

            Logging.LogInfo("SqliteTestDemo", "开始同步操作测试...");

            // 重新测试连接
            sqliteTest.RetestConnection();

            Logging.LogInfo("SqliteTestDemo", "同步操作测试完成");
        }

        /// <summary>
        /// 测试异步操作
        /// </summary>
        private async Task TestAsyncOperations()
        {
            if (sqliteTest == null)
            {
                Logging.LogError("SqliteTestDemo", "SqliteTest 组件未找到");
                return;
            }

            Logging.LogInfo("SqliteTestDemo", "开始异步操作测试...");

            try
            {
                // 这里可以添加更多的异步测试逻辑
                await Task.Delay(100); // 模拟异步操作

                // 获取数据库信息
                sqliteTest.GetDatabaseInfo();

                Logging.LogInfo("SqliteTestDemo", "异步操作测试完成");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("SqliteTestDemo", $"异步操作测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示数据库信息
        /// </summary>
        private void ShowDatabaseInfo()
        {
            if (sqliteTest == null)
            {
                Logging.LogError("SqliteTestDemo", "SqliteTest 组件未找到");
                return;
            }

            Logging.LogInfo("SqliteTestDemo", "获取数据库信息...");
            sqliteTest.GetDatabaseInfo();
        }

        /// <summary>
        /// GUI 显示
        /// </summary>
        void OnGUI()
        {
            if (!showGUI) return;

            GUILayout.BeginArea(new Rect(10, 10, 500, 300));
            GUILayout.BeginVertical("box");

            GUILayout.Label("Microsoft.Data.Sqlite 测试演示", GUI.skin.label);
            GUILayout.Space(10);

            // 状态信息
            if (sqliteTest != null)
            {
                GUILayout.Label("SqliteTest 组件: ✓ 已加载", GUI.skin.label);
            }
            else
            {
                GUILayout.Label("SqliteTest 组件: ✗ 未找到", GUI.skin.label);
            }

            GUILayout.Space(10);

            // 操作按钮
            GUILayout.BeginHorizontal();
            if (GUILayout.Button($"同步测试 ({testKey})"))
            {
                TestSyncOperations();
            }
            if (GUILayout.Button($"异步测试 ({asyncTestKey})"))
            {
                _ = TestAsyncOperations();
            }
            GUILayout.EndHorizontal();

            GUILayout.BeginHorizontal();
            if (GUILayout.Button($"数据库信息 ({infoKey})"))
            {
                ShowDatabaseInfo();
            }
            if (GUILayout.Button("重新连接"))
            {
                if (sqliteTest != null)
                {
                    sqliteTest.RetestConnection();
                }
            }
            GUILayout.EndHorizontal();

            GUILayout.Space(10);

            // 配置信息
            GUILayout.Label("配置选项:", GUI.skin.label);
            if (sqliteTest != null)
            {
                // 这里可以显示 SqliteTest 的配置信息
                GUILayout.Label("• 数据库类型: SQLite", GUI.skin.label);
                GUILayout.Label("• 连接库: Microsoft.Data.Sqlite", GUI.skin.label);
                GUILayout.Label("• 支持异步: ✓", GUI.skin.label);
            }

            GUILayout.Space(10);

            // 使用说明
            GUILayout.Label("使用说明:", GUI.skin.label);
            GUILayout.Label("1. 确保已通过 NuGet 安装 Microsoft.Data.Sqlite", GUI.skin.label);
            GUILayout.Label("2. 配置 SqliteTest 组件的参数", GUI.skin.label);
            GUILayout.Label("3. 使用快捷键或按钮进行测试", GUI.skin.label);

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        /// <summary>
        /// 右键菜单 - 显示帮助
        /// </summary>
        [ContextMenu("显示帮助信息")]
        public void ShowHelp()
        {
            ShowInstructions();
        }

        /// <summary>
        /// 右键菜单 - 运行完整测试
        /// </summary>
        [ContextMenu("运行完整测试")]
        public async void RunFullTest()
        {
            Logging.LogInfo("SqliteTestDemo", "开始运行完整测试...");

            TestSyncOperations();
            await Task.Delay(1000);

            await TestAsyncOperations();
            await Task.Delay(1000);

            ShowDatabaseInfo();

            Logging.LogInfo("SqliteTestDemo", "完整测试运行完成");
        }

        /// <summary>
        /// 右键菜单 - 检查环境
        /// </summary>
        [ContextMenu("检查环境")]
        public void CheckEnvironment()
        {
            Logging.LogInfo("SqliteTestDemo", "=== 环境检查 ===");

            // 检查 SqliteTest 组件
            bool hasComponent = GetComponent<SqliteTest>() != null;
            Logging.LogInfo("SqliteTestDemo", $"SqliteTest 组件: {(hasComponent ? "✓ 已找到" : "✗ 未找到")}");

            // 检查 Unity 版本
            Logging.LogInfo("SqliteTestDemo", $"Unity 版本: {Application.unityVersion}");

            // 检查平台
            Logging.LogInfo("SqliteTestDemo", $"运行平台: {Application.platform}");

            // 检查数据路径
            Logging.LogInfo("SqliteTestDemo", $"持久化数据路径: {Application.persistentDataPath}");

            // 检查 .NET 兼容性
            Logging.LogInfo("SqliteTestDemo", $".NET 版本: {System.Environment.Version}");

            // 检查 Microsoft.Data.Sqlite 是否可用
            try
            {
                var sqliteType = System.Type.GetType("Microsoft.Data.Sqlite.SqliteConnection, Microsoft.Data.Sqlite");
                if (sqliteType != null)
                {
                    Logging.LogInfo("SqliteTestDemo", "✓ Microsoft.Data.Sqlite 已安装");
                }
                else
                {
                    Logging.LogError("SqliteTestDemo", "✗ Microsoft.Data.Sqlite 未找到");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("SqliteTestDemo", $"✗ Microsoft.Data.Sqlite 检查失败: {ex.Message}");
            }

            // 检查 SQLitePCL 是否可用
            try
            {
                var sqlitePclType = System.Type.GetType("SQLitePCL.raw, SQLitePCLRaw.core");
                if (sqlitePclType != null)
                {
                    Logging.LogInfo("SqliteTestDemo", "✓ SQLitePCL.raw 已安装");

                    // 检查 Bundle 包
                    var bundleType = System.Type.GetType("SQLitePCL.Batteries, SQLitePCLRaw.batteries_v2");
                    if (bundleType != null)
                    {
                        Logging.LogInfo("SqliteTestDemo", "✓ SQLitePCL Batteries 已安装");
                    }
                    else
                    {
                        Logging.LogWarning("SqliteTestDemo", "⚠ SQLitePCL Batteries 未找到，可能需要安装 bundle 包");
                    }
                }
                else
                {
                    Logging.LogError("SqliteTestDemo", "✗ SQLitePCL.raw 未找到");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("SqliteTestDemo", $"✗ SQLitePCL 检查失败: {ex.Message}");
            }

            Logging.LogInfo("SqliteTestDemo", "");
            Logging.LogInfo("SqliteTestDemo", "如果发现问题，请：");
            Logging.LogInfo("SqliteTestDemo", "1. 确保已安装 Microsoft.Data.Sqlite NuGet 包");
            Logging.LogInfo("SqliteTestDemo", "2. 安装 SQLitePCLRaw.bundle_e_sqlite3 包");
            Logging.LogInfo("SqliteTestDemo", "3. 检查 Player Settings 中的 .NET 兼容性设置");
            Logging.LogInfo("SqliteTestDemo", "4. 查看 SQLitePCL错误解决方案.md 文档");
            Logging.LogInfo("SqliteTestDemo", "环境检查完成");
        }
    }
}
