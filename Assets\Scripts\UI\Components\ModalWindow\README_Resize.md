# 模态窗口调整尺寸功能

## 功能概述

为模态窗口系统添加了完整的窗口尺寸调整功能，用户可以通过拖拽窗口边框来动态调整窗口大小。

## 主要特性

### 1. 调整区域
- **右边框**：水平调整窗口宽度
- **底边框**：垂直调整窗口高度  
- **右下角**：双向调整窗口尺寸（最常用）

### 2. 尺寸限制
- **最小尺寸**：防止窗口过小影响使用
- **最大尺寸**：防止窗口超出屏幕范围
- **边界限制**：确保窗口不会超出父容器

### 3. 视觉反馈
- 调整区域悬停时显示半透明高亮
- 调整过程中窗口透明度变化
- 右下角有可见的调整手柄

## 使用方法

### 基本使用

```csharp
// 创建可调整尺寸的窗口
var window = ModalWindowManager.Instance.CreateWindow("测试窗口", new Vector2(500, 400));

// 启用调整尺寸功能（默认已启用）
window.IsResizable = true;

// 设置尺寸限制
window.MinSize = new Vector2(300, 200);
window.MaxSize = new Vector2(800, 600);
```

### 禁用调整尺寸

```csharp
// 禁用调整尺寸功能
window.IsResizable = false;
```

### 程序化调整尺寸

```csharp
// 直接设置窗口尺寸
window.Size = new Vector2(600, 450);

// 带动画效果的尺寸调整（预留接口）
window.SetSize(new Vector2(600, 450), animated: true);
```

## 配置选项

### 窗口属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `IsResizable` | bool | true | 是否允许调整尺寸 |
| `MinSize` | Vector2 | (300, 200) | 最小窗口尺寸 |
| `MaxSize` | Vector2 | (1200, 800) | 最大窗口尺寸 |

### CSS样式类

| 样式类 | 说明 |
|--------|------|
| `.resize-handle` | 调整手柄基础样式 |
| `.resize-handle-right` | 右边框调整手柄 |
| `.resize-handle-bottom` | 底边框调整手柄 |
| `.resize-handle-bottom-right` | 右下角调整手柄 |
| `.modal-window.resizing` | 调整过程中的窗口样式 |

## 测试功能

### 使用测试脚本

1. 在场景中添加 `ModalWindowResizeTest` 组件
2. 运行场景后按 `F1` 键打开测试窗口
3. 拖拽窗口边框测试调整功能
4. 按 `ESC` 键关闭测试窗口

### 测试内容

- 实时显示当前窗口尺寸
- 显示尺寸限制范围
- 提供重置尺寸按钮
- 提供启用/禁用调整功能按钮

## 技术实现

### 核心组件

1. **ModalWindow.Core.cs**：包含调整尺寸的核心逻辑
2. **ModalWindow.uxml**：定义调整手柄的UI结构
3. **ModalWindow.uss**：定义调整手柄的样式

### 事件处理

- 鼠标按下：开始调整模式
- 鼠标移动：实时计算新尺寸
- 鼠标释放：结束调整模式
- 全局事件：支持鼠标移出窗口区域的调整

### 尺寸计算

```csharp
// 根据调整方向计算新尺寸
switch (resizeDirection)
{
    case ResizeDirection.Right:
        newSize.x += deltaPosition.x;
        break;
    case ResizeDirection.Bottom:
        newSize.y += deltaPosition.y;
        break;
    case ResizeDirection.BottomRight:
        newSize.x += deltaPosition.x;
        newSize.y += deltaPosition.y;
        break;
}
```

## 扩展功能

### 支持更多调整方向

当前实现支持右边框、底边框和右下角调整。如需支持其他方向（如左边框、顶边框等），可以：

1. 在 `ResizeDirection` 枚举中添加新方向
2. 在UXML中添加对应的调整手柄
3. 在CSS中添加对应的样式
4. 在计算逻辑中处理新方向

### 动画效果

预留了动画接口，可以集成第三方Tween库实现平滑的尺寸调整动画。

## 注意事项

1. 调整尺寸功能与窗口拖拽功能完全独立，不会产生冲突
2. 尺寸限制会自动考虑父容器边界
3. 调整过程中会保持窗口在可视区域内
4. Unity UI Toolkit不支持鼠标指针样式，无法显示调整光标
