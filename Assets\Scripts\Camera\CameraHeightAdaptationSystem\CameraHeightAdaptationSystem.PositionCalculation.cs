using UnityEngine;

namespace BlastingDesign.Core.Camera
{
    /// <summary>
    /// 相机高度自适应系统 - 位置计算逻辑
    /// </summary>
    public partial class CameraHeightAdaptationSystem
    {
        #region 位置计算方法

        private Vector3 CalculateIdealCameraPosition(Vector3 hitPoint)
        {
            Vector3 currentCameraPosition = targetCamera.transform.position;
            float currentDistanceToHit = Vector3.Distance(currentCameraPosition, hitPoint);

            // 如果没有有效的存储距离，使用当前实际距离进行初始化
            if (!hasValidDistance)
            {
                storedDistance = currentDistanceToHit;
                hasValidDistance = true;

                if (showDebugInfo)
                {
                    Debug.Log($"[CameraHeightAdaptationSystem] 初始化距离: 使用当前距离 {storedDistance:F2}m");
                }
            }

            Vector3 idealPosition;

            if (onlyAdjustYCoordinate)
            {
                // 只调整Y坐标的新计算方法
                // 保持相机的X和Z坐标不变，只计算理想的Y坐标
                idealPosition = currentCameraPosition; // 保持X和Z不变

                // 计算在当前X,Z位置下，距离目标交点指定距离的理想Y坐标
                float horizontalDistance = Mathf.Sqrt(
                    Mathf.Pow(currentCameraPosition.x - hitPoint.x, 2) +
                    Mathf.Pow(currentCameraPosition.z - hitPoint.z, 2)
                );

                // 使用勾股定理计算理想的垂直距离
                float targetVerticalDistance = Mathf.Sqrt(Mathf.Max(0, storedDistance * storedDistance - horizontalDistance * horizontalDistance));

                // 计算理想的Y坐标（在交点上方）
                idealPosition.y = hitPoint.y + targetVerticalDistance;

                if (showDebugInfo)
                {
                    float distanceDifference = Mathf.Abs(currentDistanceToHit - storedDistance);
                    Debug.Log($"[CameraHeightAdaptationSystem] Y坐标调整: 当前距离={currentDistanceToHit:F2}m, 目标距离={storedDistance:F2}m");
                    Debug.Log($"[CameraHeightAdaptationSystem] 水平距离={horizontalDistance:F2}m, 垂直距离={targetVerticalDistance:F2}m");
                    Debug.Log($"[CameraHeightAdaptationSystem] 位置调整: {currentCameraPosition} → {idealPosition}");
                }
            }
            else
            {
                // 原来的计算方法（向后兼容）
                // 计算从交点到相机的方向
                Vector3 directionToCamera = (currentCameraPosition - hitPoint).normalized;

                // 如果方向无效，使用向上方向
                if (directionToCamera.magnitude < 0.1f)
                {
                    directionToCamera = Vector3.up;
                }

                // 计算理想位置：保持存储的距离
                idealPosition = hitPoint + directionToCamera * storedDistance;

                if (showDebugInfo)
                {
                    float distanceDifference = Mathf.Abs(currentDistanceToHit - storedDistance);
                    Debug.Log($"[CameraHeightAdaptationSystem] 3D位置调整: 当前距离={currentDistanceToHit:F2}m, 目标距离={storedDistance:F2}m, 差值={distanceDifference:F3}m");
                    Debug.Log($"[CameraHeightAdaptationSystem] 位置调整: {currentCameraPosition} → {idealPosition}");
                }
            }

            return idealPosition;
        }

        private Vector3 ApplySmoothTransition(Vector3 currentPosition, Vector3 targetPosition)
        {
            if (onlyAdjustYCoordinate)
            {
                // 只比较Y坐标的差值，因为我们只调整高度
                float heightDifference = Mathf.Abs(targetPosition.y - currentPosition.y);

                // 如果高度差很小，不需要调整
                if (heightDifference < 0.01f)
                {
                    return currentPosition;
                }

                // 创建新位置，保持X和Z坐标不变，只调整Y坐标
                Vector3 newPosition = currentPosition;

                if (distanceSmoothness <= 0)
                {
                    // 立即调整到目标Y坐标
                    newPosition.y = targetPosition.y;

                    if (showDebugInfo)
                    {
                        Debug.Log($"[CameraHeightAdaptationSystem] 立即Y调整: {currentPosition.y:F3} → {targetPosition.y:F3}, 高度差={heightDifference:F3}m");
                    }
                }
                else
                {
                    // 平滑过渡到目标Y坐标
                    float deltaTime = Time.deltaTime;
                    float lerpFactor = distanceSmoothness * deltaTime * 10f;
                    newPosition.y = Mathf.Lerp(currentPosition.y, targetPosition.y, lerpFactor);

                    if (showDebugInfo)
                    {
                        float actualHeightMove = Mathf.Abs(newPosition.y - currentPosition.y);
                        Debug.Log($"[CameraHeightAdaptationSystem] 平滑Y过渡: 需要调整={heightDifference:F3}m, 插值系数={lerpFactor:F3}, 实际移动={actualHeightMove:F3}m");
                    }
                }

                return newPosition;
            }
            else
            {
                // 原来的3D位置调整方法（向后兼容）
                float positionDistance = Vector3.Distance(currentPosition, targetPosition);

                // 如果距离差很小，不需要调整
                if (positionDistance < 0.01f)
                {
                    return currentPosition;
                }

                if (distanceSmoothness <= 0)
                {
                    if (showDebugInfo)
                    {
                        Debug.Log($"[CameraHeightAdaptationSystem] 立即3D调整: {currentPosition} → {targetPosition}, 距离差={positionDistance:F3}m");
                    }
                    return targetPosition;
                }

                float deltaTime = Time.deltaTime;
                float lerpFactor = distanceSmoothness * deltaTime * 10f;
                Vector3 newPosition = Vector3.Lerp(currentPosition, targetPosition, lerpFactor);

                if (showDebugInfo)
                {
                    float moveDistance = Vector3.Distance(currentPosition, newPosition);
                    Debug.Log($"[CameraHeightAdaptationSystem] 平滑3D过渡: 需要调整={positionDistance:F3}m, 插值系数={lerpFactor:F3}, 实际移动={moveDistance:F3}m");
                }

                return newPosition;
            }
        }

        #endregion
    }
}