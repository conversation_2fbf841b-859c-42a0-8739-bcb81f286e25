﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>SQLitePCLRaw.core</id>
    <version>2.1.11</version>
    <authors><PERSON></authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <description>SQLitePCL.raw is a Portable Class Library (PCL) for low-level (raw) access to SQLite.  This package does not provide an API which is friendly to app developers.  Rather, it provides an API which handles platform and configuration issues, upon which a friendlier API can be built.  In order to use this package, you will need to also add one of the SQLitePCLRaw.provider.* packages and call raw.SetProvider().  Convenience packages are named SQLitePCLRaw.bundle_*.</description>
    <copyright>Copyright 2014-2024 SourceGear, LLC</copyright>
    <tags>sqlite</tags>
    <repository type="git" url="https://github.com/ericsink/SQLitePCL.raw" />
    <dependencies>
      <group targetFramework=".NETStandard2.0">
        <dependency id="System.Memory" version="4.5.3" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>