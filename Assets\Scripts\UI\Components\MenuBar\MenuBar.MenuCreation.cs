using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Config;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// MenuBar菜单创建类 - 负责菜单结构的创建和构建
    /// </summary>
    public partial class MenuBar : UIElementBase, IMenuComponent
    {
        #region 菜单创建

        /// <summary>
        /// 创建菜单分类
        /// </summary>
        private void CreateMenuCategory(MenuBarConfig.MenuCategory category)
        {
            // 创建菜单按钮
            var menuButton = new Button
            {
                name = $"{category.name}-menu-btn",
                text = category.displayName
            };
            menuButton.AddToClassList("menu-button");

            if (!string.IsNullOrEmpty(category.customCssClass))
            {
                menuButton.AddToClassList(category.customCssClass);
            }

            // 注册点击事件
            menuButton.RegisterCallback<ClickEvent>(evt => OnMenuButtonClicked(category.name, menuButton));

            // 添加到容器
            menuCategories.Add(menuButton);
            menuButtons[category.name] = menuButton;

            // 创建下拉菜单
            CreateDropdownMenu(category);
        }

        /// <summary>
        /// 创建下拉菜单
        /// </summary>
        private void CreateDropdownMenu(MenuBarConfig.MenuCategory category)
        {
            var dropdown = new VisualElement
            {
                name = $"{category.name}-dropdown"
            };
            dropdown.AddToClassList("dropdown-menu");

            // 初始状态设置为隐藏
            dropdown.style.display = DisplayStyle.None;
            dropdown.style.position = Position.Absolute;
            dropdown.pickingMode = PickingMode.Position;

            // 设置层级，确保下拉菜单在最上层
            dropdown.style.unitySliceTop = 1000;

            // 应用样式表 - 确保下拉菜单能访问MenuBar的样式
            ApplyStyleSheetToDropdown(dropdown);

            // 生成菜单项
            foreach (var item in category.items)
            {
                if (!item.visible) continue;

                if (item.isSeparator)
                {
                    var separator = new VisualElement();
                    separator.AddToClassList("dropdown-separator");
                    dropdown.Add(separator);
                }
                else
                {
                    CreateMenuItem(dropdown, item);
                }
            }

            // 直接添加到根元素
            if (rootElement != null)
            {
                rootElement.Add(dropdown);
                dropdownMenus[category.name] = dropdown;
                Logging.LogInfo("MenuBar", $"创建下拉菜单: {category.name}, 菜单项数量: {category.items.Count}");
            }
            else
            {
                Logging.LogWarning("MenuBar", $"根元素为空，无法添加下拉菜单: {category.name}");
            }
        }

        /// <summary>
        /// 创建菜单项
        /// </summary>
        private void CreateMenuItem(VisualElement parent, MenuBarConfig.MenuItem item)
        {
            var menuItem = new Button
            {
                name = item.name,
                text = item.displayName
            };
            menuItem.AddToClassList("dropdown-item");

            if (!string.IsNullOrEmpty(item.customCssClass))
            {
                menuItem.AddToClassList(item.customCssClass);
            }

            // 设置启用状态
            menuItem.SetEnabled(item.enabled);

            // 设置工具提示
            if (!string.IsNullOrEmpty(item.tooltip))
            {
                menuItem.tooltip = item.tooltip;
            }

            // 注册点击事件
            if (!string.IsNullOrEmpty(item.callbackName))
            {
                menuItem.RegisterCallback<ClickEvent>(evt => OnMenuItemClicked(item));
            }

            parent.Add(menuItem);

            // 处理子菜单
            if (item.isSubmenu && item.SubmenuItems.Count > 0)
            {
                CreateSubmenu(menuItem, item);
            }
        }

        /// <summary>
        /// 创建子菜单
        /// </summary>
        private void CreateSubmenu(Button parentItem, MenuBarConfig.MenuItem item)
        {
            // 添加子菜单指示器
            var indicator = new VisualElement();
            indicator.AddToClassList("submenu-indicator");
            parentItem.Add(indicator);

            // 创建子菜单容器
            var submenu = new VisualElement();
            submenu.AddToClassList("submenu");

            foreach (var subItem in item.SubmenuItems)
            {
                if (!subItem.visible) continue;

                if (subItem.isSeparator)
                {
                    var separator = new VisualElement();
                    separator.AddToClassList("dropdown-separator");
                    submenu.Add(separator);
                }
                else
                {
                    CreateMenuItem(submenu, subItem);
                }
            }

            parentItem.Add(submenu);

            // 子菜单显示/隐藏事件
            parentItem.RegisterCallback<MouseEnterEvent>(evt => submenu.AddToClassList("visible"));
            parentItem.RegisterCallback<MouseLeaveEvent>(evt => submenu.RemoveFromClassList("visible"));
        }

        #endregion
    }
}