using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;
using BlastingDesign.UI.DaisyUI.Core;

namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyUI输入框组件 - 初始化部分
    /// 包含模板加载、元素创建和组件设置相关逻辑
    /// </summary>
    public partial class DaisyInput
    {
        #region 初始化方法

        /// <summary>
        /// 从模板初始化组件
        /// </summary>
        private void InitializeFromTemplate()
        {
            if (DaisyUtilities.IsDebugMode)
            {
                Logging.LogInfo("DaisyInput", "开始从模板初始化组件");
                Logging.LogInfo("DaisyInput", $"子元素数量: {childCount}");
            }

            _label = this.Q<Label>("input-label");
            _inputContainer = this.Q<VisualElement>("input-container");
            _textField = this.Q<TextField>("input-field");
            _placeholderLabel = this.Q<Label>("placeholder-label");
            _helperText = this.Q<Label>("helper-text");

            if (DaisyUtilities.IsDebugMode)
            {
                Logging.LogInfo("DaisyInput", $"模板元素查找结果: label={_label != null}, container={_inputContainer != null}, textField={_textField != null}, placeholder={_placeholderLabel != null}, helper={_helperText != null}");
            }

            if (_textField == null)
            {
                if (DaisyUtilities.IsDebugMode)
                {
                    Logging.LogWarning("DaisyInput", "模板中未找到TextField，使用代码创建");
                }
                CreateInputStructure();
            }
        }

        /// <summary>
        /// 创建输入框结构
        /// </summary>
        private void CreateInputStructure()
        {
            // 创建标签
            _label = new Label();
            _label.AddToClassList("daisy-input-label");
            _label.style.display = DisplayStyle.None; // 默认隐藏
            Add(_label);

            // 创建输入容器
            _inputContainer = new VisualElement();
            _inputContainer.AddToClassList("daisy-input-container");
            Add(_inputContainer);

            // 创建输入框
            _textField = new TextField();
            _textField.AddToClassList("daisy-input-field");
            _inputContainer.Add(_textField);

            // 创建占位符标签
            _placeholderLabel = new Label();
            _placeholderLabel.AddToClassList("daisy-input-placeholder-label");
            _placeholderLabel.style.display = DisplayStyle.None; // 默认隐藏
            _inputContainer.Add(_placeholderLabel);

            // 创建帮助文本
            _helperText = new Label();
            _helperText.AddToClassList("daisy-input-helper");
            _helperText.style.display = DisplayStyle.None; // 默认隐藏
            Add(_helperText);

            // 设置占位符
            UpdatePlaceholder();
        }

        /// <summary>
        /// 设置组件
        /// </summary>
        private void SetupComponent()
        {
            // 应用基础样式
            AddToClassList("daisy-input");

            // 设置默认尺寸
            WithSize(_currentSize);

            // 设置占位符
            UpdatePlaceholder();

            // 设置输入类型
            UpdateInputType();

            // 设置事件处理
            SetupEventHandlers();

            // 初始化状态
            UpdateState();
        }

        /// <summary>
        /// 设置事件处理
        /// </summary>
        protected override void SetupEventHandlers()
        {
            if (_textField != null)
            {
                _textField.RegisterValueChangedCallback(OnValueChanged);

                // 为TextField添加基于焦点的键盘事件阻断功能
                _textField.AddFocusBasedKeyboardEventBlocker(enableDebugLogging: false);
            }
        }

        #endregion

        #region 更新方法

        /// <summary>
        /// 更新占位符
        /// </summary>
        private void UpdatePlaceholder()
        {
            if (_placeholderLabel != null && _textField != null)
            {
                // 设置占位符文本
                _placeholderLabel.text = _placeholder;

                // 注册事件
                _textField.RegisterCallback<FocusInEvent>(OnFocusIn);
                _textField.RegisterCallback<FocusOutEvent>(OnFocusOut);

                // 添加占位符点击事件，让输入框获取焦点
                _placeholderLabel.RegisterCallback<ClickEvent>(OnPlaceholderClick);

                // 根据当前输入框状态显示或隐藏占位符
                if (string.IsNullOrEmpty(_textField.value))
                {
                    ShowPlaceholder();
                }
                else
                {
                    HidePlaceholder();
                }
            }
        }

        /// <summary>
        /// 更新输入类型
        /// </summary>
        private void UpdateInputType()
        {
            if (_textField != null)
            {
                // 移除之前的类型类名
                _textField.RemoveFromClassList("daisy-input-text");
                _textField.RemoveFromClassList("daisy-input-password");
                _textField.RemoveFromClassList("daisy-input-email");
                _textField.RemoveFromClassList("daisy-input-number");

                // 添加新的类型类名
                _textField.AddToClassList($"daisy-input-{_inputType}");

                // 设置密码字段
                if (_inputType == "password")
                {
                    _textField.isPasswordField = true;
                }
                else
                {
                    _textField.isPasswordField = false;
                }
            }
        }

        /// <summary>
        /// 更新组件状态
        /// </summary>
        private void UpdateState()
        {
            // 清除所有状态类
            RemoveFromClassList("daisy-input-normal");
            RemoveFromClassList("daisy-input-error");
            RemoveFromClassList("daisy-input-success");
            RemoveFromClassList("daisy-input-warning");
            RemoveFromClassList("daisy-input-info");

            // 应用当前状态
            AddToClassList($"daisy-input-{_currentState}");
        }

        /// <summary>
        /// 显示占位符
        /// </summary>
        private void ShowPlaceholder()
        {
            if (_placeholderLabel != null && !string.IsNullOrEmpty(_placeholder))
            {
                _placeholderLabel.style.display = DisplayStyle.Flex;
            }
        }

        /// <summary>
        /// 隐藏占位符
        /// </summary>
        private void HidePlaceholder()
        {
            if (_placeholderLabel != null)
            {
                _placeholderLabel.style.display = DisplayStyle.None;
            }
        }

        #endregion
    }
}
