using System;
using UnityEngine;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 设置数据类 - 存储所有应用程序设置
    /// 基于配置模块技术规格定义的数据结构
    /// </summary>
    [Serializable]
    public class SettingsData
    {
        #region 常规设置

        [Header("常规设置")]
        [SerializeField] private bool enableSound = true;
        [SerializeField] private bool enableNotifications = true;
        [SerializeField] private bool autoSave = true;
        [SerializeField] private string language = "中文";
        [SerializeField] private int fontSize = 14;

        public bool EnableSound { get => enableSound; set => enableSound = value; }
        public bool EnableNotifications { get => enableNotifications; set => enableNotifications = value; }
        public bool AutoSave { get => autoSave; set => autoSave = value; }
        public string Language { get => language; set => language = value; }
        public int FontSize { get => fontSize; set => fontSize = value; }

        #endregion

        #region 数据库设置

        [Header("数据库设置")]
        [SerializeField] private string sqlitePath = "data/database.db";
        [SerializeField] private bool sqliteSqlPrint = false;
        [SerializeField] private bool sqlServerEnabled = false;
        [SerializeField] private string sqlServerHost = "localhost";
        [SerializeField] private string sqlServerPort = "1433";
        [SerializeField] private string sqlServerDatabase = "BlastingDesign";
        [SerializeField] private string sqlServerUsername = "";
        [SerializeField] private string sqlServerPassword = "";

        public string SqlitePath { get => sqlitePath; set => sqlitePath = value; }
        public bool SqliteSqlPrint { get => sqliteSqlPrint; set => sqliteSqlPrint = value; }
        public bool SqlServerEnabled { get => sqlServerEnabled; set => sqlServerEnabled = value; }
        public string SqlServerHost { get => sqlServerHost; set => sqlServerHost = value; }
        public string SqlServerPort { get => sqlServerPort; set => sqlServerPort = value; }
        public string SqlServerDatabase { get => sqlServerDatabase; set => sqlServerDatabase = value; }
        public string SqlServerUsername { get => sqlServerUsername; set => sqlServerUsername = value; }
        public string SqlServerPassword { get => sqlServerPassword; set => sqlServerPassword = value; }

        #endregion

        #region API设置

        [Header("API设置")]
        [SerializeField] private string apiBaseUrl = "https://api.example.com";
        [SerializeField] private string apiKey = "";
        [SerializeField] private int apiTimeout = 30;
        [SerializeField] private int apiRetryCount = 3;
        [SerializeField] private bool apiVerifySsl = true;

        public string ApiBaseUrl { get => apiBaseUrl; set => apiBaseUrl = value; }
        public string ApiKey { get => apiKey; set => apiKey = value; }
        public int ApiTimeout { get => apiTimeout; set => apiTimeout = value; }
        public int ApiRetryCount { get => apiRetryCount; set => apiRetryCount = value; }
        public bool ApiVerifySsl { get => apiVerifySsl; set => apiVerifySsl = value; }

        #endregion

        #region 系统设置

        [Header("系统设置")]
        [SerializeField] private int maxConnections = 100;
        [SerializeField] private int memoryCacheSize = 256;
        [SerializeField] private string workDirectory = "work";
        [SerializeField] private string tempDirectory = "temp";

        public int MaxConnections { get => maxConnections; set => maxConnections = value; }
        public int MemoryCacheSize { get => memoryCacheSize; set => memoryCacheSize = value; }
        public string WorkDirectory { get => workDirectory; set => workDirectory = value; }
        public string TempDirectory { get => tempDirectory; set => tempDirectory = value; }

        #endregion

        #region 主题设置

        [Header("主题设置")]
        [SerializeField] private string theme = "深色";
        [SerializeField] private bool followSystemTheme = false;

        public string Theme { get => theme; set => theme = value; }
        public bool FollowSystemTheme { get => followSystemTheme; set => followSystemTheme = value; }

        #endregion

        #region 日志设置

        [Header("日志设置")]
        [SerializeField] private string logLevel = "INFO";
        [SerializeField] private string logFilePath = "logs/application.log";
        [SerializeField] private bool enableFileLogging = true;
        [SerializeField] private bool enableConsoleLogging = true;

        public string LogLevel { get => logLevel; set => logLevel = value; }
        public string LogFilePath { get => logFilePath; set => logFilePath = value; }
        public bool EnableFileLogging { get => enableFileLogging; set => enableFileLogging = value; }
        public bool EnableConsoleLogging { get => enableConsoleLogging; set => enableConsoleLogging = value; }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SettingsData()
        {
            SetDefaults();
        }

        /// <summary>
        /// 复制构造函数
        /// </summary>
        public SettingsData(SettingsData other)
        {
            if (other != null)
            {
                CopyFrom(other);
            }
            else
            {
                SetDefaults();
            }
        }

        #endregion

        #region 简化版本的别名属性 (用于简化的SettingsWindow)

        /// <summary>
        /// 数据库路径 (简化版别名)
        /// </summary>
        public string DatabasePath { get => SqlitePath; set => SqlitePath = value; }

        /// <summary>
        /// 启用SQL日志 (简化版别名)
        /// </summary>
        public bool EnableSqlLogging { get => SqliteSqlPrint; set => SqliteSqlPrint = value; }

        /// <summary>
        /// 缓存大小 (简化版别名)
        /// </summary>
        public int CacheSize { get => MemoryCacheSize; set => MemoryCacheSize = value; }

        #endregion

        #region 公共方法

        /// <summary>
        /// 加载默认值 (简化版本方法)
        /// </summary>
        public void LoadDefaults()
        {
            SetDefaults();
        }

        /// <summary>
        /// 保存设置 (简化版本方法)
        /// </summary>
        public void Save()
        {
            // 在实际应用中，这里会保存到文件或注册表
            // 目前只是一个占位符方法
            try
            {
                // 验证设置
                if (Validate(out string errorMessage))
                {
                    // 这里可以添加实际的保存逻辑
                    // 例如：SaveToFile() 或 SaveToRegistry()
                    Debug.Log("设置已保存");
                }
                else
                {
                    Debug.LogError($"设置保存失败: {errorMessage}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"保存设置时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置默认值
        /// </summary>
        public void SetDefaults()
        {
            // 常规设置默认值
            EnableSound = true;
            EnableNotifications = true;
            AutoSave = true;
            Language = "中文";
            FontSize = 14;

            // 数据库设置默认值
            SqlitePath = "data/database.db";
            SqliteSqlPrint = false;
            SqlServerEnabled = false;
            SqlServerHost = "localhost";
            SqlServerPort = "1433";
            SqlServerDatabase = "BlastingDesign";
            SqlServerUsername = "";
            SqlServerPassword = "";

            // API设置默认值
            ApiBaseUrl = "https://api.example.com";
            ApiKey = "";
            ApiTimeout = 30;
            ApiRetryCount = 3;
            ApiVerifySsl = true;

            // 系统设置默认值
            MaxConnections = 100;
            MemoryCacheSize = 256;
            WorkDirectory = "work";
            TempDirectory = "temp";

            // 主题设置默认值
            Theme = "深色";
            FollowSystemTheme = false;

            // 日志设置默认值
            LogLevel = "INFO";
            LogFilePath = "logs/application.log";
            EnableFileLogging = true;
            EnableConsoleLogging = true;
        }

        /// <summary>
        /// 从另一个设置数据对象复制值
        /// </summary>
        public void CopyFrom(SettingsData other)
        {
            if (other == null) return;

            // 复制常规设置
            EnableSound = other.EnableSound;
            EnableNotifications = other.EnableNotifications;
            AutoSave = other.AutoSave;
            Language = other.Language;
            FontSize = other.FontSize;

            // 复制数据库设置
            SqlitePath = other.SqlitePath;
            SqliteSqlPrint = other.SqliteSqlPrint;
            SqlServerEnabled = other.SqlServerEnabled;
            SqlServerHost = other.SqlServerHost;
            SqlServerPort = other.SqlServerPort;
            SqlServerDatabase = other.SqlServerDatabase;
            SqlServerUsername = other.SqlServerUsername;
            SqlServerPassword = other.SqlServerPassword;

            // 复制API设置
            ApiBaseUrl = other.ApiBaseUrl;
            ApiKey = other.ApiKey;
            ApiTimeout = other.ApiTimeout;
            ApiRetryCount = other.ApiRetryCount;
            ApiVerifySsl = other.ApiVerifySsl;

            // 复制系统设置
            MaxConnections = other.MaxConnections;
            MemoryCacheSize = other.MemoryCacheSize;
            WorkDirectory = other.WorkDirectory;
            TempDirectory = other.TempDirectory;

            // 复制主题设置
            Theme = other.Theme;
            FollowSystemTheme = other.FollowSystemTheme;

            // 复制日志设置
            LogLevel = other.LogLevel;
            LogFilePath = other.LogFilePath;
            EnableFileLogging = other.EnableFileLogging;
            EnableConsoleLogging = other.EnableConsoleLogging;
        }

        /// <summary>
        /// 创建深度拷贝
        /// </summary>
        public SettingsData Clone()
        {
            return new SettingsData(this);
        }

        /// <summary>
        /// 验证设置数据的有效性
        /// </summary>
        public bool Validate(out string errorMessage)
        {
            errorMessage = "";

            // 验证字体大小
            if (FontSize < 8 || FontSize > 32)
            {
                errorMessage = "字体大小必须在8-32之间";
                return false;
            }

            // 验证数据库端口
            if (!int.TryParse(SqlServerPort, out int port) || port < 1 || port > 65535)
            {
                errorMessage = "数据库端口必须是1-65535之间的有效数字";
                return false;
            }

            // 验证API超时时间
            if (ApiTimeout < 1 || ApiTimeout > 300)
            {
                errorMessage = "API超时时间必须在1-300秒之间";
                return false;
            }

            // 验证API重试次数
            if (ApiRetryCount < 0 || ApiRetryCount > 10)
            {
                errorMessage = "API重试次数必须在0-10之间";
                return false;
            }

            // 验证最大连接数
            if (MaxConnections < 1 || MaxConnections > 1000)
            {
                errorMessage = "最大连接数必须在1-1000之间";
                return false;
            }

            // 验证内存缓存大小
            if (MemoryCacheSize < 64 || MemoryCacheSize > 4096)
            {
                errorMessage = "内存缓存大小必须在64-4096MB之间";
                return false;
            }

            // 验证必填字段
            if (string.IsNullOrWhiteSpace(Language))
            {
                errorMessage = "语言设置不能为空";
                return false;
            }

            if (string.IsNullOrWhiteSpace(SqlitePath))
            {
                errorMessage = "SQLite数据库路径不能为空";
                return false;
            }

            if (SqlServerEnabled)
            {
                if (string.IsNullOrWhiteSpace(SqlServerHost))
                {
                    errorMessage = "启用SQL Server时，服务器地址不能为空";
                    return false;
                }

                if (string.IsNullOrWhiteSpace(SqlServerDatabase))
                {
                    errorMessage = "启用SQL Server时，数据库名不能为空";
                    return false;
                }
            }

            if (string.IsNullOrWhiteSpace(ApiBaseUrl))
            {
                errorMessage = "API基础URL不能为空";
                return false;
            }

            if (string.IsNullOrWhiteSpace(WorkDirectory))
            {
                errorMessage = "工作目录不能为空";
                return false;
            }

            if (string.IsNullOrWhiteSpace(TempDirectory))
            {
                errorMessage = "临时目录不能为空";
                return false;
            }

            return true;
        }

        /// <summary>
        /// 比较两个设置数据对象是否相等
        /// </summary>
        public bool Equals(SettingsData other)
        {
            if (other == null) return false;

            return EnableSound == other.EnableSound &&
                   EnableNotifications == other.EnableNotifications &&
                   AutoSave == other.AutoSave &&
                   Language == other.Language &&
                   FontSize == other.FontSize &&
                   SqlitePath == other.SqlitePath &&
                   SqliteSqlPrint == other.SqliteSqlPrint &&
                   SqlServerEnabled == other.SqlServerEnabled &&
                   SqlServerHost == other.SqlServerHost &&
                   SqlServerPort == other.SqlServerPort &&
                   SqlServerDatabase == other.SqlServerDatabase &&
                   SqlServerUsername == other.SqlServerUsername &&
                   SqlServerPassword == other.SqlServerPassword &&
                   ApiBaseUrl == other.ApiBaseUrl &&
                   ApiKey == other.ApiKey &&
                   ApiTimeout == other.ApiTimeout &&
                   ApiRetryCount == other.ApiRetryCount &&
                   ApiVerifySsl == other.ApiVerifySsl &&
                   MaxConnections == other.MaxConnections &&
                   MemoryCacheSize == other.MemoryCacheSize &&
                   WorkDirectory == other.WorkDirectory &&
                   TempDirectory == other.TempDirectory &&
                   Theme == other.Theme &&
                   FollowSystemTheme == other.FollowSystemTheme &&
                   LogLevel == other.LogLevel &&
                   LogFilePath == other.LogFilePath &&
                   EnableFileLogging == other.EnableFileLogging &&
                   EnableConsoleLogging == other.EnableConsoleLogging;
        }

        /// <summary>
        /// 转换为字符串（用于调试）
        /// </summary>
        public override string ToString()
        {
            return $"SettingsData: Language={Language}, Theme={Theme}, SqlServerEnabled={SqlServerEnabled}, ApiTimeout={ApiTimeout}";
        }

        #endregion
    }
}