{"configurations": [{"cpuArchitecture": "x86_64", "editorCpuArchitecture": "", "editorOperatingSystem": "Windows", "runtime": "win10-x64", "supportedPlatformTargets": ["StandaloneWindows64"]}, {"cpuArchitecture": "AnyCPU", "editorCpuArchitecture": "", "editorOperatingSystem": "Windows", "runtime": "win10-x86", "supportedPlatformTargets": ["StandaloneWindows"]}, {"cpuArchitecture": "x86_64", "editorCpuArchitecture": "", "editorOperatingSystem": "Windows", "runtime": "win7-x64", "supportedPlatformTargets": ["StandaloneWindows64"]}, {"cpuArchitecture": "AnyCPU", "editorCpuArchitecture": "", "editorOperatingSystem": "Windows", "runtime": "win7-x86", "supportedPlatformTargets": ["StandaloneWindows"]}, {"cpuArchitecture": "x86_64", "editorCpuArchitecture": "", "editorOperatingSystem": "Windows", "runtime": "win-x64", "supportedPlatformTargets": ["StandaloneWindows64"]}, {"cpuArchitecture": "AnyCPU", "editorCpuArchitecture": "", "editorOperatingSystem": "Windows", "runtime": "win-x86", "supportedPlatformTargets": ["StandaloneWindows"]}, {"cpuArchitecture": "x86_64", "editorCpuArchitecture": "x86_64", "editorOperatingSystem": "Linux", "runtime": "linux-x64", "supportedPlatformTargets": ["StandaloneLinux64"]}, {"cpuArchitecture": "", "editorCpuArchitecture": "", "editorOperatingSystem": "", "runtime": "android", "supportedPlatformTargets": ["Android"]}, {"cpuArchitecture": "ARM64", "editorCpuArchitecture": "", "editorOperatingSystem": "", "runtime": "android-arm64", "supportedPlatformTargets": ["Android"]}, {"cpuArchitecture": "", "editorCpuArchitecture": "", "editorOperatingSystem": "", "runtime": "ios", "supportedPlatformTargets": ["iOS"]}, {"cpuArchitecture": "ARM64", "editorCpuArchitecture": "", "editorOperatingSystem": "", "runtime": "ios-arm64", "supportedPlatformTargets": ["iOS"]}, {"cpuArchitecture": "x86_64", "editorCpuArchitecture": "", "editorOperatingSystem": "OSX", "runtime": "osx-x64", "supportedPlatformTargets": ["StandaloneOSX"]}, {"cpuArchitecture": "ARM64", "editorCpuArchitecture": "", "editorOperatingSystem": "", "runtime": "osx-arm64", "supportedPlatformTargets": ["StandaloneOSX"]}]}