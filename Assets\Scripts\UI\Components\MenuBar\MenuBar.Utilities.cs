using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// MenuBar工具方法类 - 提供辅助功能和工具方法
    /// </summary>
    public partial class MenuBar : UIElementBase, IMenuComponent
    {
        #region 工具方法

        /// <summary>
        /// 获取根元素
        /// </summary>
        private VisualElement GetRootElement()
        {
            // 通过UIDocument获取根元素
            var uiDocument = UnityEngine.Object.FindFirstObjectByType<UIDocument>();

            if (uiDocument != null && uiDocument.rootVisualElement != null)
            {
                Logging.LogInfo("MenuBar", "通过UIDocument获取根元素");
                return uiDocument.rootVisualElement;
            }

            // 向上遍历找到根元素
            VisualElement current = this;
            while (current.parent != null)
            {
                current = current.parent;
            }

            Logging.LogInfo("MenuBar", "通过向上遍历获取根元素");
            return current;
        }

        #endregion
    }
}