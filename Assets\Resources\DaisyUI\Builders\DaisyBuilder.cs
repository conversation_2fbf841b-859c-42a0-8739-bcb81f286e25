using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.UI.DaisyUI.Components.Actions;
using BlastingDesign.UI.DaisyUI.Components.DataDisplay;
using BlastingDesign.UI.DaisyUI.Components.DataInput;
using BlastingDesign.UI.DaisyUI.Components.Navigation;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;
using BlastingDesign.UI.Extensions;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI.Builders
{
    /// <summary>
    /// DaisyUI组件构建器
    /// 提供流畅的API来创建和配置UI组件
    /// </summary>
    public static class DaisyBuilder
    {
        #region 基础组件构建器

        /// <summary>
        /// 创建容器元素
        /// </summary>
        /// <param name="children">子元素</param>
        /// <returns>容器元素</returns>
        public static VisualElement Container(params VisualElement[] children)
        {
            var container = new VisualElement();
            container.AddToClassList("daisy-container");

            if (children != null)
            {
                foreach (var child in children)
                {
                    if (child != null)
                    {
                        container.Add(child);
                    }
                }
            }

            return container;
        }

        /// <summary>
        /// 创建容器元素（带CSS类名）
        /// </summary>
        /// <param name="className">CSS类名</param>
        /// <param name="children">子元素</param>
        /// <returns>容器元素</returns>
        public static VisualElement Container(string className, params VisualElement[] children)
        {
            var container = Container(children);
            if (!string.IsNullOrEmpty(className))
            {
                container.AddToClassList(className);
            }
            return container;
        }

        /// <summary>
        /// 创建Flex容器
        /// </summary>
        /// <param name="direction">Flex方向</param>
        /// <param name="children">子元素</param>
        /// <returns>Flex容器</returns>
        public static VisualElement Flex(FlexDirection direction = FlexDirection.Row, params VisualElement[] children)
        {
            var container = Container(children);
            container.AddToClassList("daisy-flex");
            container.style.flexDirection = direction;
            return container;
        }

        /// <summary>
        /// 创建行容器
        /// </summary>
        /// <param name="children">子元素</param>
        /// <returns>行容器</returns>
        public static VisualElement Row(params VisualElement[] children)
        {
            return Flex(FlexDirection.Row, children).AddDaisyClass("flex-row");
        }

        /// <summary>
        /// 创建列容器
        /// </summary>
        /// <param name="children">子元素</param>
        /// <returns>列容器</returns>
        public static VisualElement Column(params VisualElement[] children)
        {
            return Flex(FlexDirection.Column, children).AddDaisyClass("flex-col");
        }

        /// <summary>
        /// 创建网格容器
        /// </summary>
        /// <param name="columns">列数</param>
        /// <param name="children">子元素</param>
        /// <returns>网格容器</returns>
        public static VisualElement Grid(int columns, params VisualElement[] children)
        {
            var grid = Container(children);
            grid.AddToClassList("daisy-grid");
            grid.AddToClassList($"daisy-grid-cols-{columns}");
            return grid;
        }

        /// <summary>
        /// 创建居中容器
        /// </summary>
        /// <param name="children">子元素</param>
        /// <returns>居中容器</returns>
        public static VisualElement Center(params VisualElement[] children)
        {
            var container = Container(children);
            container.AddToClassList("daisy-flex");
            container.AddToClassList("daisy-items-center");
            container.AddToClassList("daisy-justify-center");
            return container;
        }

        #endregion

        #region 基础组件快捷创建方法

        /// <summary>
        /// 创建文本标签
        /// </summary>
        /// <param name="text">文本内容</param>
        /// <param name="className">CSS类名</param>
        /// <returns>文本标签</returns>
        public static Label Text(string text, string className = "")
        {
            var label = new Label(text);
            label.AddToClassList("daisy-text");

            if (!string.IsNullOrEmpty(className))
            {
                label.AddToClassList(className);
            }

            return label;
        }

        /// <summary>
        /// 创建按钮组件
        /// </summary>
        /// <param name="text">按钮文本</param>
        /// <returns>按钮组件</returns>
        public static DaisyButton Button(string text)
        {
            return DaisyButton.Create(text);
        }

        /// <summary>
        /// 创建标题
        /// </summary>
        /// <param name="text">标题文本</param>
        /// <param name="level">标题级别 (1-6)</param>
        /// <returns>标题元素</returns>
        public static Label Heading(string text, int level = 1)
        {
            var heading = Text(text);
            heading.AddToClassList("daisy-heading");
            heading.AddToClassList($"daisy-h{Mathf.Clamp(level, 1, 6)}");
            return heading;
        }

        /// <summary>
        /// 创建分割线
        /// </summary>
        /// <param name="text">分割线文本（可选）</param>
        /// <returns>分割线元素</returns>
        public static VisualElement Divider(string text = "")
        {
            var divider = new VisualElement();
            divider.AddToClassList("daisy-divider");

            if (!string.IsNullOrEmpty(text))
            {
                var label = new Label(text);
                label.AddToClassList("daisy-divider-text");
                divider.Add(label);
            }

            return divider;
        }

        /// <summary>
        /// 创建间隔元素
        /// </summary>
        /// <param name="size">间隔大小</param>
        /// <returns>间隔元素</returns>
        public static VisualElement Spacer(int size = 4)
        {
            var spacer = new VisualElement();
            spacer.AddToClassList("daisy-spacer");
            spacer.AddToClassList($"daisy-h-{size}");
            return spacer;
        }

        /// <summary>
        /// 创建卡片组件
        /// </summary>
        /// <returns>卡片组件</returns>
        public static DaisyCard Card()
        {
            return DaisyCard.Create();
        }

        /// <summary>
        /// 创建带标题的卡片组件
        /// </summary>
        /// <param name="title">卡片标题</param>
        /// <returns>卡片组件</returns>
        public static DaisyCard Card(string title)
        {
            return DaisyCard.Create(title);
        }

        /// <summary>
        /// 创建完整的卡片组件
        /// </summary>
        /// <param name="title">卡片标题</param>
        /// <param name="content">卡片内容</param>
        /// <param name="actions">操作按钮</param>
        /// <returns>卡片组件</returns>
        public static DaisyCard Card(string title, string content, params VisualElement[] actions)
        {
            var card = DaisyCard.Create(title);
            if (!string.IsNullOrEmpty(content))
            {
                card.SetContent(content);
            }
            if (actions != null && actions.Length > 0)
            {
                card.AddActions(actions);
            }
            return card;
        }

        /// <summary>
        /// 创建输入框组件
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>输入框组件</returns>
        public static DaisyInput Input(string placeholder = "")
        {
            return DaisyInput.Create(placeholder);
        }

        /// <summary>
        /// 创建带标签的输入框组件
        /// </summary>
        /// <param name="label">标签文本</param>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>输入框组件</returns>
        public static DaisyInput Input(string label, string placeholder)
        {
            return DaisyInput.Create(label, placeholder);
        }

        /// <summary>
        /// 创建特定类型的输入框
        /// </summary>
        /// <param name="type">输入类型</param>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>输入框组件</returns>
        public static DaisyInput InputType(string type, string placeholder = "")
        {
            return DaisyInput.Create(placeholder).SetType(type);
        }

        /// <summary>
        /// 创建密码输入框
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>密码输入框组件</returns>
        public static DaisyInput PasswordInput(string placeholder = "请输入密码")
        {
            return DaisyInput.Create(placeholder).SetType("password");
        }

        /// <summary>
        /// 创建邮箱输入框
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>邮箱输入框组件</returns>
        public static DaisyInput EmailInput(string placeholder = "请输入邮箱")
        {
            return DaisyInput.Create(placeholder).SetType("email");
        }

        /// <summary>
        /// 创建数字输入框
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>数字输入框组件</returns>
        public static DaisyInput NumberInput(string placeholder = "请输入数字")
        {
            return DaisyInput.Create(placeholder).SetType("number");
        }

        /// <summary>
        /// 创建选择器组件
        /// </summary>
        /// <param name="options">选项列表</param>
        /// <returns>选择器组件</returns>
        public static DaisySelect Select(params string[] options)
        {
            return DaisySelect.Create(options);
        }

        /// <summary>
        /// 创建带标签的选择器组件
        /// </summary>
        /// <param name="label">标签文本</param>
        /// <param name="options">选项列表</param>
        /// <returns>选择器组件</returns>
        public static DaisySelect Select(string label, params string[] options)
        {
            return DaisySelect.Create(label, options);
        }

        /// <summary>
        /// 创建多选选择器
        /// </summary>
        /// <param name="options">选项列表</param>
        /// <returns>多选选择器组件</returns>
        public static DaisySelect MultiSelect(params string[] options)
        {
            return DaisySelect.Create(options).SetMultiple();
        }

        /// <summary>
        /// 创建带标签的多选选择器
        /// </summary>
        /// <param name="label">标签文本</param>
        /// <param name="options">选项列表</param>
        /// <returns>多选选择器组件</returns>
        public static DaisySelect MultiSelect(string label, params string[] options)
        {
            return DaisySelect.Create(label, options).SetMultiple();
        }

        /// <summary>
        /// 创建模态框组件
        /// </summary>
        /// <returns>模态框组件</returns>
        public static DaisyModal Modal()
        {
            return DaisyModal.Create();
        }

        /// <summary>
        /// 创建带标题的模态框组件
        /// </summary>
        /// <param name="title">模态框标题</param>
        /// <returns>模态框组件</returns>
        public static DaisyModal Modal(string title)
        {
            return DaisyModal.Create(title);
        }

        /// <summary>
        /// 创建确认对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="message">消息内容</param>
        /// <param name="onConfirm">确认回调</param>
        /// <param name="onCancel">取消回调</param>
        /// <returns>确认对话框组件</returns>
        public static DaisyModal ConfirmDialog(string title, string message, System.Action onConfirm = null, System.Action onCancel = null)
        {
            return DaisyModal.CreateConfirmDialog(title, message, onConfirm, onCancel);
        }

        /// <summary>
        /// 创建下拉菜单组件
        /// </summary>
        /// <returns>下拉菜单组件</returns>
        public static DaisyDropdown Dropdown()
        {
            return DaisyDropdown.Create();
        }

        /// <summary>
        /// 创建带触发器的下拉菜单组件
        /// </summary>
        /// <param name="triggerElement">触发器元素</param>
        /// <returns>下拉菜单组件</returns>
        public static DaisyDropdown Dropdown(VisualElement triggerElement)
        {
            return DaisyDropdown.Create(triggerElement);
        }

        /// <summary>
        /// 创建带按钮触发器的下拉菜单
        /// </summary>
        /// <param name="buttonText">按钮文本</param>
        /// <returns>下拉菜单组件</returns>
        public static DaisyDropdown DropdownButton(string buttonText)
        {
            return DaisyDropdown.CreateWithButton(buttonText);
        }

        /// <summary>
        /// 创建树组件
        /// </summary>
        /// <param name="id">树ID</param>
        /// <returns>树组件</returns>
        public static DaisyTree Tree(string id = "tree")
        {
            return DaisyTree.Create(id);
        }

        /// <summary>
        /// 创建基于数据的树组件
        /// </summary>
        /// <param name="rootData">根数据</param>
        /// <returns>树组件</returns>
        public static DaisyTree Tree(DaisyTreeData rootData)
        {
            var tree = DaisyTree.Create(rootData.Id);
            tree.SetData(new System.Collections.Generic.List<DaisyTreeData> { rootData });
            return tree;
        }

        /// <summary>
        /// 创建搜索树组件
        /// </summary>
        /// <param name="id">树ID</param>
        /// <param name="searchPlaceholder">搜索占位符</param>
        /// <returns>搜索树组件</returns>
        public static DaisyTree SearchTree(string id = "search-tree", string searchPlaceholder = "Search...")
        {
            return DaisyTree.Create(id)
                .SetAllowSearch(true)
                .SetSearchPlaceholder(searchPlaceholder);
        }

        /// <summary>
        /// 创建多选树组件
        /// </summary>
        /// <param name="id">树ID</param>
        /// <returns>多选树组件</returns>
        public static DaisyTree MultiSelectTree(string id = "multi-tree")
        {
            return DaisyTree.Create(id).SetMultiSelect(true);
        }

        /// <summary>
        /// 创建深色主题树组件
        /// </summary>
        /// <param name="id">树ID</param>
        /// <returns>深色主题树组件</returns>
        public static DaisyTree DarkTree(string id = "dark-tree")
        {
            var tree = DaisyTree.Create(id);
            tree.AddClass("dark");
            return tree;
        }

        /// <summary>
        /// 创建紧凑型树组件
        /// </summary>
        /// <param name="id">树ID</param>
        /// <returns>紧凑型树组件</returns>
        public static DaisyTree CompactTree(string id = "compact-tree")
        {
            var tree = DaisyTree.Create(id);
            tree.AddClass("compact");
            return tree;
        }

        /// <summary>
        /// 创建文件浏览器风格的树组件
        /// </summary>
        /// <param name="id">树ID</param>
        /// <returns>文件浏览器风格的树组件</returns>
        public static DaisyTree FileTree(string id = "file-tree")
        {
            var tree = DaisyTree.Create(id);
            tree.AddClass("show-lines");
            tree.SetShowIcons(true);
            tree.SetShowActions(true);
            tree.SetAllowSearch(true);
            tree.SetSearchPlaceholder("Search files...");
            tree.AddClass("compact");
            return tree;
        }

        /// <summary>
        /// 创建项目树组件（类似IDE项目结构）
        /// </summary>
        /// <param name="id">树ID</param>
        /// <returns>项目树组件</returns>
        public static DaisyTree ProjectTree(string id = "project-tree")
        {
            var tree = DaisyTree.Create(id);
            tree.SetShowIcons(true);
            tree.SetShowActions(true);
            tree.SetAllowSearch(true);
            tree.SetSearchPlaceholder("Search in project...");
            tree.AddClass("dark");
            return tree;
        }

        /// <summary>
        /// 创建设置树组件
        /// </summary>
        /// <param name="id">树ID</param>
        /// <returns>设置树组件</returns>
        public static DaisyTree SettingsTree(string id = "settings-tree")
        {
            return DaisyTree.Create(id)
                .SetShowIcons(true)
                .SetShowActions(false)
                .SetAllowSearch(true)
                .SetSearchPlaceholder("Search settings...");
        }

        #endregion

        #region 组件组合构建器

        /// <summary>
        /// 创建页面容器
        /// </summary>
        /// <param name="children">子元素</param>
        /// <returns>页面容器</returns>
        public static VisualElement Page(params VisualElement[] children)
        {
            var page = Container(children);
            page.AddToClassList("daisy-page");
            page.AddToClassList("daisy-min-h-screen");
            page.AddToClassList("daisy-bg-base-100");
            page.AddToClassList("daisy-text-base-content");
            return page;
        }

        /// <summary>
        /// 创建节组件
        /// </summary>
        /// <param name="title">节标题</param>
        /// <param name="children">子元素</param>
        /// <returns>节组件</returns>
        public static VisualElement Section(string title = "", params VisualElement[] children)
        {
            var section = Container();
            section.AddToClassList("daisy-section");
            section.AddToClassList("daisy-py-8");

            if (!string.IsNullOrEmpty(title))
            {
                var titleElement = Heading(title, 2);
                titleElement.AddToClassList("daisy-mb-6");
                section.Add(titleElement);
            }

            if (children != null)
            {
                foreach (var child in children)
                {
                    if (child != null)
                    {
                        section.Add(child);
                    }
                }
            }

            return section;
        }

        /// <summary>
        /// 创建面板组件
        /// </summary>
        /// <param name="title">面板标题</param>
        /// <param name="content">面板内容</param>
        /// <returns>面板组件</returns>
        public static VisualElement Panel(string title = "", VisualElement content = null)
        {
            var panel = Container();
            panel.AddToClassList("daisy-panel");
            panel.AddToClassList("daisy-bg-base-200");
            panel.AddToClassList("daisy-rounded-lg");
            panel.AddToClassList("daisy-p-6");

            if (!string.IsNullOrEmpty(title))
            {
                var titleElement = Heading(title, 3);
                titleElement.AddToClassList("daisy-mb-4");
                panel.Add(titleElement);
            }

            if (content != null)
            {
                panel.Add(content);
            }

            return panel;
        }

        /// <summary>
        /// 创建表单组件
        /// </summary>
        /// <param name="children">表单元素</param>
        /// <returns>表单组件</returns>
        public static VisualElement Form(params VisualElement[] children)
        {
            var form = Column(children);
            form.AddToClassList("daisy-form");
            form.AddToClassList("daisy-gap-4");
            return form;
        }

        /// <summary>
        /// 创建表单组
        /// </summary>
        /// <param name="label">组标签</param>
        /// <param name="input">输入元素</param>
        /// <param name="helper">帮助文本</param>
        /// <returns>表单组</returns>
        public static VisualElement FormGroup(string label = "", VisualElement input = null, string helper = "")
        {
            var group = Column();
            group.AddToClassList("daisy-form-group");

            if (!string.IsNullOrEmpty(label))
            {
                var labelElement = Text(label, "daisy-label");
                labelElement.AddToClassList("daisy-mb-2");
                group.Add(labelElement);
            }

            if (input != null)
            {
                group.Add(input);
            }

            if (!string.IsNullOrEmpty(helper))
            {
                var helperElement = Text(helper, "daisy-helper-text");
                helperElement.AddToClassList("daisy-text-sm");
                helperElement.AddToClassList("daisy-text-neutral");
                helperElement.AddToClassList("daisy-mt-1");
                group.Add(helperElement);
            }

            return group;
        }

        /// <summary>
        /// 创建按钮组
        /// </summary>
        /// <param name="buttons">按钮列表</param>
        /// <returns>按钮组</returns>
        public static VisualElement ButtonGroup(params VisualElement[] buttons)
        {
            var group = Row(buttons);
            group.AddToClassList("daisy-btn-group");
            group.AddToClassList("daisy-gap-2");
            return group;
        }

        #endregion

        #region 布局构建器

        /// <summary>
        /// 创建头部布局
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="actions">操作按钮</param>
        /// <returns>头部布局</returns>
        public static VisualElement Header(string title = "", params VisualElement[] actions)
        {
            var header = Row();
            header.AddToClassList("daisy-header");
            header.AddToClassList("daisy-justify-between");
            header.AddToClassList("daisy-items-center");
            header.AddToClassList("daisy-p-4");
            header.AddToClassList("daisy-bg-base-100");
            header.AddToClassList("daisy-border-b");
            header.AddToClassList("daisy-border-base-300");

            if (!string.IsNullOrEmpty(title))
            {
                var titleElement = Heading(title, 1);
                header.Add(titleElement);
            }

            if (actions != null && actions.Length > 0)
            {
                var actionGroup = ButtonGroup(actions);
                header.Add(actionGroup);
            }

            return header;
        }

        /// <summary>
        /// 创建底部布局
        /// </summary>
        /// <param name="children">子元素</param>
        /// <returns>底部布局</returns>
        public static VisualElement Footer(params VisualElement[] children)
        {
            var footer = Row(children);
            footer.AddToClassList("daisy-footer");
            footer.AddToClassList("daisy-justify-center");
            footer.AddToClassList("daisy-items-center");
            footer.AddToClassList("daisy-p-4");
            footer.AddToClassList("daisy-bg-base-200");
            footer.AddToClassList("daisy-border-t");
            footer.AddToClassList("daisy-border-base-300");
            return footer;
        }

        /// <summary>
        /// 创建侧边栏布局
        /// </summary>
        /// <param name="width">侧边栏宽度</param>
        /// <param name="children">子元素</param>
        /// <returns>侧边栏布局</returns>
        public static VisualElement Sidebar(int width = 300, params VisualElement[] children)
        {
            var sidebar = Column(children);
            sidebar.AddToClassList("daisy-sidebar");
            sidebar.AddToClassList("daisy-bg-base-200");
            sidebar.AddToClassList("daisy-border-r");
            sidebar.AddToClassList("daisy-border-base-300");
            sidebar.AddToClassList("daisy-p-4");
            sidebar.style.width = width;
            return sidebar;
        }

        /// <summary>
        /// 创建主内容区域
        /// </summary>
        /// <param name="children">子元素</param>
        /// <returns>主内容区域</returns>
        public static VisualElement Main(params VisualElement[] children)
        {
            var main = Column(children);
            main.AddToClassList("daisy-main");
            main.AddToClassList("daisy-flex-1");
            main.AddToClassList("daisy-p-4");
            return main;
        }

        #endregion

        #region 实用工具方法

        /// <summary>
        /// 应用主题到元素
        /// </summary>
        /// <param name="element">目标元素</param>
        /// <param name="theme">主题</param>
        /// <returns>元素</returns>
        public static T WithTheme<T>(this T element, DaisyTheme theme) where T : VisualElement
        {
            if (theme != null)
            {
                theme.Apply(element);
            }
            return element;
        }





        #endregion
    }
}