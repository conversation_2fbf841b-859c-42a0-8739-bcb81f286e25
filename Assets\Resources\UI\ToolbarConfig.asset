%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d1f786ea4277b4bb1bb841ba66fdf8cd, type: 3}
  m_Name: ToolbarConfig
  m_EditorClassIdentifier: 
  toolGroups:
  - name: selection
    displayName: "\u9009\u62E9\u5DE5\u5177"
    enabled: 1
    sortOrder: 0
    buttons:
    - name: select-tool
      displayName: "\u9009\u62E9"
      enabled: 1
      visible: 1
      toolType: 0
      callbackName: SelectTool
      parameters: []
      isToggle: 0
      defaultSelected: 1
      iconName: select-icon
      tooltip: "\u9009\u62E9\u5DE5\u5177"
      showLabel: 1
      isExclusive: 1
      exclusiveGroup: main-tools
      customCssClass: 
      sortOrder: 0
    showSeparator: 1
    customCssClass: 
  - name: transform
    displayName: "\u53D8\u6362\u5DE5\u5177"
    enabled: 1
    sortOrder: 1
    buttons:
    - name: move-tool
      displayName: "\u79FB\u52A8"
      enabled: 1
      visible: 1
      toolType: 1
      callbackName: MoveTool
      parameters: []
      isToggle: 0
      defaultSelected: 0
      iconName: move-icon
      tooltip: "\u79FB\u52A8\u5DE5\u5177"
      showLabel: 1
      isExclusive: 1
      exclusiveGroup: main-tools
      customCssClass: 
      sortOrder: 0
    - name: rotate-tool
      displayName: "\u65CB\u8F6C"
      enabled: 1
      visible: 1
      toolType: 1
      callbackName: RotateTool
      parameters: []
      isToggle: 0
      defaultSelected: 0
      iconName: rotate-icon
      tooltip: "\u65CB\u8F6C\u5DE5\u5177"
      showLabel: 1
      isExclusive: 1
      exclusiveGroup: main-tools
      customCssClass: 
      sortOrder: 0
    - name: scale-tool
      displayName: "\u7F29\u653E"
      enabled: 1
      visible: 1
      toolType: 1
      callbackName: ScaleTool
      parameters: []
      isToggle: 0
      defaultSelected: 0
      iconName: scale-icon
      tooltip: "\u7F29\u653E\u5DE5\u5177"
      showLabel: 1
      isExclusive: 1
      exclusiveGroup: main-tools
      customCssClass: 
      sortOrder: 0
    showSeparator: 1
    customCssClass: 
  - name: navigation
    displayName: "\u5BFC\u822A\u5DE5\u5177"
    enabled: 1
    sortOrder: 2
    buttons:
    - name: pan-tool
      displayName: "\u5E73\u79FB"
      enabled: 1
      visible: 1
      toolType: 4
      callbackName: PanTool
      parameters: []
      isToggle: 0
      defaultSelected: 0
      iconName: pan-icon
      tooltip: "\u5E73\u79FB\u89C6\u56FE"
      showLabel: 1
      isExclusive: 1
      exclusiveGroup: main-tools
      customCssClass: 
      sortOrder: 0
    - name: zoom-tool
      displayName: "\u7F29\u653E"
      enabled: 1
      visible: 1
      toolType: 4
      callbackName: ZoomTool
      parameters: []
      isToggle: 0
      defaultSelected: 0
      iconName: zoom-icon
      tooltip: "\u7F29\u653E\u89C6\u56FE"
      showLabel: 1
      isExclusive: 1
      exclusiveGroup: main-tools
      customCssClass: 
      sortOrder: 0
    showSeparator: 1
    customCssClass: 
  - name: playback
    displayName: "\u64AD\u653E\u63A7\u5236"
    enabled: 1
    sortOrder: 3
    buttons:
    - name: play-button
      displayName: "\u64AD\u653E"
      enabled: 1
      visible: 1
      toolType: 5
      callbackName: TogglePlay
      parameters: []
      isToggle: 1
      defaultSelected: 0
      iconName: play-icon
      tooltip: "\u64AD\u653E/\u6682\u505C"
      showLabel: 1
      isExclusive: 0
      exclusiveGroup: 
      customCssClass: 
      sortOrder: 0
    showSeparator: 1
    customCssClass: 
  showLabels: 1
  showTooltips: 1
  buttonSize: 32
  allowMultiSelection: 0
