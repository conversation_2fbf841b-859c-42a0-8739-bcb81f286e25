﻿<?xml version="1.0" encoding="utf-8"?>
<!--Automatically generated-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Condition=" '$(RuntimeIdentifier)' == '' AND '$(OS)' == 'Windows_NT' ">
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\win-x86\native\e_sqlite3.dll">
      <Link>runtimes\win-x86\native\e_sqlite3.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\win-x64\native\e_sqlite3.dll">
      <Link>runtimes\win-x64\native\e_sqlite3.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\win-arm\native\e_sqlite3.dll">
      <Link>runtimes\win-arm\native\e_sqlite3.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(RuntimeIdentifier)' == '' AND '$(OS)' == 'Unix' AND Exists('/Library/Frameworks') ">
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\osx-x64\native\libe_sqlite3.dylib">
      <Link>runtimes\osx-x64\native\libe_sqlite3.dylib</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
  </ItemGroup>
  <ItemGroup Condition=" '$(RuntimeIdentifier)' == '' AND '$(OS)' == 'Unix' AND !Exists('/Library/Frameworks') ">
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-x86\native\libe_sqlite3.so">
      <Link>runtimes\linux-x86\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-x64\native\libe_sqlite3.so">
      <Link>runtimes\linux-x64\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-arm\native\libe_sqlite3.so">
      <Link>runtimes\linux-arm\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-armel\native\libe_sqlite3.so">
      <Link>runtimes\linux-armel\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-arm64\native\libe_sqlite3.so">
      <Link>runtimes\linux-arm64\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-x64\native\libe_sqlite3.so">
      <Link>runtimes\linux-x64\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-mips64\native\libe_sqlite3.so">
      <Link>runtimes\linux-mips64\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-s390x\native\libe_sqlite3.so">
      <Link>runtimes\linux-s390x\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)..\..\runtimes\linux-riscv64\native\libe_sqlite3.so">
      <Link>runtimes\linux-riscv64\native\libe_sqlite3.so</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>false</Pack>
    </Content>
  </ItemGroup>
</Project>