/* ============================================
   DaisyUI Component Library Window Styles
   Unity USS Compatible Version
   ============================================
   
   Changes made for Unity USS compatibility:
   - Replaced shorthand border properties with border-width/border-color
   - Converted transform properties to scale/translate
   - Removed unsupported properties: cursor, transition, box-shadow
   - Converted shorthand margin properties to explicit top/bottom/left/right
   - Replaced border-radius: 50% with explicit pixel values
   - Removed font-family (not supported)
   - Removed @media queries and @keyframes (not supported)
   - Converted overflow-x to overflow
   - Removed word-wrap (not supported)
   ============================================ */

/* Main Container */
.component-library-content {
    flex-direction: column;
    overflow: hidden;
}

/* Header Styles */
.theme-toggle-btn {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    background-color: var(--neutral);
    border-width: 1px;
    border-color: var(--neutral-focus);
    color: var(--neutral-content);
    font-size: 16px;
}

.theme-toggle-btn:hover {
    background-color: var(--neutral-focus);
    scale: 1.1 1.1;
}

.theme-toggle-btn:active {
    scale: 0.95 0.95;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    min-width: 280px;
    max-width: 320px;
    min-height: 400px;
}

/* Category Group Styles */
.category-group {
    margin-bottom: 16px;
}

.category-header {
    font-size: 13px;
    color: var(--base-content);
    opacity: 0.8;
    margin-bottom: 8px;
    padding: 4px 8px;
    border-bottom-width: 1px;
    border-bottom-color: var(--base-300);
}

.category-item {
    width: 100%;
    height: 32px;
    padding: 6px 12px;
    margin-top: 2px;
    margin-bottom: 2px;
    background-color: transparent;
    border-width: 0;
    border-radius: 6px;
    color: var(--base-content);
    font-size: 13px;
    -unity-text-align: middle-left;
    opacity: 0.8;
}

.category-item:hover {
    background-color: var(--base-300);
    opacity: 1;
    /* translate: 2px 0; */
}

.category-item:active {
    background-color: var(--primary);
    color: var(--primary-content);
}

.category-item.selected {
    background-color: var(--primary);
    color: var(--primary-content);
    opacity: 1;
}

/* Content Area Styles */
.content-area {
    min-height: 400px;
    background-color: var(--base-100);
}

/* Component Header */
.component-header {
    padding: 16px 20px;
    background-color: var(--base-100);
}

.header-content {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.component-title {
    font-size: 32px;
    color: var(--base-content);
    margin-bottom: 8px;
    -unity-text-align: middle-left;
}

.component-subtitle {
    font-size: 16px;
    color: var(--base-content);
    opacity: 0.7;
    -unity-text-align: middle-left;
}

/* Component Body */
.component-body {
    padding: 0;
    background-color: var(--base-100);
}

/* Welcome Section */
.welcome-section {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 40px;
}

.welcome-header {
    margin-bottom: 64px;
    align-items: center;
    justify-content: center;
}

.welcome-icon {
    font-size: 64px;
    margin-bottom: 16px;
    -unity-text-align: middle-center;
}

.welcome-title {
    font-size: 48px;
    color: var(--base-content);
    margin-bottom: 16px;
    -unity-text-align: middle-center;
}

.welcome-subtitle {
    font-size: 20px;
    color: var(--base-content);
    opacity: 0.7;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    -unity-text-align: middle-center;
}

/* Section Styles */
.section-title {
    font-size: 28px;
    color: var(--base-content);
    margin-bottom: 32px;
    -unity-text-align: middle-left;
}

/* Examples Section */
.examples-section {
    margin-bottom: 80px;
}

.examples-grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}

.example-card {
    background-color: var(--base-200);
    border-radius: 16px;
    border-width: 1px;
    border-color: var(--base-300);
    padding: 24px;
    flex: 1;
    min-width: 280px;
    max-width: 360px;
    margin-right: 24px;
    margin-bottom: 24px;
}

.example-card:hover {
    border-color: var(--primary);
    scale: 1.02 1.02;
}

.example-header {
    margin-bottom: 20px;
}

.example-title {
    font-size: 18px;
    color: var(--base-content);
    margin-bottom: 4px;
    -unity-text-align: middle-left;
}

.example-description {
    font-size: 14px;
    color: var(--base-content);
    opacity: 0.6;
    -unity-text-align: middle-left;
}

.example-preview {
    background-color: var(--base-100);
    border-radius: 12px;
    padding: 20px;
    border-width: 1px;
    border-color: var(--base-300);
    align-items: center;
    justify-content: center;
}

/* Features Section */
.features-section {
    margin-bottom: 40px;
}

.features-grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}

.feature-card {
    background-color: var(--base-200);
    border-radius: 12px;
    padding: 24px;
    flex: 1;
    min-width: 240px;
    max-width: 280px;
    align-items: center;
    justify-content: flex-start;
    margin-right: 24px;
    margin-bottom: 24px;
}

.feature-card:hover {
    background-color: var(--base-300);
}

.feature-icon {
    font-size: 32px;
    margin-bottom: 12px;
    -unity-text-align: middle-center;
}

.feature-title {
    font-size: 16px;
    color: var(--base-content);
    margin-bottom: 8px;
    -unity-text-align: middle-center;
}

.feature-description {
    font-size: 14px;
    color: var(--base-content);
    opacity: 0.7;
    -unity-text-align: middle-center;
}

/* Component Preview Styles */
.component-preview {
    background-color: var(--base-100);
    border-width: 1px;
    border-color: var(--base-300);
    border-radius: 8px;
    padding: 24px;
    margin-top: 16px;
    margin-bottom: 16px;
}

.component-preview-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom-width: 1px;
    border-bottom-color: var(--base-300);
}

.component-preview-title {
    font-size: 16px;
    color: var(--base-content);
}

.component-preview-section-title {
    font-size: 20px;
    color: var(--base-content);
    margin-bottom: 16px;
    margin-top: 24px;
    -unity-font-style: bold;
    -unity-text-align: middle-left;
}

.component-preview-body {
    padding: 20px;
    background-color: var(--base-200);
    border-radius: 6px;
    border-width: 1px;
    border-color: var(--base-300);
    margin-top: 16px;
    margin-bottom: 16px;
}

/* Code Block Styles */
.code-block {
    background-color: var(--neutral);
    border-width: 1px;
    border-color: var(--neutral-focus);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
    margin-bottom: 16px;
    /* font-family not supported in Unity USS */
    font-size: 12px;
    color: var(--neutral-content);
    overflow: hidden;
    white-space: normal;
}

.code-block-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom-width: 1px;
    border-bottom-color: var(--neutral-focus);
}

.code-block-title {
    font-size: 11px;
    color: var(--neutral-content);
    opacity: 0.8;
}

.copy-button {
    background-color: var(--neutral-focus);
    border-width: 1px;
    border-color: var(--neutral-focus);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10px;
    color: var(--neutral-content);
}

.copy-button:hover {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--primary-content);
}

/* Footer Styles */
.footer-container {
    padding-top: 16px;
    margin-top: 16px;
}

/* Button Styles */
.btn {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    border-width: 1px;
    border-color: transparent;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-ghost {
    background-color: transparent;
    color: var(--base-content);
    border-color: transparent;
}

.btn-ghost:hover {
    background-color: var(--base-300);
}

.btn-neutral {
    background-color: var(--neutral);
    color: var(--neutral-content);
    border-color: var(--neutral);
}

.btn-neutral:hover {
    background-color: var(--neutral-focus);
    border-color: var(--neutral-focus);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--primary-content);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-focus);
    border-color: var(--primary-focus);
}



/* Responsive Design - Unity USS doesn't support @media queries */
/* These styles are defined statically for the default layout */

/* Animation Classes */
.fade-in {
    opacity: 0;
}

.fade-in.show {
    opacity: 1;
}

.slide-up {
    translate: 0 20px;
    opacity: 0;
}

.slide-up.show {
    translate: 0 0;
    opacity: 1;
}

/* Loading States */
.loading-spinner {
    width: 32px;
    height: 32px;
    border-width: 3px;
    border-color: var(--base-300);
    border-radius: 16px;
}


/* Component-specific utility classes that override or extend TailwindUss */
/* Note: Most utility classes are now provided by TailwindUss and have been removed to avoid duplicates */

.border-base-300 {
    border-color: var(--base-300);
}

.bg-base-100 {
    background-color: var(--base-100);
}

.bg-base-200 {
    background-color: var(--base-200);
}

.bg-base-300 {
    background-color: var(--base-300);
}

.text-base-content {
    color: var(--base-content);
}