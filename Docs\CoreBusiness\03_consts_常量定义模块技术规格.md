# src/consts/ 常量定义模块技术规格文档

## 模块概述

常量定义模块是系统的基础数据字典组件，负责定义和管理系统中使用的所有枚举常量和静态数据。该模块通过Python枚举类型提供类型安全的常量定义，确保系统中数据类型的一致性和可维护性。

## 文件清单及功能

### 枚举定义文件

| 文件名 | 功能描述 | 业务领域 |
|--------|----------|----------|
| `border_line_type_enum.py` | 边界线类型枚举 | 爆破区域边界管理 |
| `coordinate_system_enum.py` | 坐标系统枚举 | 地理信息系统 |
| `goods_type_enum.py` | 货物类型枚举 | 矿物分类管理 |
| `hole_pattern_enum.py` | 钻孔布置模式枚举 | 钻孔设计 |
| `tools_active_enum.py` | 工具激活状态枚举 | 用户界面交互 |

## 核心数据结构

### 1. 边界线类型枚举 (BorderLineTypeEnum)

```python
class BorderLineTypeEnum(Enum):
    """边界线类型枚举类"""
    
    # 枚举值定义
    HEAD_ROW_HOLE_LINE = ("HEAD_ROW_HOLE_LINE", "头排孔线")
    BLAST_BOUNDARY_LINE = ("BLAST_BOUNDARY_LINE", "爆区边界线")
    
    # 属性结构
    key: str      # 内部标识符
    label: str    # 显示名称
```

**业务含义**:
- `HEAD_ROW_HOLE_LINE`: 头排孔线，用于标识钻孔布置的第一排孔位线
- `BLAST_BOUNDARY_LINE`: 爆区边界线，用于标识爆破区域的边界范围

### 2. 坐标系统枚举 (CoordinateSystemEnum)

```python
class CoordinateSystemEnum(Enum):
    """坐标系统枚举"""
    
    # 坐标系统定义
    WGS84 = "WGS84"           # WGS84地理坐标系
    BEIJING54 = "北京54"       # 北京54大地坐标系
    XIAN80 = "西安80"          # 西安80大地坐标系
    CGCS2000 = "CGCS2000"     # 2000国家大地坐标系
```

**扩展方法**:
```python
@classmethod
def get_description(cls, value) -> str
    """获取坐标系统的详细描述"""

@classmethod  
def get_all_systems(cls) -> List[Tuple]
    """获取所有坐标系统列表"""
```

### 3. 货物类型枚举 (GoodsTypeEnum)

```python
class GoodsTypeEnum(Enum):
    """货物类型枚举类"""
    
    # 矿物类型定义
    COAL = ("COAL", "煤矿")
    WASTE_ROCK = ("WASTE_ROCK", "废石")
    IRON_ORE = ("IRON_ORE", "铁矿石")
    COPPER_ORE = ("COPPER_ORE", "铜矿石")
    
    # 属性结构
    key: str      # 内部标识符
    label: str    # 显示名称
```

### 4. 钻孔布置模式枚举 (HolePatternEnum)

```python
class HolePatternEnum(Enum):
    """布孔模式枚举类"""
    
    # 布孔模式定义
    TRIANGULAR_HOLE = ("TRIANGULAR_HOLE", "三角孔(梅花孔)")
    SQUARE_HOLE = ("SQUARE_HOLE", "方形孔")
    
    # 属性结构
    key: str      # 内部标识符
    label: str    # 显示名称
```

**业务含义**:
- `TRIANGULAR_HOLE`: 三角形布孔模式，也称梅花孔，钻孔呈三角形排列
- `SQUARE_HOLE`: 方形布孔模式，钻孔呈方形网格排列

### 5. 工具激活状态枚举 (ToolsActiveEnum)

```python
class ToolsActiveEnum(Enum):
    """工具激活枚举"""
    
    # 基础工具
    UNSPECIFIED = "unspecified"                    # 未指定
    ROTATE_SCENE = "rotate_scene"                  # 旋转场景
    FULL_MAP_VIEW = "full_map_view"               # 全图视角
    POLY_SELECT = "poly_select"                   # 多边形选择
    RECTANGLE_SELECT = "rectangle_select"         # 矩形选择
    MEASURE_TOOL = "measure_tool"                 # 测量工具
    ZOOM_IN = "zoom_in"                          # 放大
    ZOOM_OUT = "zoom_out"                        # 缩小
    
    # 爆破区域工具
    ADD_BLAST_AREA = "add_blast_area"            # 添加爆区
    ADD_BORDER_LINE = "add_border_line"          # 添加边界线
    ADD_MEASURE_POINT = "add_measure_point"      # 添加测量点
    
    # 钻孔相关工具
    ADD_HOLE_INFO = "add_hole_info"              # 添加钻孔信息
    ADD_NEAR_FAR_POINT = "add_near_far_point"    # 添加近点远点
    CLEAR_NEAR_FAR_POINT = "clear_near_far_point" # 清除近点远点
    DRILL_HOLE_CLEAR = "drill_hole_clear"        # 清除钻孔
    MEASURE_TO_HOLE = "measure_to_hole"          # 测量点转换钻孔
    ENCRYPTION_HOLE = "encryption_hole"          # 加密孔
    ADD_HOLE = "add_hole"                        # 添加孔位
    ADD_ROW_HOLE = "add_row_hole"               # 添加行孔
    ADD_PIN_LINE = "add_pin_line"               # 沿线增加孔位
```

## 接口规范

### 通用枚举接口

所有枚举类都实现以下标准接口：

```python
# 字符串表示
def __str__(self) -> str
    """返回枚举的字符串表示"""

# 属性访问
@property
def key(self) -> str
    """获取枚举的内部标识符"""

@property  
def label(self) -> str
    """获取枚举的显示名称"""
```

### 坐标系统专用接口

```python
class CoordinateSystemEnum:
    @classmethod
    def get_description(cls, value) -> str
        """
        获取坐标系统的详细描述
        
        参数:
            value: 坐标系统枚举值
        返回:
            详细描述字符串
        """
    
    @classmethod
    def get_all_systems(cls) -> List[Tuple[Enum, str]]
        """
        获取所有坐标系统
        
        返回:
            (枚举值, 显示名称) 元组列表
        """
```

## 业务规则

### 边界线类型规则
- 头排孔线用于钻孔布置的起始参考线
- 爆区边界线定义爆破作业的安全范围
- 边界线类型影响钻孔生成算法的执行逻辑

### 坐标系统规则
- WGS84为国际标准坐标系，用于GPS定位
- 北京54和西安80为中国历史坐标系
- CGCS2000为中国现行国家标准坐标系
- 系统支持坐标系统间的转换

### 钻孔布置模式规则
- 三角孔模式适用于硬岩爆破，爆破效果更均匀
- 方形孔模式适用于软岩爆破，施工更简便
- 布孔模式影响孔距、排距的计算方法

### 工具激活状态规则
- 同一时间只能激活一个主要工具
- 工具状态变化通过事件系统通知
- 工具激活状态影响用户交互行为

## 关键约束条件

### 枚举值约束
- 枚举值一旦定义不可随意修改
- 新增枚举值必须保持向后兼容
- 枚举标识符使用大写下划线命名

### 显示名称约束
- 显示名称支持中文，用于用户界面
- 显示名称应简洁明了，便于用户理解
- 显示名称可以根据国际化需求调整

### 业务逻辑约束
- 枚举值与业务逻辑紧密耦合
- 删除枚举值前需评估对系统的影响
- 枚举值的语义必须保持稳定

## 扩展点设计

### 新枚举类型扩展
```python
# 扩展模板
class NewEnum(Enum):
    """新枚举类型"""
    
    VALUE1 = ("KEY1", "显示名称1")
    VALUE2 = ("KEY2", "显示名称2")
    
    def __init__(self, key: str, label: str = ""):
        self.key = key
        self.label = label
    
    def __str__(self):
        return self.key  # 或 self.label
```

### 枚举方法扩展
- 支持添加类方法进行枚举值查询
- 支持添加实例方法进行业务逻辑处理
- 支持添加属性进行额外信息存储

### 国际化扩展
- 支持多语言显示名称
- 支持动态语言切换
- 支持本地化配置

## 使用模式

### 基本使用模式
```python
from consts.border_line_type_enum import BorderLineTypeEnum

# 获取枚举值
line_type = BorderLineTypeEnum.HEAD_ROW_HOLE_LINE

# 获取显示名称
display_name = line_type.label

# 获取内部标识
key = line_type.key

# 字符串比较
if str(line_type) == "HEAD_ROW_HOLE_LINE":
    # 处理头排孔线逻辑
    pass
```

### 事件系统集成模式
```python
from consts.tools_active_enum import ToolsActiveEnum

# 工具激活事件
eventBridgeSystem.TriggerEvent(
    InteractiveEventType.TOOLS_ACTIVE,
    EventParam({
        "value": ToolsActiveEnum.RECTANGLE_SELECT.name,
        "enabled": True
    })
)
```

### 配置驱动模式
```python
# 在配置文件中使用枚举值
tool_config = {
    "name": "select",
    "action": ToolsActiveEnum.RECTANGLE_SELECT.value
}
```

## 质量保证

### 类型安全
- 使用Python枚举确保类型安全
- 编译时检查枚举值有效性
- IDE支持自动补全和类型提示

### 一致性保证
- 统一的枚举定义模式
- 标准化的属性命名
- 一致的字符串表示方法

### 可维护性
- 集中的常量定义管理
- 清晰的业务语义表达
- 便于重构和扩展的结构设计
