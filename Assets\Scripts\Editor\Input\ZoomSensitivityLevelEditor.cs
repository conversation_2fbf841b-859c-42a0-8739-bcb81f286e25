using UnityEngine;
using UnityEditor;

/// <summary>
/// 缩放敏感度级别编辑器，提供更直观的配置界面
/// </summary>
[CustomPropertyDrawer(typeof(ZoomSensitivityLevel))]
public class ZoomSensitivityLevelEditor : PropertyDrawer
{
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        EditorGUI.BeginProperty(position, label, property);

        // 计算各个字段的位置
        float labelWidth = 0f;
        float spacing = 5f;
        float availableWidth = position.width - labelWidth - spacing;
        float distanceLabelWidth = 5f;
        float multiplierLabelWidth = 5f;
        float fieldWidth = (availableWidth - distanceLabelWidth - multiplierLabelWidth - spacing) * 0.5f;

        Rect labelRect = new Rect(position.x, position.y, labelWidth, position.height);
        Rect distanceRect = new Rect(position.x + labelWidth, position.y, distanceLabelWidth + fieldWidth, position.height);
        Rect multiplierRect = new Rect(position.x + labelWidth + distanceLabelWidth + fieldWidth + spacing, position.y, multiplierLabelWidth + fieldWidth, position.height);

        // 绘制标签
        EditorGUI.LabelField(labelRect, label);

        // 获取属性
        SerializedProperty distanceThreshold = property.FindPropertyRelative("distanceThreshold");
        SerializedProperty sensitivityMultiplier = property.FindPropertyRelative("sensitivityMultiplier");

        // 绘制距离阈值字段
        EditorGUI.BeginChangeCheck();
        float newDistance = EditorGUI.FloatField(distanceRect, new GUIContent("距离", "距离阈值"), distanceThreshold.floatValue);
        if (EditorGUI.EndChangeCheck())
        {
            distanceThreshold.floatValue = Mathf.Max(0.1f, newDistance);
        }

        // 绘制敏感度倍数字段
        EditorGUI.BeginChangeCheck();
        float newMultiplier = EditorGUI.FloatField(multiplierRect, new GUIContent("倍数", "敏感度倍数"), sensitivityMultiplier.floatValue);
        if (EditorGUI.EndChangeCheck())
        {
            sensitivityMultiplier.floatValue = Mathf.Max(0.01f, newMultiplier);
        }

        EditorGUI.EndProperty();
    }

    public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
    {
        return EditorGUIUtility.singleLineHeight;
    }
}

/// <summary>
/// InputManager的自定义编辑器，为缩放敏感度级别提供更好的界面
/// </summary>
[CustomEditor(typeof(InputManager))]
public class InputManagerEditor : Editor
{
    private SerializedProperty zoomSensitivityLevels;
    private SerializedProperty useDistanceBasedZoomSensitivity;
    private bool showZoomSettings = false;

    void OnEnable()
    {
        zoomSensitivityLevels = serializedObject.FindProperty("zoomSensitivityLevels");
        useDistanceBasedZoomSensitivity = serializedObject.FindProperty("useDistanceBasedZoomSensitivity");
    }

    public override void OnInspectorGUI()
    {
        // 绘制默认Inspector
        DrawDefaultInspector();

        // 添加动态缩放设置的特殊界面
        EditorGUILayout.Space();
        showZoomSettings = EditorGUILayout.Foldout(showZoomSettings, "动态缩放配置工具", true);

        if (showZoomSettings)
        {
            EditorGUILayout.BeginVertical(GUI.skin.box);

            // 显示当前配置摘要
            if (useDistanceBasedZoomSensitivity.boolValue && zoomSensitivityLevels.arraySize > 0)
            {
                EditorGUILayout.LabelField("当前配置:", EditorStyles.boldLabel);
                for (int i = 0; i < zoomSensitivityLevels.arraySize; i++)
                {
                    var element = zoomSensitivityLevels.GetArrayElementAtIndex(i);
                    var distance = element.FindPropertyRelative("distanceThreshold").floatValue;
                    var multiplier = element.FindPropertyRelative("sensitivityMultiplier").floatValue;

                    string rangeText = i == 0 ? $"0 - {distance}" :
                                     i == zoomSensitivityLevels.arraySize - 1 ? $"{distance}+" :
                                     $"{zoomSensitivityLevels.GetArrayElementAtIndex(i - 1).FindPropertyRelative("distanceThreshold").floatValue} - {distance}";

                    EditorGUILayout.LabelField($"距离范围 {rangeText}: 敏感度倍数 {multiplier:F2}x", EditorStyles.miniLabel);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("动态缩放敏感度未启用或未配置级别", MessageType.Info);
            }

            EditorGUILayout.Space();

            // 提供预设配置按钮
            EditorGUILayout.LabelField("快速配置:", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("精细操作配置", GUILayout.Height(25)))
            {
                SetPrecisionConfiguration();
            }
            if (GUILayout.Button("平衡配置", GUILayout.Height(25)))
            {
                SetBalancedConfiguration();
            }
            if (GUILayout.Button("快速浏览配置", GUILayout.Height(25)))
            {
                SetFastNavigationConfiguration();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // 验证配置按钮
            if (GUILayout.Button("验证并修正配置", GUILayout.Height(30)))
            {
                ValidateConfiguration();
            }

            EditorGUILayout.EndVertical();
        }

        serializedObject.ApplyModifiedProperties();
    }

    void SetPrecisionConfiguration()
    {
        useDistanceBasedZoomSensitivity.boolValue = true;

        zoomSensitivityLevels.ClearArray();
        zoomSensitivityLevels.arraySize = 6;

        SetLevelValues(0, 1f, 0.1f);
        SetLevelValues(1, 3f, 0.3f);
        SetLevelValues(2, 5f, 0.5f);
        SetLevelValues(3, 10f, 1.0f);
        SetLevelValues(4, 20f, 2.0f);
        SetLevelValues(5, 50f, 3.0f);

        serializedObject.ApplyModifiedProperties();
        EditorUtility.SetDirty(target);
    }

    void SetBalancedConfiguration()
    {
        useDistanceBasedZoomSensitivity.boolValue = true;

        zoomSensitivityLevels.ClearArray();
        zoomSensitivityLevels.arraySize = 5;

        SetLevelValues(0, 2f, 0.2f);
        SetLevelValues(1, 5f, 0.5f);
        SetLevelValues(2, 10f, 1.0f);
        SetLevelValues(3, 20f, 2.0f);
        SetLevelValues(4, 50f, 4.0f);

        serializedObject.ApplyModifiedProperties();
        EditorUtility.SetDirty(target);
    }

    void SetFastNavigationConfiguration()
    {
        useDistanceBasedZoomSensitivity.boolValue = true;

        zoomSensitivityLevels.ClearArray();
        zoomSensitivityLevels.arraySize = 4;

        SetLevelValues(0, 3f, 0.5f);
        SetLevelValues(1, 10f, 1.5f);
        SetLevelValues(2, 25f, 3.0f);
        SetLevelValues(3, 50f, 5.0f);

        serializedObject.ApplyModifiedProperties();
        EditorUtility.SetDirty(target);
    }

    void SetLevelValues(int index, float distance, float multiplier)
    {
        var element = zoomSensitivityLevels.GetArrayElementAtIndex(index);
        element.FindPropertyRelative("distanceThreshold").floatValue = distance;
        element.FindPropertyRelative("sensitivityMultiplier").floatValue = multiplier;
    }

    void ValidateConfiguration()
    {
        bool hasChanges = false;

        for (int i = 0; i < zoomSensitivityLevels.arraySize; i++)
        {
            var element = zoomSensitivityLevels.GetArrayElementAtIndex(i);
            var distanceProp = element.FindPropertyRelative("distanceThreshold");
            var multiplierProp = element.FindPropertyRelative("sensitivityMultiplier");

            // 验证距离阈值
            if (distanceProp.floatValue < 0.1f)
            {
                distanceProp.floatValue = 0.1f;
                hasChanges = true;
            }

            // 验证敏感度倍数
            if (multiplierProp.floatValue < 0.01f)
            {
                multiplierProp.floatValue = 0.01f;
                hasChanges = true;
            }

            // 验证距离递增
            if (i > 0)
            {
                var prevElement = zoomSensitivityLevels.GetArrayElementAtIndex(i - 1);
                var prevDistance = prevElement.FindPropertyRelative("distanceThreshold").floatValue;

                if (distanceProp.floatValue <= prevDistance)
                {
                    distanceProp.floatValue = prevDistance + 0.1f;
                    hasChanges = true;
                }
            }
        }

        if (hasChanges)
        {
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(target);
            EditorUtility.DisplayDialog("配置验证", "配置已修正并保存", "确定");
        }
        else
        {
            EditorUtility.DisplayDialog("配置验证", "配置验证通过，无需修正", "确定");
        }
    }
}