using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Components.Actions;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - DaisyButton组件测试
    /// 测试DaisyButton组件的所有功能
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region DaisyButton组件测试

        /// <summary>
        /// 测试DaisyButton组件
        /// </summary>
        private void TestDaisyButton()
        {
            LogTest("DaisyButton 组件测试");

            try
            {
                // 测试基础按钮创建
                var basicButton = new DaisyButton("基础按钮");
                if (basicButton == null)
                {
                    throw new System.Exception("基础按钮创建失败");
                }

                // 测试静态工厂方法
                var primaryButton = DaisyButton.Primary("主要按钮");
                var secondaryButton = DaisyButton.Secondary("次要按钮");
                var accentButton = DaisyButton.Accent("强调按钮");
                var ghostButton = DaisyButton.Ghost("幽灵按钮");

                if (primaryButton == null || secondaryButton == null ||
                    accentButton == null || ghostButton == null)
                {
                    throw new System.Exception("静态工厂方法创建失败");
                }

                // 测试尺寸变体
                var largeButton = DaisyButton.Create("大按钮").SetLarge();
                var smallButton = DaisyButton.Create("小按钮").SetSmall();
                var extraSmallButton = DaisyButton.Create("超小按钮").SetExtraSmall();

                // 测试修饰符
                var outlineButton = DaisyButton.Create("轮廓按钮").SetOutline();
                var wideButton = DaisyButton.Create("宽按钮").SetWide();
                var blockButton = DaisyButton.Create("块级按钮").SetBlock();
                var circleButton = DaisyButton.Create("○").SetCircle();
                var squareButton = DaisyButton.Create("□").SetSquare();

                // 测试状态
                var loadingButton = DaisyButton.Create("加载中").SetLoading();
                var disabledButton = DaisyButton.Create("禁用按钮").SetDisabled();
                var activeButton = DaisyButton.Create("激活状态").SetActive();

                // 测试事件处理
                var clickButton = DaisyButton.Create("点击测试")
                    .OnClick(() => Logging.LogInfo("DaisyButton", "按钮被点击"));

                // 测试链式调用组合
                var combinedButton = DaisyButton.Create("组合按钮")
                    .SetPrimary()
                    .SetLarge()
                    .SetOutline()
                    .OnClick(() => Logging.LogInfo("DaisyButton", "组合按钮被点击"));

                // 验证类名设置
                if (!primaryButton.ClassListContains("daisy-btn-primary"))
                {
                    throw new System.Exception("主要按钮类名设置失败");
                }

                if (!largeButton.ClassListContains("daisy-btn-lg"))
                {
                    throw new System.Exception("大按钮类名设置失败");
                }

                if (!outlineButton.ClassListContains("daisy-btn-outline"))
                {
                    throw new System.Exception("轮廓按钮类名设置失败");
                }

                if (!loadingButton.ClassListContains("daisy-btn-loading"))
                {
                    throw new System.Exception("加载按钮类名设置失败");
                }

                if (!blockButton.ClassListContains("daisy-btn-block"))
                {
                    throw new System.Exception("块级按钮类名设置失败");
                }

                // 测试按钮文本设置
                var textButton = DaisyButton.Create("原始文本");
                textButton.SetText("修改后文本");
                if (textButton.Text != "修改后文本")
                {
                    throw new System.Exception("按钮文本设置失败");
                }

                // 添加到UI中进行视觉验证
                var buttonContainer = new VisualElement();
                buttonContainer.AddToClassList("daisy-button-test");
                buttonContainer.style.paddingTop = 20;
                buttonContainer.style.paddingBottom = 20;
                buttonContainer.style.paddingLeft = 20;
                buttonContainer.style.paddingRight = 20;
                buttonContainer.style.marginBottom = 20;
                buttonContainer.style.backgroundColor = new Color(0.98f, 0.98f, 0.98f, 1f);
                buttonContainer.style.borderTopLeftRadius = 8;
                buttonContainer.style.borderTopRightRadius = 8;
                buttonContainer.style.borderBottomLeftRadius = 8;
                buttonContainer.style.borderBottomRightRadius = 8;

                var heading = new Label("DaisyButton 组件测试");
                heading.style.fontSize = 18;
                heading.style.unityFontStyleAndWeight = FontStyle.Bold;
                heading.style.marginBottom = 10;
                heading.style.color = Color.black;
                buttonContainer.Add(heading);

                var description = new Label("测试按钮组件的各种变体、尺寸、修饰符和状态");
                description.style.fontSize = 12;
                description.style.marginBottom = 15;
                description.style.color = new Color(0.5f, 0.5f, 0.5f, 1f);
                buttonContainer.Add(description);

                // 创建按钮行 - 基础变体
                var row1 = new VisualElement();
                row1.style.flexDirection = FlexDirection.Row;
                row1.style.marginBottom = 10;
                row1.style.flexWrap = Wrap.Wrap;
                row1.AddToClassList("daisy-gap-2"); // 使用CSS类代替gap属性

                var row1Title = new Label("变体测试:");
                row1Title.style.fontSize = 14;
                row1Title.style.unityFontStyleAndWeight = FontStyle.Bold;
                row1Title.style.marginBottom = 5;
                buttonContainer.Add(row1Title);

                row1.Add(basicButton);
                row1.Add(primaryButton);
                row1.Add(secondaryButton);
                row1.Add(accentButton);
                row1.Add(ghostButton);
                buttonContainer.Add(row1);

                // 创建按钮行 - 尺寸测试
                var row2 = new VisualElement();
                row2.style.flexDirection = FlexDirection.Row;
                row2.style.marginBottom = 10;
                row2.style.flexWrap = Wrap.Wrap;
                row2.AddToClassList("daisy-gap-2");

                var row2Title = new Label("尺寸测试:");
                row2Title.style.fontSize = 14;
                row2Title.style.unityFontStyleAndWeight = FontStyle.Bold;
                row2Title.style.marginBottom = 5;
                row2Title.style.marginTop = 10;
                buttonContainer.Add(row2Title);

                row2.Add(extraSmallButton);
                row2.Add(smallButton);
                row2.Add(DaisyButton.Create("默认尺寸"));
                row2.Add(largeButton);
                buttonContainer.Add(row2);

                // 创建按钮行 - 修饰符测试
                var row3 = new VisualElement();
                row3.style.flexDirection = FlexDirection.Row;
                row3.style.marginBottom = 10;
                row3.style.flexWrap = Wrap.Wrap;
                row3.AddToClassList("daisy-gap-2");

                var row3Title = new Label("修饰符测试:");
                row3Title.style.fontSize = 14;
                row3Title.style.unityFontStyleAndWeight = FontStyle.Bold;
                row3Title.style.marginBottom = 5;
                row3Title.style.marginTop = 10;
                buttonContainer.Add(row3Title);

                row3.Add(outlineButton);
                row3.Add(wideButton);
                row3.Add(circleButton);
                row3.Add(squareButton);
                buttonContainer.Add(row3);

                // 创建按钮行 - 状态测试
                var row4 = new VisualElement();
                row4.style.flexDirection = FlexDirection.Row;
                row4.style.marginBottom = 10;
                row4.style.flexWrap = Wrap.Wrap;
                row4.AddToClassList("daisy-gap-2");

                var row4Title = new Label("状态测试:");
                row4Title.style.fontSize = 14;
                row4Title.style.unityFontStyleAndWeight = FontStyle.Bold;
                row4Title.style.marginBottom = 5;
                row4Title.style.marginTop = 10;
                buttonContainer.Add(row4Title);

                row4.Add(loadingButton);
                row4.Add(disabledButton);
                row4.Add(activeButton);
                row4.Add(clickButton);
                buttonContainer.Add(row4);

                // 块级按钮单独一行
                var blockTitle = new Label("布局测试:");
                blockTitle.style.fontSize = 14;
                blockTitle.style.unityFontStyleAndWeight = FontStyle.Bold;
                blockTitle.style.marginBottom = 5;
                blockTitle.style.marginTop = 10;
                buttonContainer.Add(blockTitle);

                buttonContainer.Add(blockButton);
                buttonContainer.Add(combinedButton);

                root.Add(buttonContainer);

                LogTestPass("DaisyButton 组件测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisyButton 组件测试失败: {ex.Message}");
            }
        }

        #endregion
    }
}
