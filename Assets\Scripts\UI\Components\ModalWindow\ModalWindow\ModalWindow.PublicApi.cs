using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 模态窗口组件 - 公共API功能
    /// 包含对外提供的公共方法和内容管理方法
    /// </summary>
    public partial class ModalWindow
    {
        #region Public Window Operations

        /// <summary>
        /// 关闭窗口
        /// </summary>
        public void CloseWindow()
        {
            // 触发关闭事件
            OnWindowClosed?.Invoke(this);

            // 通过事件系统通知窗口关闭
            UIEventSystem.TriggerCustomEvent("ModalWindowClosed", this);

            // 通知管理器
            if (ModalWindowManager.Instance != null)
            {
                ModalWindowManager.Instance.CloseWindow(this);
            }
        }

        /// <summary>
        /// 设置窗口尺寸（带动画效果的重载）
        /// </summary>
        public void SetSize(Vector2 size, bool animated = false)
        {
            if (animated)
            {
                // 这里可以添加动画效果的实现
                // Unity UI Toolkit的动画支持相对有限，可能需要使用Tween库
            }

            Size = size;
        }

        /// <summary>
        /// 设置窗口位置（带动画效果的重载）
        /// </summary>
        public void SetPosition(Vector2 position, bool animated = false)
        {
            if (animated)
            {
                // 这里可以添加动画效果的实现
            }

            Position = ClampPosition(position);
        }

        #endregion

        #region Content Management

        /// <summary>
        /// 添加内容到窗口
        /// </summary>
        public void SetContent(VisualElement content)
        {
            if (contentContainer != null)
            {
                contentContainer.Clear();
                if (content != null)
                {
                    contentContainer.Add(content);
                }
            }
        }

        /// <summary>
        /// 添加内容到窗口（追加模式）
        /// </summary>
        public void AddContent(VisualElement content)
        {
            if (contentContainer != null && content != null)
            {
                contentContainer.Add(content);
            }
        }

        /// <summary>
        /// 清空窗口内容
        /// </summary>
        public void ClearContent()
        {
            if (contentContainer != null)
            {
                contentContainer.Clear();
            }
        }

        #endregion
    }
}