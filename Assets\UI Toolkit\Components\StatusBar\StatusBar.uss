/* 状态栏根容器 */
.status-bar {
    flex-direction: row;
    background-color: rgba(56, 56, 56, 0.95);
    border-top-width: 1px;
    border-top-color: rgb(32, 32, 32);
    height: 24px;
    min-height: 24px;
    align-items: center;
    padding: 0 8px;
    justify-content: space-between;
}

/* 状态栏区域 */
.status-section {
    flex-direction: row;
    align-items: center;
}

.status-left {
    flex-grow: 0;
    flex-shrink: 0;
}

.status-center {
    flex-grow: 1;
    justify-content: center;
    position: relative;
}

.status-right {
    flex-grow: 0;
    flex-shrink: 0;
}

/* 状态项 */
.status-item {
    flex-direction: row;
    align-items: center;
    margin: 0 4px;
}

/* 状态文本 */
.status-text {
    color: rgb(210, 210, 210);
    font-size: 11px;
    margin: 0;
    padding: 0;
}

.hint-text {
    color: rgb(160, 160, 160);
    -unity-font-style: italic;
}

/* 状态图标 */
.status-icon {
    width: 12px;
    height: 12px;
    background-color: rgb(190, 190, 190);
    margin-right: 4px;
}

.tool-icon {
    background-color: rgb(76, 175, 80);
}

.zoom-icon {
    background-color: rgb(33, 150, 243);
}

/* 分隔符 */
.status-separator {
    width: 1px;
    height: 16px;
    background-color: rgb(96, 96, 96);
    margin: 0 6px;
}

/* 坐标显示 */
.coords-item {
}

.coords-label {
    color: rgb(160, 160, 160);
    font-size: 10px;
    margin-right: 4px;
}

.coords-value {
    color: rgb(210, 210, 210);
    font-size: 10px;
    min-width: 40px;
    -unity-text-align: middle-right;
}

.coords-separator {
    color: rgb(160, 160, 160);
    font-size: 10px;
    margin: 0 2px;
}

/* 性能信息 */
.perf-label {
    color: rgb(160, 160, 160);
    font-size: 10px;
    margin-right: 2px;
}

.perf-value {
    color: rgb(210, 210, 210);
    font-size: 10px;
    -unity-font-style: bold;
    margin-right: 8px;
    min-width: 30px;
    -unity-text-align: middle-right;
}

/* 进度指示器 */
.progress-container {
    flex-direction: row;
    align-items: center;
    background-color: rgba(64, 64, 64, 0.9);
    border-radius: 4px;
    padding: 4px 8px;
    display: none;
}

.progress-bar {
    width: 120px;
    height: 4px;
    margin-right: 8px;
}

.progress-bar > .unity-progress-bar__background {
    background-color: rgb(32, 32, 32);
    border-radius: 2px;
}

.progress-bar > .unity-progress-bar__progress {
    background-color: rgb(76, 175, 80);
    border-radius: 2px;
}

.progress-text {
    color: rgb(210, 210, 210);
    font-size: 10px;
    margin-right: 8px;
}

.progress-cancel {
    background-color: transparent;
    border-width: 1px;
    border-color: rgb(96, 96, 96);
    color: rgb(210, 210, 210);
    padding: 2px 6px;
    font-size: 9px;
    border-radius: 2px;
}

.progress-cancel:hover {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: rgb(244, 67, 54);
}

/* 通知消息 */
.notification {
    flex-direction: row;
    align-items: center;
    background-color: rgba(33, 150, 243, 0.2);
    border-width: 1px;
    border-color: rgba(33, 150, 243, 0.5);
    border-radius: 4px;
    padding: 4px 8px;
    display: none;
    max-width: 300px;
}

.notification.success {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.5);
}

.notification.warning {
    background-color: rgba(255, 193, 7, 0.2);
    border-color: rgba(255, 193, 7, 0.5);
}

.notification.error {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.5);
}

.notification-icon {
    width: 12px;
    height: 12px;
    background-color: rgb(33, 150, 243);
    margin-right: 6px;
    flex-shrink: 0;
}

.notification.success .notification-icon {
    background-color: rgb(76, 175, 80);
}

.notification.warning .notification-icon {
    background-color: rgb(255, 193, 7);
}

.notification.error .notification-icon {
    background-color: rgb(244, 67, 54);
}

.notification-text {
    color: rgb(210, 210, 210);
    font-size: 10px;
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notification-close {
    background-color: transparent;
    border-width: 0;
    width: 16px;
    height: 16px;
    padding: 2px;
    margin-left: 4px;
    flex-shrink: 0;
}

.notification-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.close-icon {
    width: 8px;
    height: 8px;
    background-color: rgb(160, 160, 160);
}

/* 控制按钮区域 */
.status-controls {
    flex-direction: row;
    align-items: center;
    margin-left: 8px;
}

.control-button {
    background-color: transparent;
    border-width: 1px;
    border-color: transparent;
    width: 20px;
    height: 20px;
    margin: 0 1px;
    padding: 2px;
    border-radius: 2px;
    align-items: center;
    justify-content: center;
}

.control-button:hover {
    background-color: rgba(96, 96, 96, 0.4);
    border-color: rgba(128, 128, 128, 0.6);
}

.control-button:active {
    background-color: rgba(48, 48, 48, 0.8);
    border-color: rgb(58, 121, 187);
}

.coord-icon {
    width: 12px;
    height: 12px;
    background-color: rgb(190, 190, 190);
}

.unit-label,
.precision-label {
    color: rgb(190, 190, 190);
    font-size: 9px;
    -unity-font-style: bold;
}

/* 高亮状态 */
.status-item.highlight {
    background-color: rgba(58, 121, 187, 0.2);
    border-radius: 2px;
    padding: 2px 4px;
}

.status-item.highlight .status-text {
    color: rgb(255, 255, 255);
}

/* 错误状态 */
.status-item.error .status-text {
    color: rgb(244, 67, 54);
}

.status-item.error .status-icon {
    background-color: rgb(244, 67, 54);
}

/* 警告状态 */
.status-item.warning .status-text {
    color: rgb(255, 193, 7);
}

.status-item.warning .status-icon {
    background-color: rgb(255, 193, 7);
}

/* 成功状态 */
.status-item.success .status-text {
    color: rgb(76, 175, 80);
}

.status-item.success .status-icon {
    background-color: rgb(76, 175, 80);
}

/* 动画效果 */
.notification {
    transition-property: opacity;
    transition-duration: 0.3s;
}

.progress-container {
    transition-property: opacity;
    transition-duration: 0.2s;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .performance-info {
        display: none;
    }
}

@media (max-width: 1000px) {
    .zoom-level {
        display: none;
    }
    
    .coords-value {
        min-width: 35px;
    }
}

@media (max-width: 800px) {
    .world-coords {
        display: none;
    }
    
    .action-hint {
        display: none;
    }
}

@media (max-width: 600px) {
    .status-bar {
        padding: 0 4px;
    }
    
    .status-separator {
        margin: 0 3px;
    }
    
    .coords-label {
        display: none;
    }
    
    .status-controls {
        margin-left: 4px;
    }
}
