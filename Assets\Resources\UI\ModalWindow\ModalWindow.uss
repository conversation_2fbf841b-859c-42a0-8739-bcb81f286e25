/* ModalWindow 组件样式 */

/* 模态窗口根容器 */
.modal-window {
    position: absolute;
    background-color: rgb(56, 56, 56);
    border-width: 1px;
    border-color: rgb(35, 35, 35);
    border-radius: 6px;
    min-width: 300px;
    min-height: 200px;
    /* Unity UI Toolkit 不支持 box-shadow，使用边框替代 */
    /* 确保在最顶层 */
    flex-direction: column;
    overflow: hidden;
}

/* 窗口标题栏 */
.modal-titlebar {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    min-height: 32px;
    background-color: rgb(48, 48, 48);
    border-bottom-width: 1px;
    border-bottom-color: rgb(35, 35, 35);
    padding: 0 12px;
    /* Unity UI Toolkit 不支持 cursor 属性 */
}

.modal-titlebar:hover {
    background-color: rgb(52, 52, 52);
}

.modal-titlebar:active {
    background-color: rgb(45, 45, 45);
}

/* 窗口标题 */
.modal-title {
    color: rgb(210, 210, 210);
    font-size: 12px;
    -unity-font-style: bold;
    -unity-text-align: middle-left;
    flex-grow: 1;
    /* Unity UI Toolkit 不支持 white-space, text-overflow */
    overflow: hidden;
}

/* 关闭按钮 */
.modal-close-btn {
    width: 20px;
    height: 20px;
    background-color: transparent;
    border-width: 0;
    color: rgb(180, 180, 180);
    font-size: 16px;
    -unity-font-style: bold;
    -unity-text-align: middle-center;
    border-radius: 3px;
    padding: 0;
    margin-left: 8px;
    /* 过渡效果 */
    transition-duration: 0.15s;
    transition-property: background-color, color;
}

.modal-close-btn:hover {
    background-color: rgb(232, 17, 35);
    color: rgb(255, 255, 255);
}

.modal-close-btn:active {
    background-color: rgb(196, 43, 28);
    color: rgb(255, 255, 255);
}

/* 窗口内容区域 */
.modal-content {
    flex-grow: 1;
    background-color: rgb(56, 56, 56);
    overflow: hidden;
    flex-direction: column;
}

/* 窗口尺寸变体 */
.modal-window.small {
    min-width: 250px;
    min-height: 150px;
}

.modal-window.medium {
    min-width: 400px;
    min-height: 300px;
}

.modal-window.large {
    min-width: 600px;
    min-height: 450px;
}

.modal-window.extra-large {
    min-width: 800px;
    min-height: 600px;
}

/* 窗口状态变体 */
.modal-window.focused {
    border-color: rgb(62, 95, 150);
    /* Unity UI Toolkit 不支持 box-shadow */
}

.modal-window.dragging {
    opacity: 0.9;
    /* Unity UI Toolkit 不支持 cursor 属性 */
}

.modal-window.resizing {
    opacity: 0.95;
}

/* 调整尺寸手柄 */
.resize-handle {
    position: absolute;
    background-color: transparent;
    /* Unity UI Toolkit 不支持 cursor 属性 */
}

.resize-handle:hover {
    background-color: rgba(100, 150, 200, 0.3);
}

/* 右边框调整手柄 */
.resize-handle-right {
    top: 0;
    right: -3px;
    width: 6px;
    height: 100%;
}

/* 底边框调整手柄 */
.resize-handle-bottom {
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 6px;
}

/* 右下角调整手柄 */
.resize-handle-bottom-right {
    bottom: -3px;
    right: -3px;
    width: 12px;
    height: 12px;
    background-color: rgba(100, 150, 200, 0.2);
    border-radius: 2px;
}

.resize-handle-bottom-right:hover {
    background-color: rgba(100, 150, 200, 0.5);
}

/* Unity UI Toolkit 不支持 @media 查询和响应式设计 */
/* 这些样式需要在代码中动态应用 */

/* Unity UI Toolkit 不支持 CSS 动画和关键帧 */
/* 动画效果需要通过 C# 代码实现 */

/* 数据库连接窗口特定样式 - 现代化设计 */
.database-connection-content {
    flex-direction: column;
    padding: 0;
    background-color: rgb(48, 48, 48);
    border-radius: 6px;
    overflow: hidden;
}

/* 窗口头部区域 */
.database-header {
    background-color: rgb(42, 42, 42);
    padding: 20px 24px 16px 24px;
    border-bottom-width: 1px;
    border-bottom-color: rgb(60, 60, 60);
}

.database-title {
    color: rgb(240, 240, 240);
    font-size: 16px;
    -unity-font-style: bold;
    -unity-text-align: middle-left;
    margin-bottom: 4px;
}

.database-subtitle {
    color: rgb(180, 180, 180);
    font-size: 12px;
    -unity-text-align: middle-left;
}

/* 表单容器 */
.form-container {
    flex-direction: column;
    padding: 24px;
    background-color: rgb(48, 48, 48);
}

/* 表单分组 */
.form-group {
    flex-direction: column;
    margin-bottom: 16px;
}

.form-group-title {
    color: rgb(220, 220, 220);
    font-size: 13px;
    -unity-font-style: bold;
    -unity-text-align: middle-left;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom-width: 1px;
    border-bottom-color: rgb(65, 65, 65);
    height: 20px;
    min-height: 20px;
}

/* 字段行布局 */
.field-row {
    flex-direction: row;
    align-items: flex-start;
    margin-bottom: 8px;
}

.field-row.single {
    flex-direction: column;
}

.field-container {
    flex-direction: column;
    flex-grow: 1;
    min-width: 120px;
    flex-shrink: 0;
}

.field-container.half-width {
    flex-basis: calc(50% - 8px);
    max-width: calc(50% - 8px);
    min-width: 200px;
}

.field-container.full-width {
    flex-basis: 100%;
    max-width: 100%;
}

/* 字段标签 */
.field-label {
    color: rgb(200, 200, 200);
    font-size: 12px;
    -unity-font-style: normal;
    -unity-text-align: middle-left;
    margin-bottom: 4px;
    height: 16px;
    min-height: 16px;
}

/* 输入框样式 */
.field-input {
    height: 32px;
    background-color: rgb(56, 56, 56);
    border-width: 1px;
    border-color: rgb(70, 70, 70);
    border-radius: 4px;
    padding: 8px 12px;
    color: rgb(230, 230, 230);
    font-size: 13px;
    transition-duration: 0.2s;
    transition-property: border-color, background-color;
}

.field-input:focus {
    border-color: rgb(100, 150, 255);
    background-color: rgb(60, 60, 60);
}

.field-input:hover {
    border-color: rgb(85, 85, 85);
}

/* Unity UI Toolkit 不支持 ::placeholder 伪类 */
/* 需要在 C# 代码中通过 placeholder-text 属性设置占位符文本样式 */

/* Toggle 开关样式 */
.toggle-container {
    flex-direction: row;
    align-items: center;
    height: 36px;
    min-height: 36px;
    padding: 6px 0;
    margin-bottom: 4px;
}

.toggle-label {
    color: rgb(200, 200, 200);
    font-size: 13px;
    -unity-text-align: middle-left;
    flex-grow: 1;
    height: 20px;
    min-height: 20px;
}

.toggle-input {
    align-self: center;
    min-width: 40px;
    height: 20px;
    flex-shrink: 0;
}

/* 高级设置区域 */
.advanced-settings {
    background-color: rgb(52, 52, 52);
    border-radius: 6px;
    padding: 16px;
    border-width: 1px;
    border-color: rgb(65, 65, 65);
    margin-top: 8px;
}

.advanced-title {
    color: rgb(190, 190, 190);
    font-size: 12px;
    -unity-font-style: bold;
    -unity-text-align: middle-left;
    margin-bottom: 16px;
    height: 16px;
    min-height: 16px;
}

.advanced-grid {
    flex-direction: column;
}

/* 按钮区域 */
.button-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background-color: rgb(44, 44, 44);
    border-top-width: 1px;
    border-top-color: rgb(60, 60, 60);
}

.button-left-group {
    flex-direction: row;
}

.button-right-group {
    flex-direction: row;
}

/* 按钮基础样式 */
.db-button {
    height: 34px;
    border-radius: 5px;
    padding: 8px 20px;
    font-size: 13px;
    -unity-font-style: normal;
    transition-duration: 0.2s;
    transition-property: background-color, border-color, color;
    min-width: 80px;
    -unity-text-align: middle-center;
}

/* 主要按钮 */
.test-button {
    background-color: rgb(64, 158, 255);
    color: rgb(255, 255, 255);
    border-width: 1px;
    border-color: rgb(64, 158, 255);
}

.test-button:hover {
    background-color: rgb(84, 174, 255);
    border-color: rgb(84, 174, 255);
}

.test-button:active {
    background-color: rgb(44, 142, 235);
    border-color: rgb(44, 142, 235);
}

.test-button:disabled {
    background-color: rgb(80, 80, 80);
    border-color: rgb(80, 80, 80);
    color: rgb(140, 140, 140);
}

/* 成功按钮 */
.save-button {
    background-color: rgb(72, 187, 120);
    color: rgb(255, 255, 255);
    border-width: 1px;
    border-color: rgb(72, 187, 120);
}

.save-button:hover {
    background-color: rgb(92, 207, 140);
    border-color: rgb(92, 207, 140);
}

.save-button:active {
    background-color: rgb(52, 167, 100);
    border-color: rgb(52, 167, 100);
}

/* 次要按钮 */
.cancel-button {
    background-color: transparent;
    color: rgb(180, 180, 180);
    border-width: 1px;
    border-color: rgb(100, 100, 100);
}

.cancel-button:hover {
    background-color: rgb(70, 70, 70);
    border-color: rgb(120, 120, 120);
    color: rgb(200, 200, 200);
}

.cancel-button:active {
    background-color: rgb(60, 60, 60);
    border-color: rgb(110, 110, 110);
}

/* 状态区域 */
.status-container {
    padding: 16px 24px;
    background-color: rgb(52, 52, 52);
    border-top-width: 1px;
    border-top-color: rgb(60, 60, 60);
    flex-direction: row;
    align-items: center;
}

.status-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-label {
    font-size: 13px;
    -unity-text-align: middle-left;
    color: rgb(190, 190, 190);
    flex-grow: 1;
}

.status-label.status-success {
    color: rgb(72, 187, 120);
}

.status-label.status-error {
    color: rgb(255, 99, 99);
}

.status-label.status-info {
    color: rgb(64, 158, 255);
}

.status-label.status-warning {
    color: rgb(255, 193, 7);
}

/* 加载指示器 */
.loading-indicator {
    width: 18px;
    height: 18px;
    background-color: rgb(64, 158, 255);
    border-radius: 50%;
    flex-shrink: 0;
    /* 旋转动画需要通过代码实现 */
}

.loading-indicator.hidden {
    display: none;
}

/* 连接状态指示器 */
.connection-status {
    flex-direction: row;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: rgb(58, 58, 58);
    border-width: 1px;
    border-color: rgb(75, 75, 75);
}

.connection-status.connected {
    background-color: rgb(46, 125, 50);
    border-color: rgb(72, 187, 120);
}

.connection-status.disconnected {
    background-color: rgb(211, 47, 47);
    border-color: rgb(255, 99, 99);
}

.connection-status.connecting {
    background-color: rgb(25, 118, 210);
    border-color: rgb(64, 158, 255);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgb(120, 120, 120);
}

.status-dot.connected {
    background-color: rgb(72, 187, 120);
}

.status-dot.disconnected {
    background-color: rgb(255, 99, 99);
}

.status-dot.connecting {
    background-color: rgb(64, 158, 255);
}

.status-text {
    color: rgb(220, 220, 220);
    font-size: 12px;
    -unity-font-style: normal;
}