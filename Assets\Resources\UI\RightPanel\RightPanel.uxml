<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xmlns:custom="BlastingDesign.UI.Core" editor-extension-mode="False">
    <Style src="project://database/Assets/UI%20Toolkit/Components/RightPanel/RightPanel.uss?fileID=7433441132597879392&amp;guid=53bd5ebdb59e44c8e842ff12690fabcb&amp;type=3#RightPanel" />
    <Style src="project://database/Assets/UI%20Toolkit/Common/ScrollBarStyles.uss" />
    <ui:VisualElement name="right-panel-root" class="right-panel grow">
        <ui:VisualElement name="panel-header" class="panel-header">
            <ui:Label text="属性" name="panel-title" class="panel-title" />
            <ui:VisualElement name="header-controls" class="header-controls">
                <ui:Button name="collapse-button" tooltip="折叠/展开面板" class="header-button collapse-button">
                    <ui:VisualElement class="collapse-icon" />
                </ui:Button>
                <ui:Button name="lock-button" tooltip="锁定选择" class="header-button">
                    <ui:VisualElement class="lock-icon" />
                </ui:Button>
                <ui:Button name="settings-button" tooltip="面板设置" class="header-button">
                    <ui:VisualElement class="settings-icon" />
                </ui:Button>
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement name="object-info" class="object-info">
            <ui:VisualElement name="object-header" class="object-header">
                <ui:Toggle name="object-active" class="object-active-toggle" />
                <ui:TextField name="object-name" value="未选择对象" class="object-name-field" />
                <ui:Button name="object-icon" class="object-icon-button">
                    <ui:VisualElement class="object-icon" />
                </ui:Button>
            </ui:VisualElement>
            <ui:VisualElement name="object-tags" class="object-tags">
                <ui:Label text="标签:" class="tag-label" />
                <ui:DropdownField name="tag-dropdown" class="tag-dropdown" />
                <ui:Label text="图层:" class="layer-label" />
                <ui:DropdownField name="layer-dropdown" class="layer-dropdown" />
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement name="panel-content" class="panel-content">
            <ui:ScrollView name="properties-scroll" 
                           mode="Vertical"
                           vertical-scroller-visibility="Auto"
                           horizontal-scroller-visibility="Hidden"
                           mouse-wheel-scroll-size="18"
                           vertical-page-size="1"
                           elasticity="0.1">
                <ui:VisualElement name="properties-container" class="properties-container">
                    <ui:Foldout text="变换" name="transform-group" value="true" class="property-group">
                        <ui:VisualElement class="property-section">
                            <ui:Vector3Field label="位置" name="position-field" class="property-field" />
                            <ui:Vector3Field label="旋转" name="rotation-field" class="property-field" />
                            <ui:Vector3Field label="缩放" name="scale-field" value="1,1,1" class="property-field" />
                        </ui:VisualElement>
                    </ui:Foldout>
                    <ui:Foldout text="渲染" name="renderer-group" value="true" class="property-group">
                        <ui:VisualElement class="property-section">
                            <custom:CustomColorField label="颜色" name="color-field" value="RGBA(1.000, 1.000, 1.000, 1.000)" class="property-field" />
                            <ui:Slider label="透明度" name="alpha-slider" high-value="1" low-value="0" value="1" class="property-field" />
                            <custom:CustomObjectField label="材质" name="material-field" class="property-field" />
                            <ui:Toggle label="投射阴影" name="cast-shadows" value="true" class="property-field" />
                            <ui:Toggle label="接收阴影" name="receive-shadows" value="true" class="property-field" />
                        </ui:VisualElement>
                    </ui:Foldout>
                    <ui:Foldout text="物理" name="physics-group" value="false" class="property-group">
                        <ui:VisualElement class="property-section">
                            <ui:FloatField label="质量" name="mass-field" value="1" class="property-field" />
                            <ui:FloatField label="阻力" name="drag-field" value="0" class="property-field" />
                            <ui:FloatField label="角阻力" name="angular-drag-field" value="0.05" class="property-field" />
                            <ui:Toggle label="使用重力" name="use-gravity" value="true" class="property-field" />
                            <ui:Toggle label="运动学" name="is-kinematic" value="false" class="property-field" />
                        </ui:VisualElement>
                    </ui:Foldout>
                    <ui:Foldout text="碰撞" name="collider-group" value="false" class="property-group">
                        <ui:VisualElement class="property-section">
                            <ui:DropdownField label="碰撞器类型" name="collider-type" class="property-field" />
                            <ui:Toggle label="是触发器" name="is-trigger" value="false" class="property-field" />
                            <ui:Vector3Field label="中心" name="collider-center" class="property-field" />
                            <ui:Vector3Field label="大小" name="collider-size" value="1,1,1" class="property-field" />
                        </ui:VisualElement>
                    </ui:Foldout>
                </ui:VisualElement>
            </ui:ScrollView>
            <ui:VisualElement name="empty-state" class="empty-state">
                <ui:VisualElement class="empty-icon" />
                <ui:Label text="未选择对象" class="empty-message" />
                <ui:Label text="在左侧面板中选择一个对象以查看其属性" class="empty-hint" />
            </ui:VisualElement>
        </ui:VisualElement>
        <ui:VisualElement name="resize-handle" class="resize-handle" />
        <ui:VisualElement name="component-dropdown" class="component-dropdown">
            <ui:VisualElement name="component-category" class="component-category">
                <ui:Label text="渲染" class="category-title" />
                <ui:Button text="Mesh Renderer" name="add-mesh-renderer" class="component-item" />
                <ui:Button text="Skinned Mesh Renderer" name="add-skinned-mesh-renderer" class="component-item" />
                <ui:Button text="Line Renderer" name="add-line-renderer" class="component-item" />
            </ui:VisualElement>
            <ui:VisualElement class="component-separator" />
            <ui:VisualElement name="component-category" class="component-category">
                <ui:Label text="物理" class="category-title" />
                <ui:Button text="Rigidbody" name="add-rigidbody" class="component-item" />
                <ui:Button text="Box Collider" name="add-box-collider" class="component-item" />
                <ui:Button text="Sphere Collider" name="add-sphere-collider" class="component-item" />
                <ui:Button text="Capsule Collider" name="add-capsule-collider" class="component-item" />
            </ui:VisualElement>
            <ui:VisualElement class="component-separator" />
            <ui:VisualElement name="component-category" class="component-category">
                <ui:Label text="脚本" class="category-title" />
                <ui:Button text="自定义脚本" name="add-custom-script" class="component-item" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
