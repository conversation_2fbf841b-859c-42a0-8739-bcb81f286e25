/* DaisyUI 按钮组件样式 */

/* ========================================
   基础按钮样式
   ======================================== */

.daisy-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    padding: var(--btn-padding-y) var(--btn-padding-x);
    font-size: var(--btn-font-size);
    border-width: var(--border-width);
    border-color: transparent;
    transition: all var(--transition-duration) var(--transition-easing);
    white-space: nowrap;
    -unity-text-align: middle-center;
    min-height: var(--btn-min-height);
    background-color: var(--neutral);
    color: var(--neutral-content);
    flex-shrink: 0;
    text-overflow: ellipsis;
    overflow: hidden;
}

.daisy-btn:hover {
    background-color: var(--neutral-focus);
    border-color: var(--neutral-focus);
    translate: 0 -1px;
}

.daisy-btn:active {
    translate: 0 0;
}

.daisy-btn:focus {
}

/* ========================================
   按钮变体样式
   ======================================== */

/* 主要按钮 */
.daisy-btn-primary {
    background-color: var(--primary);
    color: var(--primary-content);
    border-color: var(--primary);
}

.daisy-btn-primary:hover {
    background-color: var(--primary-focus);
    border-color: var(--primary-focus);
}

/* 次要按钮 */
.daisy-btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-content);
    border-color: var(--secondary);
}

.daisy-btn-secondary:hover {
    background-color: var(--secondary-focus);
    border-color: var(--secondary-focus);
}

/* 强调按钮 */
.daisy-btn-accent {
    background-color: var(--accent);
    color: var(--accent-content);
    border-color: var(--accent);
}

.daisy-btn-accent:hover {
    background-color: var(--accent-focus);
    border-color: var(--accent-focus);
}

/* 中性按钮 */
.daisy-btn-neutral {
    background-color: var(--neutral);
    color: var(--neutral-content);
    border-color: var(--neutral);
}

.daisy-btn-neutral:hover {
    background-color: var(--neutral-focus);
    border-color: var(--neutral-focus);
}

/* 幽灵按钮 */
.daisy-btn-ghost {
    background-color: transparent;
    border-color: transparent;
    color: var(--base-content);
}

.daisy-btn-ghost:hover {
    background-color: var(--base-200);
    border-color: var(--base-200);
}

/* 链接按钮 */
.daisy-btn-link {
    background-color: transparent;
    border-color: transparent;
    color: var(--primary);
}

.daisy-btn-link:hover {
    background-color: transparent;
    border-color: transparent;
    color: var(--primary-focus);
}

/* 信息按钮 */
.daisy-btn-info {
    background-color: var(--info);
    color: var(--info-content);
    border-color: var(--info);
}

.daisy-btn-info:hover {
    background-color: var(--info);
    opacity: 0.9;
}

/* 成功按钮 */
.daisy-btn-success {
    background-color: var(--success);
    color: var(--success-content);
    border-color: var(--success);
}

.daisy-btn-success:hover {
    background-color: var(--success);
    opacity: 0.9;
}

/* 警告按钮 */
.daisy-btn-warning {
    background-color: var(--warning);
    color: var(--warning-content);
    border-color: var(--warning);
}

.daisy-btn-warning:hover {
    background-color: var(--warning);
    opacity: 0.9;
}

/* 错误按钮 */
.daisy-btn-error {
    background-color: var(--error);
    color: var(--error-content);
    border-color: var(--error);
}

.daisy-btn-error:hover {
    background-color: var(--error);
    opacity: 0.9;
}

/* ========================================
   按钮尺寸样式
   ======================================== */

/* 超小尺寸 */
.daisy-btn-xs {
    --btn-padding-x: 8px;
    --btn-padding-y: 4px;
    --btn-font-size: var(--font-size-xs);
    --btn-min-height: 24px;
    --btn-gap: 4px;
}

/* 小尺寸 */
.daisy-btn-sm {
    --btn-padding-x: 12px;
    --btn-padding-y: 6px;
    --btn-font-size: var(--font-size-sm);
    --btn-min-height: 32px;
    --btn-gap: 6px;
}

/* 中等尺寸（默认） */
.daisy-btn-md {
    --btn-padding-x: 16px;
    --btn-padding-y: 8px;
    --btn-font-size: var(--font-size-base);
    --btn-min-height: 40px;
    --btn-gap: 8px;
}

/* 大尺寸 */
.daisy-btn-lg {
    --btn-padding-x: 24px;
    --btn-padding-y: 12px;
    --btn-font-size: var(--font-size-lg);
    --btn-min-height: 48px;
    --btn-gap: 10px;
}

/* 超大尺寸 */
.daisy-btn-xl {
    --btn-padding-x: 32px;
    --btn-padding-y: 16px;
    --btn-font-size: var(--font-size-xl);
    --btn-min-height: 56px;
    --btn-gap: 12px;
}

/* ========================================
   按钮修饰符样式
   ======================================== */

/* 轮廓样式 */
.daisy-btn-outline {
    background-color: transparent;
    border-color: var(--primary);
}

.daisy-btn-outline:hover {
    background-color: var(--primary);
    color: var(--base-100);
}

/* 轮廓 + 变体组合 */
.daisy-btn-primary.daisy-btn-outline {
    color: var(--primary);
    border-color: var(--primary);
}

.daisy-btn-primary.daisy-btn-outline:hover {
    background-color: var(--primary);
    color: var(--primary-content);
}

.daisy-btn-secondary.daisy-btn-outline {
    color: var(--secondary);
    border-color: var(--secondary);
}

.daisy-btn-secondary.daisy-btn-outline:hover {
    background-color: var(--secondary);
    color: var(--secondary-content);
}

.daisy-btn-accent.daisy-btn-outline {
    color: var(--accent);
    border-color: var(--accent);
}

.daisy-btn-accent.daisy-btn-outline:hover {
    background-color: var(--accent);
    color: var(--accent-content);
}

/* 宽按钮 */
.daisy-btn-wide {
    --btn-padding-x: 32px;
}

/* 块级按钮 */
.daisy-btn-block {
    width: 100%;
}

/* 圆形按钮 */
.daisy-btn-circle {
    border-radius: var(--border-radius-full);
    width: var(--btn-min-height);
    padding: 0;
}

/* 方形按钮 */
.daisy-btn-square {
    border-radius: var(--border-radius);
    width: var(--btn-min-height);
    padding: 0;
}

/* 活跃状态 */
.daisy-btn-active {
    scale: 0.95;
}

/* 加载状态 */
.daisy-btn-loading {
    opacity: 0.8;
}

.daisy-btn-loading .daisy-loading-spinner {
    width: 16px;
    height: 16px;
    border-width: 2px;
    border-radius: var(--border-radius-full);
}

/* 禁用状态 */
.daisy-btn-disabled,
.daisy-btn:disabled {
    opacity: 0.6;
}

/* ========================================
   按钮组样式
   ======================================== */

.daisy-btn-group {
    display: flex;
}

.daisy-btn-group .daisy-btn {
    border-radius: var(--border-radius);
}

/* 连接的按钮组 */
.daisy-btn-group.daisy-btn-group-connected {
}

.daisy-btn-group.daisy-btn-group-connected .daisy-btn {
    border-radius: 0;
    border-right-width: 0;
}

/* Unity UI Toolkit 不支持 :first-child 伪类选择器 */
.daisy-btn-group.daisy-btn-group-connected .daisy-btn.first-item {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

/* Unity UI Toolkit 不支持 :last-child 伪类选择器 */
.daisy-btn-group.daisy-btn-group-connected .daisy-btn.last-item {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    border-right-width: var(--border-width);
}

/* 垂直按钮组 */
.daisy-btn-group.daisy-btn-group-vertical {
    flex-direction: column;
}

.daisy-btn-group.daisy-btn-group-vertical.daisy-btn-group-connected .daisy-btn {
    border-radius: 0;
    border-bottom-width: 0;
    border-right-width: var(--border-width);
}

/* Unity UI Toolkit 不支持 :first-child 伪类选择器 */
.daisy-btn-group.daisy-btn-group-vertical.daisy-btn-group-connected .daisy-btn.first-item {
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    border-bottom-left-radius: 0;
}

/* Unity UI Toolkit 不支持 :last-child 伪类选择器 */
.daisy-btn-group.daisy-btn-group-vertical.daisy-btn-group-connected .daisy-btn.last-item {
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    border-top-right-radius: 0;
    border-bottom-width: var(--border-width);
}

/* ========================================
   动画定义
   ======================================== */

@keyframes daisy-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ========================================
   无障碍支持
   ======================================== */

.daisy-btn:focus {
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .daisy-btn {
        border-width: 2px;
    }
    
    .daisy-btn-outline {
        border-width: 2px;
    }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
    .daisy-btn {
        transition: none;
    }
    
    .daisy-btn:hover {
        translate: none;
    }
    
    .daisy-btn:active {
        translate: none;
    }
    
    .daisy-loading-spinner {
    }
}

/* ========================================
   主题适配
   ======================================== */

/* 暗色主题适配 */
.theme-dark .daisy-btn-ghost:hover {
    background-color: var(--base-300);
}

.theme-dark .daisy-btn:focus {
}

/* 自定义主题适配 */
.theme-cupcake .daisy-btn {
    border-radius: var(--border-radius-lg);
}

.theme-synthwave .daisy-btn {
    text-shadow: 0 0 10px var(--base-300);
}

.theme-synthwave .daisy-btn:hover {
}