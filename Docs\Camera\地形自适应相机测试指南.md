# 地形自适应相机测试指南

## 概述

本指南介绍如何测试和验证新实现的地形自适应相机功能，包括自适应缩放和自适应平移。

## 测试组件

### TerrainAdaptiveCameraTester

这是专门用于测试地形自适应功能的测试组件，提供了完整的测试环境和调试工具。

## 快速开始

### 1. 设置测试环境

1. 在场景中创建一个空的GameObject
2. 添加 `TerrainAdaptiveCameraTester` 组件
3. 确保场景中有 `InputManager` 组件
4. 运行场景

### 2. 基本测试操作

**快捷键控制**：
- `F1` - 切换地形自适应缩放
- `F2` - 切换地形自适应平移  
- `F3` - 创建测试地形
- `F4` - 重置相机位置

**GUI控制面板**：
- 右上角显示实时控制面板
- 可以通过按钮切换功能开关
- 滑块调节地形跟随平滑度

## 测试场景

### 自动生成测试地形

测试器会自动创建一个波浪形的测试地形：
- 大小：50x10x50 单位
- 使用Perlin噪声生成高度变化
- 包含碰撞器用于射线检测

### 测试步骤

#### 1. 地形自适应缩放测试

1. 按 `F3` 创建测试地形
2. 将鼠标移动到地形不同高度的位置
3. 使用鼠标滚轮进行缩放
4. 观察相机是否基于射线检测到的表面距离进行缩放
5. 按 `F1` 切换功能，对比差异

**预期行为**：
- 启用时：缩放基于屏幕中心射线到地形表面的实际距离
- 禁用时：缩放基于固定的旋转中心距离

#### 2. 地形自适应平移测试

1. 确保测试地形存在
2. 使用鼠标中键拖拽进行平移
3. 观察相机在移动过程中是否保持与地形的相对高度
4. 按 `F2` 切换功能，对比差异

**预期行为**：
- 启用时：相机平移时会跟随地形高度变化，保持相对高度不变
- 禁用时：相机只在水平面移动，不考虑地形高度

#### 3. 平滑度调节测试

1. 启用地形自适应平移
2. 使用GUI面板中的滑块调节平滑度（0-1）
3. 进行平移操作，观察高度跟随的平滑程度

**平滑度效果**：
- 0：立即跟随地形高度（可能产生突兀变化）
- 0.1：默认值，平滑跟随
- 1：非常平滑，但响应较慢

## 调试信息

### 屏幕显示信息

左上角显示实时调试信息：
- 自适应缩放状态
- 自适应平移状态
- 相机当前位置
- 地面高度
- 相机相对高度
- 平滑度设置

### 控制台日志

测试器会在控制台输出详细的状态变化信息：
- 功能启用/禁用状态
- 地形创建确认
- 相机重置确认

## 高级测试

### 复杂地形测试

1. 创建更复杂的地形（山峰、峡谷等）
2. 测试极端高度差情况下的相机行为
3. 验证射线检测的准确性

### 性能测试

1. 在复杂场景中测试性能影响
2. 观察射线检测的频率和开销
3. 调整相关参数优化性能

### 边界情况测试

1. 测试没有地形物体的区域
2. 测试射线检测失败的情况
3. 验证回退机制是否正常工作

## 故障排除

### 常见问题

1. **测试器无法找到InputManager**
   - 确保场景中存在InputManager组件
   - 检查InputManager是否正确配置

2. **地形自适应功能无效**
   - 检查InputManager中的相关设置是否启用
   - 确认groundLayerMask包含地形图层

3. **射线检测不准确**
   - 检查地形是否有正确的碰撞器
   - 确认图层设置正确

4. **平移时高度跟随过于突兀**
   - 增加terrainFollowSmoothness值
   - 检查地形高度变化是否过于剧烈

### 调试技巧

1. 启用 `showDebugRaycast` 查看射线可视化
2. 使用Scene视图观察相机移动轨迹
3. 监控控制台输出的调试信息

## 配置参数

### InputManager设置

- `useTerrainAdaptiveZoom`: 启用地形自适应缩放
- `useTerrainAdaptivePan`: 启用地形自适应平移
- `terrainFollowSmoothness`: 地形跟随平滑度
- `groundLayerMask`: 地面检测图层掩码
- `showDebugRaycast`: 显示射线调试信息

### 测试器设置

- `showDebugInfo`: 显示调试信息
- `terrainSize`: 测试地形大小
- `terrainResolution`: 地形分辨率

## 总结

通过这个测试系统，您可以全面验证地形自适应相机功能的正确性和性能。建议在不同的场景和地形条件下进行测试，确保功能在各种情况下都能正常工作。
