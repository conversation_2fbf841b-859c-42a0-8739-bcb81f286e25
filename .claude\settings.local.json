{"permissions": {"allow": ["Bash(grep:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(cp:*)", "Bash(rm:*)", "mcp__ide__getDiagnostics", "WebFetch(domain:daisyui.com)", "WebFetch(domain:docs.unity3d.com)", "<PERSON><PERSON>(csc:*)", "mcp__ide__executeCode", "Bash(/Applications/Unity/Hub/Editor/*/Unity.app/Contents/MacOS/Unity)", "Bash(-batchmode -quit -projectPath . -executeMethod UnityEditor.Compilation.CompilationPipeline.RequestScriptCompilation -logFile /tmp/unity_compile.log)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(sed:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebFetch(domain:raw.githubusercontent.com)", "Bash(ls:*)", "Bash(-exec csc -t:library -nologo -nowarn:1701,1702 -r:UnityEngine.dll -r:UnityEngine.CoreModule.dll -r:UnityEngine.UIElementsModule.dll {})"], "deny": []}}