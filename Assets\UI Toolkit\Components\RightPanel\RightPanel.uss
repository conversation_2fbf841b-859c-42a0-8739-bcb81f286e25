/* 右侧面板根容器 */
.right-panel {
    width: 100%;
    min-width: 200px;
    background-color: rgba(48, 48, 48, 0.95);
    border-left-width: 1px;
    border-left-color: rgb(32, 32, 32);
    flex-direction: column;
    position: relative;
}

/* 面板标题栏 */
.panel-header {
    flex-direction: row;
    background-color: rgba(64, 64, 64, 0.9);
    border-bottom-width: 1px;
    border-bottom-color: rgb(32, 32, 32);
    padding: 6px 8px;
    align-items: center;
    justify-content: space-between;
    min-height: 28px;
}

.panel-title {
    color: rgb(210, 210, 210);
    font-size: 12px;
    -unity-font-style: bold;
    flex-grow: 1;
}

.header-controls {
    flex-direction: row;
    align-items: center;
}

.header-button {
    background-color: transparent;
    border-width: 0;
    width: 20px;
    height: 20px;
    margin: 0 2px;
    padding: 2px;
    border-radius: 2px;
    align-items: center;
    justify-content: center;
}

.header-button:hover {
    background-color: rgba(96, 96, 96, 0.6);
}

.header-button:active {
    background-color: rgba(48, 48, 48, 0.8);
}

/* 图标样式 */
.collapse-icon,
.lock-icon,
.settings-icon,
.object-icon,
.menu-icon,
.empty-icon {
    width: 12px;
    height: 12px;
    background-color: rgb(190, 190, 190);
}

/* 对象信息栏 */
.object-info {
    background-color: rgba(56, 56, 56, 0.9);
    border-bottom-width: 1px;
    border-bottom-color: rgb(32, 32, 32);
    padding: 8px;
}

.object-header {
    flex-direction: row;
    align-items: center;
    margin-bottom: 6px;
}

.object-active-toggle {
    margin-right: 6px;
}

.object-name-field {
    flex-grow: 1;
    margin-right: 6px;
}

.object-name-field > .unity-base-text-field__input {
    background-color: rgb(42, 42, 42);
    border-color: rgb(32, 32, 32);
    color: rgb(210, 210, 210);
    font-size: 12px;
    padding: 4px 6px;
    border-radius: 3px;
}

.object-name-field > .unity-base-text-field__input:focus {
    border-color: rgb(58, 121, 187);
}

.object-icon-button {
    background-color: transparent;
    border-width: 1px;
    border-color: rgb(96, 96, 96);
    width: 24px;
    height: 24px;
    padding: 2px;
    border-radius: 3px;
}

.object-icon-button:hover {
    background-color: rgba(96, 96, 96, 0.4);
}

.object-tags {
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
}

.tag-label,
.layer-label {
    color: rgb(190, 190, 190);
    font-size: 10px;
    margin-right: 4px;
    min-width: 30px;
}

.tag-dropdown,
.layer-dropdown {
    flex-grow: 1;
    margin-right: 8px;
    min-width: 80px;
}

.tag-dropdown > .unity-base-popup-field__input,
.layer-dropdown > .unity-base-popup-field__input {
    background-color: rgb(42, 42, 42);
    border-color: rgb(32, 32, 32);
    color: rgb(210, 210, 210);
    font-size: 10px;
    padding: 2px 4px;
}

/* 主内容区域 */
.panel-content {
    flex-grow: 1;
    position: relative;
}

.properties-scroll {
    flex-grow: 1;
}

.properties-container {
    padding: 8px;
}

/* 属性组 */
.property-group {
    margin-bottom: 8px;
    background-color: rgba(52, 52, 52, 0.9);
    border-radius: 4px;
    overflow: hidden;
}

.property-group > .unity-foldout__toggle {
    background-color: rgba(64, 64, 64, 0.9);
    color: rgb(210, 210, 210);
    padding: 6px 8px;
    margin: 0;
    border-radius: 0;
    font-size: 11px;
    -unity-font-style: bold;
}

.property-group > .unity-foldout__toggle:hover {
    background-color: rgba(80, 80, 80, 0.9);
}

.property-group > .unity-foldout__content {
    padding: 0;
}

.property-section {
    padding: 8px;
}

/* 属性字段 */
.property-field {
    margin: 4px 0;
    color: rgb(210, 210, 210);
}

.property-field > .unity-base-field__label {
    color: rgb(190, 190, 190);
    min-width: 60px;
    font-size: 11px;
}

/* 输入字段样式 */
.unity-base-text-field__input,
.unity-float-field__input,
.unity-integer-field__input {
    background-color: rgb(42, 42, 42);
    border-color: rgb(32, 32, 32);
    color: rgb(210, 210, 210);
    font-size: 11px;
    padding: 2px 4px;
}

.unity-base-text-field__input:focus,
.unity-float-field__input:focus,
.unity-integer-field__input:focus {
    border-color: rgb(58, 121, 187);
}

/* 滑块样式 */
.unity-slider__tracker {
    background-color: rgb(32, 32, 32);
    height: 4px;
}

.unity-slider__dragger {
    background-color: rgb(210, 210, 210);
    width: 12px;
    height: 12px;
    border-radius: 6px;
}

.unity-slider__dragger:hover {
    background-color: rgb(58, 121, 187);
}

/* 颜色字段样式 */
.unity-color-field__input {
    border-color: rgb(32, 32, 32);
    border-radius: 3px;
}

/* 对象字段样式 */
.unity-object-field__selector {
    background-color: rgb(64, 64, 64);
    border-color: rgb(32, 32, 32);
    color: rgb(210, 210, 210);
}

.unity-object-field__display {
    background-color: rgb(42, 42, 42);
    border-color: rgb(32, 32, 32);
    color: rgb(210, 210, 210);
}

/* 切换开关样式 */
.unity-toggle__checkmark {
    background-color: rgb(32, 32, 32);
    border-color: rgb(64, 64, 64);
    border-radius: 2px;
}

.unity-toggle:checked > .unity-toggle__checkmark {
    background-color: rgb(58, 121, 187);
    border-color: rgb(58, 121, 187);
}

/* 空状态 */
.empty-state {
    position: absolute;
    top: 50%;
    left: 50%;
    translate: -50% -50%;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    display: none;
}

.empty-icon {
    width: 48px;
    height: 48px;
    background-color: rgb(128, 128, 128);
    margin-bottom: 12px;
    opacity: 0.5;
}

.empty-message {
    color: rgb(160, 160, 160);
    font-size: 14px;
    -unity-font-style: bold;
    margin-bottom: 4px;
}

.empty-hint {
    color: rgb(128, 128, 128);
    font-size: 11px;
    -unity-text-align: middle-center;

    max-width: 200px;
}

/* 组件控制栏 */
.component-controls {
    flex-direction: row;
    background-color: rgba(56, 56, 56, 0.9);
    border-top-width: 1px;
    border-top-color: rgb(32, 32, 32);
    padding: 6px 8px;
    align-items: center;
}

.add-component-button {
    background-color: rgba(76, 175, 80, 0.2);
    border-width: 1px;
    border-color: rgba(76, 175, 80, 0.5);
    color: rgb(210, 210, 210);
    padding: 4px 12px;
    border-radius: 3px;
    font-size: 11px;
    flex-grow: 1;
    margin-right: 4px;
}

.add-component-button:hover {
    background-color: rgba(76, 175, 80, 0.4);
    border-color: rgb(76, 175, 80);
}

.component-menu-button {
    background-color: transparent;
    border-width: 1px;
    border-color: rgb(96, 96, 96);
    width: 24px;
    height: 24px;
    padding: 2px;
    border-radius: 3px;
}

.component-menu-button:hover {
    background-color: rgba(96, 96, 96, 0.4);
}

/* 调整大小手柄 */
.resize-handle {
    position: absolute;
    left: -2px;
    top: 0;
    bottom: 0;
    width: 6px;
    background-color: transparent;
    cursor: resize-horizontal;
}

.resize-handle:hover {
    background-color: rgba(58, 121, 187, 0.4);
    border-left-width: 2px;
    border-left-color: rgba(58, 121, 187, 0.8);
}

.resize-handle:active {
    background-color: rgba(58, 121, 187, 0.6);
}

/* 组件下拉菜单 */
.component-dropdown {
    position: absolute;
    bottom: 40px;
    right: 8px;
    background-color: rgb(56, 56, 56);
    border-width: 1px;
    border-color: rgb(32, 32, 32);
    border-radius: 4px;
    padding: 4px 0;
    min-width: 200px;
    max-height: 300px;
    display: none;
}

.component-category {
    padding: 4px 0;
}

.category-title {
    color: rgb(160, 160, 160);
    font-size: 10px;
    -unity-font-style: bold;
    padding: 4px 12px;
    background-color: rgba(64, 64, 64, 0.5);
}

.component-item {
    background-color: transparent;
    border-width: 0;
    color: rgb(210, 210, 210);
    padding: 4px 16px;
    margin: 0;
    -unity-text-align: middle-left;
    font-size: 11px;
    border-radius: 0;
    width: 100%;
}

.component-item:hover {
    background-color: rgba(96, 96, 96, 0.8);
}

.component-item:active {
    background-color: rgba(58, 121, 187, 0.6);
}

.component-separator {
    height: 1px;
    background-color: rgb(96, 96, 96);
    margin: 2px 8px;
}

/* 折叠状态 */
.right-panel.collapsed {
    width: 32px;
    min-width: 32px;
}

.right-panel.collapsed .panel-title,
.right-panel.collapsed .object-info,
.right-panel.collapsed .panel-content,
.right-panel.collapsed .component-controls {
    display: none;
}

.right-panel.collapsed .panel-header {
    justify-content: center;
    padding: 6px 4px;
}

.right-panel.collapsed .header-controls {
    flex-direction: column;
}

/* 锁定状态 */
.right-panel.locked .object-info {
    opacity: 0.7;
}

.right-panel.locked .object-name-field {
}

/* 拖拽状态 */
.right-panel.resizing {
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .right-panel {
        width: 250px;
        min-width: 180px;
    }
}

@media (max-width: 768px) {
    .right-panel {
        width: 200px;
        min-width: 150px;
    }
    
    .object-tags {
        flex-direction: column;
        align-items: stretch;
    }
    
    .tag-dropdown,
    .layer-dropdown {
        margin-bottom: 4px;
        margin-right: 0;
    }
}
