# 鼠标旋转方向调整说明

## 功能概述

根据用户需求，调整了鼠标中键旋转时的上下移动方向，使其更符合直觉操作习惯。现在向上移动鼠标时相机会向上看，向下移动鼠标时相机会向下看。

## 修改内容

### 1. 旋转方向调整

**修改位置**：`InputManager.ViewportControl.cs` - `HandleRotation` 方法

**修改前**：
```csharp
float rotationX = -mouseDelta.y * rotationSensitivity * 0.1f; // 负号导致方向反转
```

**修改后**：
```csharp
// 根据配置决定垂直旋转方向
float verticalMultiplier = invertVerticalRotation ? -1f : 1f;
float rotationX = mouseDelta.y * verticalMultiplier * rotationSensitivity * 0.1f;
```

### 2. 新增配置参数

**添加位置**：`InputManager.Core.cs`

**新增参数**：
```csharp
public bool invertVerticalRotation = false; // 是否反转垂直旋转方向（true=向上移动鼠标向下看）
```

## 旋转行为对比

### 修改前（原始行为）

| 鼠标移动方向 | 相机视角变化 | 用户感受 |
|-------------|-------------|----------|
| 向上移动 | 向下看 | 反直觉 |
| 向下移动 | 向上看 | 反直觉 |
| 向左移动 | 向左转 | 正常 |
| 向右移动 | 向右转 | 正常 |

### 修改后（当前默认行为）

| 鼠标移动方向 | 相机视角变化 | 用户感受 |
|-------------|-------------|----------|
| 向上移动 | 向上看 | 直觉 |
| 向下移动 | 向下看 | 直觉 |
| 向左移动 | 向左转 | 正常 |
| 向右移动 | 向右转 | 正常 |

## 配置选项

### Inspector 设置

在 InputManager 组件的 "旋转设置" 部分：

- **Invert Vertical Rotation**：勾选此项可以恢复到原始的反转行为
  - `false`（默认）：向上移动鼠标→向上看（直觉模式）
  - `true`：向上移动鼠标→向下看（传统模式）

### 代码控制

```csharp
// 设置为直觉模式（默认）
inputManager.invertVerticalRotation = false;

// 设置为传统模式
inputManager.invertVerticalRotation = true;

// 切换模式
inputManager.invertVerticalRotation = !inputManager.invertVerticalRotation;
```

## 使用场景

### 推荐设置

1. **新用户**：建议使用默认设置（`invertVerticalRotation = false`）
   - 更符合直觉操作习惯
   - 与大多数现代3D应用一致

2. **习惯传统操作的用户**：可以启用反转（`invertVerticalRotation = true`）
   - 保持与旧版本一致的操作感受
   - 适合习惯了反转操作的用户

3. **不同应用场景**：
   - **游戏类应用**：建议使用直觉模式
   - **CAD/建筑类应用**：根据行业习惯选择
   - **地图浏览应用**：建议使用直觉模式

## 测试工具

### RotationDirectionTester

专门用于测试旋转方向的测试组件：

**功能特性**：
- **快捷键控制**：F8键快速切换旋转方向
- **实时监控**：显示当前旋转状态和相机角度
- **GUI控制面板**：可视化控制和调节参数
- **旋转检测**：监控并记录旋转行为

**使用方法**：
1. 在场景中添加 `RotationDirectionTester` 组件
2. 使用鼠标中键进行旋转测试
3. 按F8键切换旋转方向
4. 观察控制台输出和GUI显示

## 技术实现

### 核心逻辑

```csharp
private void HandleRotation(Vector2 mouseDelta)
{
    // 根据配置决定垂直旋转方向
    float verticalMultiplier = invertVerticalRotation ? -1f : 1f;
    float rotationX = mouseDelta.y * verticalMultiplier * rotationSensitivity * 0.1f;
    float rotationY = mouseDelta.x * rotationSensitivity * 0.1f;
    
    // 应用旋转...
}
```

### 参数说明

- `mouseDelta.y`：鼠标垂直移动量（正值=向上移动）
- `verticalMultiplier`：方向乘数（1=正常，-1=反转）
- `rotationSensitivity`：旋转敏感度
- `rotationX`：最终的垂直旋转量

## 向后兼容性

### 兼容性保证

- ✅ 保持所有现有功能不变
- ✅ 新增配置参数，默认为改进后的行为
- ✅ 可以通过参数恢复到原始行为
- ✅ 不影响水平旋转逻辑

### 迁移指南

**对于现有项目**：
1. 如果用户习惯了原始的反转行为，可以设置 `invertVerticalRotation = true`
2. 新项目建议使用默认设置（`invertVerticalRotation = false`）
3. 可以在运行时动态切换，让用户自己选择偏好

## 常见问题

### Q: 为什么要改变旋转方向？
A: 原始的反转行为（向上移动鼠标向下看）对大多数用户来说是反直觉的，新的行为更符合现代3D应用的标准。

### Q: 如何恢复到原来的行为？
A: 在Inspector中勾选 "Invert Vertical Rotation" 或在代码中设置 `invertVerticalRotation = true`。

### Q: 这个改动会影响性能吗？
A: 不会，只是简单的数学运算，性能影响可以忽略不计。

### Q: 水平旋转有变化吗？
A: 没有，水平旋转（左右移动鼠标）的行为保持不变。

### Q: 可以在运行时切换吗？
A: 可以，通过修改 `invertVerticalRotation` 参数即可实时切换。

## 总结

这次调整主要解决了鼠标中键旋转时垂直方向反直觉的问题：

✅ **改进用户体验**：向上移动鼠标现在会向上看，更符合直觉
✅ **保持兼容性**：通过配置参数可以恢复原始行为
✅ **提供测试工具**：便于验证和调试旋转行为
✅ **灵活配置**：支持运行时动态切换

这个改动让相机控制更加用户友好，特别是对于新用户来说，学习成本更低，操作更直观。
