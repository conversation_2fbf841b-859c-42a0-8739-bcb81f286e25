﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>SQLitePCLRaw.provider.e_sqlite3</id>
    <version>2.1.11</version>
    <authors><PERSON></authors>
    <license type="expression">Apache-2.0</license>
    <licenseUrl>https://licenses.nuget.org/Apache-2.0</licenseUrl>
    <description>SQLitePCLRaw is a Portable Class Library (PCL) for low-level (raw) access to SQLite.  Packages named 'SQLitePCLRaw.provider.*' (like this one) are 'plugins' that allow SQLitePCLRaw.core to access the native SQLite library.  This provider does DllImport of 'e_sqlite3', the SQLite builds provided with SQLitePCLRaw.</description>
    <copyright>Copyright 2014-2024 SourceGear, LLC</copyright>
    <tags>sqlite</tags>
    <repository type="git" url="https://github.com/ericsink/SQLitePCL.raw" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="SQLitePCLRaw.core" version="2.1.11" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0-windows7.0">
        <dependency id="SQLitePCLRaw.core" version="2.1.11" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="SQLitePCLRaw.core" version="2.1.11" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>