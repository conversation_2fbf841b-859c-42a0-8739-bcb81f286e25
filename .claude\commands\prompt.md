# 🎯 prompt - 提示词合成器

你将通过结合以下内容创建一个**完整的、可直接复制的提示**：
1. 来自 OVERVIEW.md 的项目概览。
2. 此处提供的具体任务详情。

### 📋 你的任务：

1. **读取** OVERVIEW.md 的项目概览。
2. **提取** 核心提示结构和要求
3. **检查** 检查关键的项目文件是否如预期
4. **整合** 用户意图无缝融入提示中
5. **输出** 可轻松复制的代码块格式的完整提示

### 🎨 输出格式：

以 markdown 代码块形式展示合成的提示，如下所示：

```
[结合 OVERVIEW.md 指令和用户具体任务的完整合成提示]
```

### ⚡ 合成规则：

1. **保持结构** - 维护 OVERVIEW.md 的工作流程、检查点和要求
3. **上下文感知** - 如果用户参数涉及特定技术，强调相关部分
4. **完整独立** - 输出应在粘贴到新的 Claude 对话中时完美工作
5. **无评论** - 不要解释你在做什么，只输出合成的提示

### 🔧 增强指南：

- 如果任务涉及特定语言（C#、Uxml、Uss、Typescript、Python、Shell 等），强调相关语言特定规则
- 如果任务看起来复杂，确保"元认知"和"多智能体"部分突出
- 如果任务涉及重构，突出"删除旧代码"要求
- 无论任务如何，保留所有关键要求（hooks、linting、测试）

### 📦 示例行为：

如果用户提供："实现带 JWT 认证的用户管理 REST API"

你将：
1. 读取 OVERVIEW.md
2. 强调相关部分（API 设计、安全性、测试）
3. 检查关键的项目文件是否如预期
4. 输出完整的、整合的提示