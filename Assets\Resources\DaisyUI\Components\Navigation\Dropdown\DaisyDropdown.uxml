<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <ui:Template name="DaisyDropdown" src="project://database/Assets/Resources/DaisyUI/Components/Navigation/Dropdown/DaisyDropdown.uxml">
        <Style src="project://database/Assets/Resources/DaisyUI/Components/Navigation/Dropdown/DaisyDropdown.uss" />
        <ui:VisualElement name="daisy-dropdown" class="daisy-dropdown">
            <ui:Button text="Dropdown" name="dropdown-trigger" class="dropdown-trigger" />
            <ui:VisualElement name="dropdown-content" class="dropdown-content">
                <ui:Label text="Dropdown item 1" class="dropdown-item" />
                <ui:Label text="Dropdown item 2" class="dropdown-item" />
            </ui:VisualElement>
        </ui:VisualElement>
    </ui:Template>
</ui:UXML>