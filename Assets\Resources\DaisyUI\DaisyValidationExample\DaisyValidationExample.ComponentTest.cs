using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Core;
using BlastingDesign.UI.DaisyUI.Components.Actions;
using BlastingDesign.UI.Extensions;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI
{
    /// <summary>
    /// DaisyUI验证示例 - DaisyComponent基类测试
    /// 测试DaisyComponent基类的核心功能
    /// </summary>
    public partial class DaisyValidationExample : MonoBehaviour
    {
        #region DaisyComponent基类测试

        /// <summary>
        /// 测试DaisyComponent基类
        /// </summary>
        private void TestDaisyComponent()
        {
            LogTest("DaisyComponent 基类测试");

            try
            {
                // 创建测试按钮来验证基类功能
                var testButton = new DaisyButton("基类测试");

                // 测试基础属性
                if (string.IsNullOrEmpty(testButton.ComponentType))
                {
                    throw new System.Exception("ComponentType 未正确设置");
                }

                // 测试尺寸设置
                testButton.WithSize("lg");
                if (testButton.Size != "lg")
                {
                    throw new System.Exception("尺寸设置失败");
                }

                // 测试变体设置
                testButton.WithVariant("primary");
                if (testButton.Variant != "primary")
                {
                    throw new System.Exception("变体设置失败");
                }

                // 测试修饰符
                testButton.SetModifier("outline", true);
                if (!testButton.ClassListContains("daisy-btn-outline"))
                {
                    throw new System.Exception("修饰符设置失败");
                }

                // 测试链式调用
                var chainedButton = testButton
                    .AddClass("test-class")
                    .SetVisible(true)
                    .SetDisabled(false);

                if (chainedButton != testButton)
                {
                    throw new System.Exception("链式调用失败");
                }

                // 测试主题应用
                if (testTheme != null)
                {
                    testButton.WithTheme(testTheme);
                }

                // 测试组件状态管理
                testButton.SetModifier("loading", true);
                if (!testButton.ClassListContains("daisy-btn-loading"))
                {
                    throw new System.Exception("状态修饰符设置失败");
                }

                testButton.SetModifier("loading", false);
                if (testButton.ClassListContains("daisy-btn-loading"))
                {
                    throw new System.Exception("状态修饰符移除失败");
                }

                // 测试CSS类管理
                testButton.AddClass("custom-class");
                if (!testButton.ClassListContains("custom-class"))
                {
                    throw new System.Exception("自定义类添加失败");
                }

                testButton.RemoveClass("custom-class");
                if (testButton.ClassListContains("custom-class"))
                {
                    throw new System.Exception("自定义类移除失败");
                }

                // 测试条件类设置
                testButton.WithConditionalClass(true, "conditional-true");
                if (!testButton.ClassListContains("conditional-true"))
                {
                    throw new System.Exception("条件类设置失败");
                }

                testButton.WithConditionalClass(false, "conditional-false");
                if (testButton.ClassListContains("conditional-false"))
                {
                    throw new System.Exception("条件类设置失败");
                }

                // 测试可见性控制
                testButton.SetVisible(false);
                if (testButton.style.display != DisplayStyle.None)
                {
                    throw new System.Exception("可见性控制失败");
                }

                testButton.SetVisible(true);
                if (testButton.style.display == DisplayStyle.None)
                {
                    throw new System.Exception("可见性恢复失败");
                }

                // 测试禁用状态
                testButton.SetDisabled(true);
                if (!testButton.ClassListContains("daisy-btn-disabled"))
                {
                    throw new System.Exception("禁用状态设置失败");
                }

                testButton.SetDisabled(false);
                if (testButton.ClassListContains("daisy-btn-disabled"))
                {
                    throw new System.Exception("禁用状态恢复失败");
                }

                // 添加到UI中进行视觉验证
                var componentContainer = new VisualElement();
                componentContainer.AddToClassList("daisy-component-test");
                componentContainer.style.paddingTop = 20;
                componentContainer.style.paddingBottom = 20;
                componentContainer.style.paddingLeft = 20;
                componentContainer.style.paddingRight = 20;
                componentContainer.style.marginBottom = 20;
                componentContainer.style.backgroundColor = new Color(0.98f, 0.98f, 0.98f, 1f);
                componentContainer.style.borderTopLeftRadius = 8;
                componentContainer.style.borderTopRightRadius = 8;
                componentContainer.style.borderBottomLeftRadius = 8;
                componentContainer.style.borderBottomRightRadius = 8;

                var heading = new Label("DaisyComponent 基类测试");
                heading.style.fontSize = 18;
                heading.style.unityFontStyleAndWeight = FontStyle.Bold;
                heading.style.marginBottom = 10;
                heading.style.color = Color.black;
                componentContainer.Add(heading);

                var description = new Label("测试基类的核心功能：尺寸、变体、修饰符、链式调用、状态管理等");
                description.style.fontSize = 12;
                description.style.marginBottom = 15;
                description.style.color = new Color(0.5f, 0.5f, 0.5f, 1f);
                componentContainer.Add(description);

                // 创建测试按钮展示区
                var buttonRow = new VisualElement();
                buttonRow.style.flexDirection = FlexDirection.Row;
                buttonRow.style.flexWrap = Wrap.Wrap;
                buttonRow.AddToClassList("daisy-gap-2");

                // 重新创建干净的测试按钮用于展示
                var displayButton1 = new DaisyButton("基础按钮");
                var displayButton2 = new DaisyButton("大尺寸").WithSize("lg");
                var displayButton3 = new DaisyButton("主要变体").WithVariant("primary");
                var displayButton4 = new DaisyButton("轮廓修饰").SetModifier("outline", true);
                var displayButton5 = new DaisyButton("禁用状态").SetDisabled(true);

                buttonRow.Add(displayButton1);
                buttonRow.Add(displayButton2);
                buttonRow.Add(displayButton3);
                buttonRow.Add(displayButton4);
                buttonRow.Add(displayButton5);

                componentContainer.Add(buttonRow);

                // 添加功能说明
                var featureList = new VisualElement();
                featureList.style.marginTop = 15;
                featureList.style.paddingTop = 10;
                featureList.style.borderTopWidth = 1;
                featureList.style.borderTopColor = new Color(0.9f, 0.9f, 0.9f, 1f);

                var featureTitle = new Label("已验证功能：");
                featureTitle.style.fontSize = 14;
                featureTitle.style.unityFontStyleAndWeight = FontStyle.Bold;
                featureTitle.style.marginBottom = 5;
                featureList.Add(featureTitle);

                var features = new string[]
                {
                    "✓ 组件类型识别",
                    "✓ 尺寸设置 (WithSize)",
                    "✓ 变体设置 (WithVariant)",
                    "✓ 修饰符管理 (SetModifier)",
                    "✓ 链式调用支持",
                    "✓ 主题应用 (WithTheme)",
                    "✓ CSS类管理",
                    "✓ 条件类设置",
                    "✓ 可见性控制",
                    "✓ 禁用状态管理"
                };

                foreach (var feature in features)
                {
                    var featureLabel = new Label(feature);
                    featureLabel.style.fontSize = 11;
                    featureLabel.style.marginBottom = 2;
                    featureLabel.style.color = new Color(0.3f, 0.6f, 0.3f, 1f);
                    featureList.Add(featureLabel);
                }

                componentContainer.Add(featureList);
                root.Add(componentContainer);

                LogTestPass("DaisyComponent 基类测试通过");
            }
            catch (System.Exception ex)
            {
                LogTestFail($"DaisyComponent 基类测试失败: {ex.Message}");
            }
        }

        #endregion
    }
}
