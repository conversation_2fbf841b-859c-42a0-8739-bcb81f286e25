using UnityEngine;
using System.Collections.Generic;

namespace BlastingDesign.UI.DaisyUI.Core
{
    /// <summary>
    /// DaisyUI工具类
    /// 提供通用的工具方法和常量定义
    /// </summary>
    public static class DaisyUtilities
    {
        #region 常量定义

        /// <summary>
        /// 组件尺寸常量
        /// </summary>
        public static class Sizes
        {
            public const string ExtraSmall = "xs";
            public const string Small = "sm";
            public const string Medium = "md";
            public const string Large = "lg";
            public const string ExtraLarge = "xl";
        }

        /// <summary>
        /// 组件变体常量
        /// </summary>
        public static class Variants
        {
            public const string Default = "default";
            public const string Primary = "primary";
            public const string Secondary = "secondary";
            public const string Accent = "accent";
            public const string Neutral = "neutral";
            public const string Ghost = "ghost";
            public const string Link = "link";
            public const string Info = "info";
            public const string Success = "success";
            public const string Warning = "warning";
            public const string Error = "error";
        }

        /// <summary>
        /// 组件修饰符常量
        /// </summary>
        public static class Modifiers
        {
            public const string Outline = "outline";
            public const string Wide = "wide";
            public const string Block = "block";
            public const string Circle = "circle";
            public const string Square = "square";
            public const string Loading = "loading";
            public const string Disabled = "disabled";
            public const string Active = "active";
            public const string Focus = "focus";
            public const string Bordered = "bordered";
            public const string Glass = "glass";
            public const string Compact = "compact";
        }

        /// <summary>
        /// 组件类型常量
        /// </summary>
        public static class ComponentTypes
        {
            // Actions
            public const string Button = "btn";
            public const string Dropdown = "dropdown";
            public const string Modal = "modal";
            public const string Swap = "swap";

            // Data Display
            public const string Card = "card";
            public const string Badge = "badge";
            public const string Avatar = "avatar";
            public const string Accordion = "accordion";
            public const string Carousel = "carousel";
            public const string Chat = "chat";
            public const string Collapse = "collapse";
            public const string Countdown = "countdown";
            public const string Stat = "stat";
            public const string Timeline = "timeline";

            // Navigation
            public const string Breadcrumb = "breadcrumb";
            public const string Menu = "menu";
            public const string Navbar = "navbar";
            public const string Pagination = "pagination";
            public const string Steps = "steps";
            public const string Tabs = "tabs";

            // Data Input
            public const string Input = "input";
            public const string Textarea = "textarea";
            public const string Select = "select";
            public const string Checkbox = "checkbox";
            public const string Radio = "radio";
            public const string Range = "range";
            public const string Rating = "rating";
            public const string Toggle = "toggle";
            public const string FileInput = "file-input";

            // Layout
            public const string Container = "container";
            public const string Divider = "divider";
            public const string Drawer = "drawer";
            public const string Footer = "footer";
            public const string Hero = "hero";
            public const string Indicator = "indicator";
            public const string Join = "join";
            public const string Stack = "stack";
            public const string Tree = "tree";
            public const string TreeItem = "tree-item";
            public const string TreeNode = "tree-node";
        }

        #endregion

        #region 配置属性

        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        public static bool IsDebugMode
        {
            get { return Debug.isDebugBuild || Application.isEditor; }
        }

        /// <summary>
        /// 默认动画持续时间（毫秒）
        /// </summary>
        public static int DefaultAnimationDuration = 200;

        /// <summary>
        /// 默认缓动函数
        /// </summary>
        public static string DefaultEasing = "ease-in-out";

        #endregion

        #region 工具方法

        /// <summary>
        /// 生成组件基础类名
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <returns>基础类名</returns>
        public static string GetBaseClassName(string componentType)
        {
            return $"daisy-{componentType}";
        }

        /// <summary>
        /// 生成组件尺寸类名
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <param name="size">尺寸</param>
        /// <returns>尺寸类名</returns>
        public static string GetSizeClassName(string componentType, string size)
        {
            return $"daisy-{componentType}-{size}";
        }

        /// <summary>
        /// 生成组件变体类名
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <param name="variant">变体</param>
        /// <returns>变体类名</returns>
        public static string GetVariantClassName(string componentType, string variant)
        {
            return $"daisy-{componentType}-{variant}";
        }

        /// <summary>
        /// 生成组件修饰符类名
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <param name="modifier">修饰符</param>
        /// <returns>修饰符类名</returns>
        public static string GetModifierClassName(string componentType, string modifier)
        {
            return $"daisy-{componentType}-{modifier}";
        }

        /// <summary>
        /// 验证组件类型
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <returns>是否有效</returns>
        public static bool IsValidComponentType(string componentType)
        {
            return !string.IsNullOrWhiteSpace(componentType) &&
                   componentType.Length > 0 &&
                   !componentType.Contains(" ");
        }

        /// <summary>
        /// 验证尺寸
        /// </summary>
        /// <param name="size">尺寸</param>
        /// <returns>是否有效</returns>
        public static bool IsValidSize(string size)
        {
            return size == Sizes.ExtraSmall ||
                   size == Sizes.Small ||
                   size == Sizes.Medium ||
                   size == Sizes.Large ||
                   size == Sizes.ExtraLarge;
        }

        /// <summary>
        /// 验证变体
        /// </summary>
        /// <param name="variant">变体</param>
        /// <returns>是否有效</returns>
        public static bool IsValidVariant(string variant)
        {
            return variant == Variants.Default ||
                   variant == Variants.Primary ||
                   variant == Variants.Secondary ||
                   variant == Variants.Accent ||
                   variant == Variants.Neutral ||
                   variant == Variants.Ghost ||
                   variant == Variants.Link ||
                   variant == Variants.Info ||
                   variant == Variants.Success ||
                   variant == Variants.Warning ||
                   variant == Variants.Error;
        }

        /// <summary>
        /// 获取所有可用的尺寸
        /// </summary>
        /// <returns>尺寸列表</returns>
        public static string[] GetAllSizes()
        {
            return new string[]
            {
                Sizes.ExtraSmall,
                Sizes.Small,
                Sizes.Medium,
                Sizes.Large,
                Sizes.ExtraLarge
            };
        }

        /// <summary>
        /// 获取所有可用的变体
        /// </summary>
        /// <returns>变体列表</returns>
        public static string[] GetAllVariants()
        {
            return new string[]
            {
                Variants.Default,
                Variants.Primary,
                Variants.Secondary,
                Variants.Accent,
                Variants.Neutral,
                Variants.Ghost,
                Variants.Link,
                Variants.Info,
                Variants.Success,
                Variants.Warning,
                Variants.Error
            };
        }

        /// <summary>
        /// 将颜色转换为十六进制字符串
        /// </summary>
        /// <param name="color">颜色</param>
        /// <returns>十六进制字符串</returns>
        public static string ColorToHex(Color color)
        {
            return $"#{ColorUtility.ToHtmlStringRGBA(color)}";
        }

        /// <summary>
        /// 将十六进制字符串转换为颜色
        /// </summary>
        /// <param name="hex">十六进制字符串</param>
        /// <returns>颜色</returns>
        public static Color HexToColor(string hex)
        {
            if (ColorUtility.TryParseHtmlString(hex, out Color color))
            {
                return color;
            }
            return Color.white;
        }

        /// <summary>
        /// 计算对比色（用于文本颜色）
        /// </summary>
        /// <param name="backgroundColor">背景色</param>
        /// <returns>对比色</returns>
        public static Color GetContrastColor(Color backgroundColor)
        {
            // 计算亮度
            float luminance = 0.299f * backgroundColor.r + 0.587f * backgroundColor.g + 0.114f * backgroundColor.b;

            // 根据亮度返回黑色或白色
            return luminance > 0.5f ? Color.black : Color.white;
        }

        /// <summary>
        /// 生成组件ID
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <returns>唯一ID</returns>
        public static string GenerateComponentId(string componentType)
        {
            return $"daisy-{componentType}-{System.Guid.NewGuid().ToString("N")[..8]}";
        }

        /// <summary>
        /// 解析CSS单位值
        /// </summary>
        /// <param name="value">CSS值（如 "16px", "1em", "50%"）</param>
        /// <param name="unit">单位</param>
        /// <returns>数值部分</returns>
        public static float ParseCSSValue(string value, out string unit)
        {
            unit = "px";

            if (string.IsNullOrEmpty(value))
                return 0f;

            // 提取数字部分
            string numberPart = "";
            string unitPart = "";

            for (int i = 0; i < value.Length; i++)
            {
                if (char.IsDigit(value[i]) || value[i] == '.' || value[i] == '-')
                {
                    numberPart += value[i];
                }
                else
                {
                    unitPart = value.Substring(i);
                    break;
                }
            }

            if (!string.IsNullOrEmpty(unitPart))
                unit = unitPart;

            if (float.TryParse(numberPart, out float result))
                return result;

            return 0f;
        }

        #endregion

        #region 性能优化

        private static Dictionary<string, string> _classNameCache = new Dictionary<string, string>();

        /// <summary>
        /// 获取缓存的类名（性能优化）
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="generator">类名生成器</param>
        /// <returns>类名</returns>
        public static string GetCachedClassName(string key, System.Func<string> generator)
        {
            if (!_classNameCache.ContainsKey(key))
            {
                _classNameCache[key] = generator();
            }
            return _classNameCache[key];
        }

        /// <summary>
        /// 清理类名缓存
        /// </summary>
        public static void ClearClassNameCache()
        {
            _classNameCache.Clear();
        }

        #endregion
    }
}