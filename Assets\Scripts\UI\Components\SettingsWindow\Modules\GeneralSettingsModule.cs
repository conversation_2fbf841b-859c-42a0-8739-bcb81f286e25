using UnityEngine;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 常规设置模块
    /// </summary>
    public class GeneralSettingsModule : SettingsModule
    {
        #region 属性

        public override string ModuleId => "general";
        public override string DisplayName => "⚙ 常规";
        public override string Description => "基本应用程序设置，包括声音、语言、字体等选项";
        public override int Priority => 0;

        #endregion

        #region 私有字段

        private Toggle enableSoundToggle;
        private Toggle autoSaveToggle;
        private Toggle enableNotificationsToggle;
        private DropdownField languageDropdown;
        private SliderInt fontSizeSlider;
        private Label fontSizeLabel;

        #endregion

        #region 实现抽象方法

        public override VisualElement CreateUI()
        {
            var root = new VisualElement();
            root.AddToClassList("settings-module");

            // 标题
            var title = new Label("常规设置");
            title.AddToClassList("module-title");
            root.Add(title);

            // 声音设置
            var soundContainer = new VisualElement();
            soundContainer.AddToClassList("setting-item");
            
            var soundLabel = new Label("启用声音效果");
            soundLabel.AddToClassList("setting-label");
            soundContainer.Add(soundLabel);
            
            enableSoundToggle = new Toggle();
            enableSoundToggle.AddToClassList("setting-toggle");
            soundContainer.Add(enableSoundToggle);
            
            root.Add(soundContainer);

            // 自动保存设置
            var autoSaveContainer = new VisualElement();
            autoSaveContainer.AddToClassList("setting-item");
            
            var autoSaveLabel = new Label("自动保存");
            autoSaveLabel.AddToClassList("setting-label");
            autoSaveContainer.Add(autoSaveLabel);
            
            autoSaveToggle = new Toggle();
            autoSaveToggle.AddToClassList("setting-toggle");
            autoSaveContainer.Add(autoSaveToggle);
            
            root.Add(autoSaveContainer);

            // 通知设置
            var notificationContainer = new VisualElement();
            notificationContainer.AddToClassList("setting-item");
            
            var notificationLabel = new Label("启用通知");
            notificationLabel.AddToClassList("setting-label");
            notificationContainer.Add(notificationLabel);
            
            enableNotificationsToggle = new Toggle();
            enableNotificationsToggle.AddToClassList("setting-toggle");
            notificationContainer.Add(enableNotificationsToggle);
            
            root.Add(notificationContainer);

            // 语言设置
            var languageContainer = new VisualElement();
            languageContainer.AddToClassList("setting-item");
            
            var languageLabel = new Label("语言");
            languageLabel.AddToClassList("setting-label");
            languageContainer.Add(languageLabel);
            
            languageDropdown = new DropdownField();
            languageDropdown.AddToClassList("setting-dropdown");
            languageDropdown.choices = new System.Collections.Generic.List<string> { "中文", "English" };
            languageContainer.Add(languageDropdown);
            
            root.Add(languageContainer);

            // 字体大小设置
            var fontSizeContainer = new VisualElement();
            fontSizeContainer.AddToClassList("setting-item");
            
            var fontSizeMainLabel = new Label("字体大小");
            fontSizeMainLabel.AddToClassList("setting-label");
            fontSizeContainer.Add(fontSizeMainLabel);
            
            var fontSizeSubContainer = new VisualElement();
            fontSizeSubContainer.AddToClassList("setting-sub-container");
            
            fontSizeSlider = new SliderInt(8, 32);
            fontSizeSlider.AddToClassList("setting-slider");
            fontSizeSubContainer.Add(fontSizeSlider);
            
            fontSizeLabel = new Label("14");
            fontSizeLabel.AddToClassList("setting-value-label");
            fontSizeSubContainer.Add(fontSizeLabel);
            
            fontSizeContainer.Add(fontSizeSubContainer);
            root.Add(fontSizeContainer);

            // 绑定事件
            fontSizeSlider.RegisterValueChangedCallback(evt =>
            {
                fontSizeLabel.text = evt.newValue.ToString();
            });

            return root;
        }

        public override void LoadSettings(SettingsData settingsData)
        {
            if (settingsData == null) return;

            enableSoundToggle?.SetValueWithoutNotify(settingsData.EnableSound);
            autoSaveToggle?.SetValueWithoutNotify(settingsData.AutoSave);
            enableNotificationsToggle?.SetValueWithoutNotify(settingsData.EnableNotifications);
            languageDropdown?.SetValueWithoutNotify(settingsData.Language);
            fontSizeSlider?.SetValueWithoutNotify(settingsData.FontSize);
            if (fontSizeLabel != null)
                fontSizeLabel.text = settingsData.FontSize.ToString();
        }

        public override void SaveSettings(SettingsData settingsData)
        {
            if (settingsData == null) return;

            settingsData.EnableSound = enableSoundToggle?.value ?? settingsData.EnableSound;
            settingsData.AutoSave = autoSaveToggle?.value ?? settingsData.AutoSave;
            settingsData.EnableNotifications = enableNotificationsToggle?.value ?? settingsData.EnableNotifications;
            settingsData.Language = languageDropdown?.value ?? settingsData.Language;
            settingsData.FontSize = fontSizeSlider?.value ?? settingsData.FontSize;
        }

        public override void ResetToDefaults(SettingsData settingsData)
        {
            if (settingsData == null) return;

            settingsData.EnableSound = true;
            settingsData.AutoSave = true;
            settingsData.EnableNotifications = true;
            settingsData.Language = "中文";
            settingsData.FontSize = 14;

            LoadSettings(settingsData);
        }

        public override bool ValidateSettings(SettingsData settingsData, out string errorMessage)
        {
            errorMessage = "";

            if (settingsData == null)
            {
                errorMessage = "设置数据为空";
                return false;
            }

            if (settingsData.FontSize < 8 || settingsData.FontSize > 32)
            {
                errorMessage = "字体大小必须在8-32之间";
                return false;
            }

            if (string.IsNullOrEmpty(settingsData.Language))
            {
                errorMessage = "语言设置不能为空";
                return false;
            }

            return true;
        }

        #endregion
    }
}