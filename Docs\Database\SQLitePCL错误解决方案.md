# SQLitePCL 错误解决方案

## 错误描述

当使用 Microsoft.Data.Sqlite 时遇到以下错误：
```
You need to call SQLitePCL.raw.SetProvider(). If you are using a bundle package, this is done by calling SQLitePCL.Batteries.Init().
```

## 问题原因

Microsoft.Data.Sqlite 依赖于 SQLitePCL.raw 来提供底层的 SQLite 功能。这个错误表示 SQLitePCL 的提供程序没有正确初始化。

## 解决方案

### 方案一：安装 SQLitePCLRaw Bundle 包（推荐）

#### 1. 安装合适的 Bundle 包

根据您的目标平台选择合适的 bundle：

**Windows/Mac/Linux 桌面平台：**
```
SQLitePCLRaw.bundle_e_sqlite3
```

**跨平台通用：**
```
SQLitePCLRaw.bundle_green
```

**Windows 特定（使用系统 SQLite）：**
```
SQLitePCLRaw.bundle_winsqlite3
```

#### 2. 使用 NuGet for Unity 安装

1. 打开 NuGet for Unity 窗口
2. 搜索上述包名之一
3. 点击安装
4. 重新编译项目

#### 3. 手动添加到 packages.config

在项目根目录的 `packages.config` 文件中添加：

```xml
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Data.Sqlite" version="7.0.0" targetFramework="netstandard2.0" />
  <package id="SQLitePCLRaw.bundle_e_sqlite3" version="2.1.4" targetFramework="netstandard2.0" />
</packages>
```

### 方案二：手动初始化（高级用户）

如果无法安装 bundle 包，可以手动初始化：

#### 1. 修改代码

```csharp
using SQLitePCL;

// 在使用 SQLite 之前调用
private static void InitializeSQLite()
{
    try
    {
        // 尝试使用 Batteries.Init()
        Batteries.Init();
    }
    catch
    {
        // 如果 Batteries 不可用，手动设置提供程序
        raw.SetProvider(new SQLite3Provider_e_sqlite3());
    }
}
```

#### 2. 在 Start() 方法中调用

```csharp
void Start()
{
    InitializeSQLite();
    // 然后进行数据库操作
}
```

### 方案三：使用不同的 SQLite 库

如果上述方案都不可行，考虑使用其他 SQLite 库：

#### 1. sqlite-net-pcl
```
Install-Package sqlite-net-pcl
```

#### 2. System.Data.SQLite
```
Install-Package System.Data.SQLite
```

## 平台特定解决方案

### Unity Editor / Windows Standalone

**推荐包：** `SQLitePCLRaw.bundle_e_sqlite3`

```xml
<package id="SQLitePCLRaw.bundle_e_sqlite3" version="2.1.4" />
```

### Android

**推荐包：** `SQLitePCLRaw.bundle_green`

```xml
<package id="SQLitePCLRaw.bundle_green" version="2.1.4" />
```

可能还需要在 Android 设置中：
- Target API Level: 28 或更高
- Scripting Backend: IL2CPP
- Api Compatibility Level: .NET Standard 2.1

### iOS

**推荐包：** `SQLitePCLRaw.bundle_green`

```xml
<package id="SQLitePCLRaw.bundle_green" version="2.1.4" />
```

iOS 特定设置：
- Scripting Backend: IL2CPP
- 可能需要在 Xcode 中链接 SQLite 库

### WebGL

WebGL 平台对 SQLite 支持有限，建议：
- 使用 IndexedDB 替代
- 或者使用 WebAssembly 版本的 SQLite

## 验证解决方案

### 1. 创建测试脚本

```csharp
using UnityEngine;
using Microsoft.Data.Sqlite;

public class SQLiteTest : MonoBehaviour
{
    void Start()
    {
        TestSQLiteConnection();
    }
    
    void TestSQLiteConnection()
    {
        try
        {
            using var connection = new SqliteConnection("Data Source=:memory:");
            connection.Open();
            Debug.Log("SQLite 连接成功！");
            
            using var command = new SqliteCommand("SELECT sqlite_version()", connection);
            var version = command.ExecuteScalar();
            Debug.Log($"SQLite 版本: {version}");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"SQLite 连接失败: {ex.Message}");
        }
    }
}
```

### 2. 检查控制台输出

成功的输出应该显示：
```
SQLite 连接成功！
SQLite 版本: 3.x.x
```

## 常见问题排查

### 问题1：Bundle 包安装后仍然报错

**解决方案：**
1. 清理项目：删除 Library 文件夹
2. 重新导入项目
3. 检查 .NET 兼容性设置

### 问题2：特定平台构建失败

**解决方案：**
1. 检查平台特定的包依赖
2. 确认 Scripting Backend 设置
3. 查看构建日志中的具体错误

### 问题3：运行时找不到 SQLite 库

**解决方案：**
1. 确认目标平台支持 SQLite
2. 检查是否需要额外的原生库
3. 尝试不同的 bundle 包

## 推荐的完整配置

### packages.config
```xml
<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.Data.Sqlite" version="7.0.0" targetFramework="netstandard2.0" />
  <package id="SQLitePCLRaw.bundle_e_sqlite3" version="2.1.4" targetFramework="netstandard2.0" />
</packages>
```

### Unity Player Settings
- **Api Compatibility Level**: .NET Standard 2.1
- **Scripting Backend**: Mono (桌面) / IL2CPP (移动)
- **Target Framework**: .NET Standard 2.1

### 初始化代码
```csharp
using Microsoft.Data.Sqlite;
using SQLitePCL;

public class DatabaseManager : MonoBehaviour
{
    void Awake()
    {
        // 初始化 SQLitePCL（如果使用 bundle 包，这通常是自动的）
        try
        {
            Batteries.Init();
        }
        catch
        {
            // Bundle 包会自动处理初始化
        }
    }
}
```

## 总结

最简单和最可靠的解决方案是安装适当的 SQLitePCLRaw bundle 包。这些包会自动处理 SQLitePCL 的初始化，避免手动配置的复杂性。

**推荐步骤：**
1. 安装 `SQLitePCLRaw.bundle_e_sqlite3`
2. 重新编译项目
3. 测试 SQLite 连接
4. 如果仍有问题，尝试其他 bundle 包

如果您继续遇到问题，请检查 Unity 控制台的详细错误信息，并根据具体的错误消息进行相应的调整。
