/* 顶部工具栏容器 - 固定在顶部 */
.top-toolbar-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    background-color: rgba(64, 64, 64, 1);
    border-bottom-width: 1px;
    border-bottom-color: rgb(35, 35, 35);
}

/* 左侧面板容器 - 固定在左侧 */
.left-panel-container {
    position: absolute;
    top: 40px;
    left: 0;
    bottom: 30px;
    background-color: rgba(56, 56, 56, 1);
    border-right-width: 1px;
    border-right-color: rgb(35, 35, 35);
}

/* 右侧面板容器 - 固定在右侧 */
.right-panel-container {
    position: absolute;
    top: 40px;
    right: 0;
    bottom: 30px;
    background-color: rgba(56, 56, 56, 1);
    border-left-width: 1px;
    border-left-color: rgb(35, 35, 35);
}

/* 底部状态栏容器 - 固定在底部 */
.status-bar-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background-color: rgba(64, 64, 64, 1);
    border-top-width: 1px;
    border-top-color: rgb(35, 35, 35);
}

/* 浮动面板基础样式 */
.floating-panel {
    position: absolute;
    background-color: rgba(56, 56, 56, 0.95); /* 半透明深灰背景 */
    border-width: 1px;
    border-color: rgb(35, 35, 35);
    border-radius: 4px;
    padding: 8px;
    min-width: 280px;
    max-width: 400px;
    min-height: 200px;
    max-height: 80%;
    overflow: hidden;
    /* 添加阴影效果 */
    -unity-background-image-tint-color: rgba(0, 0, 0, 0.2);
}
