using System;
using UnityEngine.UIElements;

namespace BlastingDesign.UI.DaisyUI.Components.DataInput
{
    /// <summary>
    /// DaisyUI输入框组件 - 公共方法和事件处理部分
    /// 包含公共API方法和事件处理逻辑
    /// </summary>
    public partial class DaisyInput
    {
        #region 公共方法

        /// <summary>
        /// 设置标签
        /// </summary>
        /// <param name="label">标签文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetLabel(string label)
        {
            Label = label;
            return this;
        }

        /// <summary>
        /// 设置占位符
        /// </summary>
        /// <param name="placeholder">占位符文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetPlaceholder(string placeholder)
        {
            Placeholder = placeholder;
            return this;
        }

        /// <summary>
        /// 设置输入类型
        /// </summary>
        /// <param name="type">输入类型</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetType(string type)
        {
            InputType = type;
            return this;
        }

        /// <summary>
        /// 设置帮助文本
        /// </summary>
        /// <param name="text">帮助文本</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetHelperText(string text)
        {
            HelperText = text;
            return this;
        }

        /// <summary>
        /// 设置值
        /// </summary>
        /// <param name="value">输入值</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput SetValue(string value)
        {
            Value = value;
            return this;
        }

        /// <summary>
        /// 注册值改变回调
        /// </summary>
        /// <param name="callback">回调函数</param>
        /// <returns>当前实例，支持链式调用</returns>
        public DaisyInput OnValueChanged(Action<string> callback)
        {
            if (_textField != null && callback != null)
            {
                _textField.RegisterValueChangedCallback(evt => callback(evt.newValue));
            }
            return this;
        }

        /// <summary>
        /// 聚焦输入框
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public new DaisyInput Focus()
        {
            _textField?.Focus();
            return this;
        }

        /// <summary>
        /// 失去焦点
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public new DaisyInput Blur()
        {
            _textField?.Blur();
            return this;
        }

        /// <summary>
        /// 重置组件到初始状态
        /// </summary>
        /// <returns>当前实例，支持链式调用</returns>
        public new DaisyInput Reset()
        {
            Value = string.Empty;
            _currentState = "normal";
            _isDisabled = false;
            _isReadOnly = false;
            UpdateState();
            return this;
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 焦点进入事件
        /// </summary>
        private void OnFocusIn(FocusInEvent evt)
        {
            HidePlaceholder();
            AddToClassList("daisy-input-focused");

            // 键盘事件阻断由TextFieldExtensions.AddFocusBasedKeyboardEventBlocker()自动处理
        }

        /// <summary>
        /// 焦点离开事件
        /// </summary>
        private void OnFocusOut(FocusOutEvent evt)
        {
            if (string.IsNullOrEmpty(_textField.value))
            {
                ShowPlaceholder();
            }
            RemoveFromClassList("daisy-input-focused");

            // 键盘事件阻断的恢复由TextFieldExtensions.AddFocusBasedKeyboardEventBlocker()自动处理
        }

        /// <summary>
        /// 值改变事件
        /// </summary>
        private void OnValueChanged(ChangeEvent<string> evt)
        {
            if (string.IsNullOrEmpty(evt.newValue))
            {
                ShowPlaceholder();
            }
            else
            {
                HidePlaceholder();
            }
        }

        /// <summary>
        /// 占位符点击事件
        /// </summary>
        private void OnPlaceholderClick(ClickEvent evt)
        {
            // 点击占位符时让输入框获取焦点
            _textField?.Focus();
            evt.StopPropagation();
        }

        #endregion
    }
}
