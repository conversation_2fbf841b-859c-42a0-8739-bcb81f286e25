# Dapper + SQL Server 配置指南

## 概述

本指南介绍如何在 Unity 项目中使用 Dapper ORM 连接和操作 SQL Server 数据库，包括基础配置、高级功能和最佳实践。

## 环境要求

### SQL Server 版本支持
- **SQL Server 2016 或更高版本**（推荐）
- **SQL Server Express**（免费版本，适合开发和小型应用）
- **Azure SQL Database**（云端版本）
- **SQL Server LocalDB**（轻量级版本，适合开发）

### .NET 依赖
- **System.Data.SqlClient**（Unity 内置）
- **Microsoft.Data.SqlClient**（推荐，更现代的驱动）
- **Dapper**（通过 NuGet 安装）

## 安装和配置

### 1. 安装 SQL Server

#### SQL Server Express（推荐用于开发）
1. 下载 SQL Server Express：https://www.microsoft.com/sql-server/sql-server-downloads
2. 安装时选择"基本"安装类型
3. 记录服务器名称（通常是 `localhost\SQLEXPRESS`）

#### SQL Server LocalDB（轻量级选项）
```bash
# 通过 Visual Studio Installer 安装
# 或下载独立安装包
```

### 2. 配置数据库

#### 创建测试数据库
```sql
-- 连接到 SQL Server Management Studio (SSMS)
CREATE DATABASE DapperTestDB;
GO

USE DapperTestDB;
GO

-- 创建登录用户（可选）
CREATE LOGIN DapperUser WITH PASSWORD = 'YourPassword123!';
CREATE USER DapperUser FOR LOGIN DapperUser;
ALTER ROLE db_owner ADD MEMBER DapperUser;
```

#### 启用必要功能
```sql
-- 启用 SNAPSHOT 隔离（可选，用于高级事务）
ALTER DATABASE DapperTestDB SET ALLOW_SNAPSHOT_ISOLATION ON;
ALTER DATABASE DapperTestDB SET READ_COMMITTED_SNAPSHOT ON;
```

### 3. Unity 项目配置

#### 连接字符串配置
```csharp
// Windows 身份验证（推荐用于开发）
string connectionString = "Server=localhost\\SQLEXPRESS;Database=DapperTestDB;Integrated Security=true;Connection Timeout=30;";

// SQL Server 身份验证
string connectionString = "Server=localhost\\SQLEXPRESS;Database=DapperTestDB;User Id=DapperUser;Password=YourPassword123!;Connection Timeout=30;";

// LocalDB
string connectionString = "Server=(localdb)\\MSSQLLocalDB;Database=DapperTestDB;Integrated Security=true;";

// Azure SQL Database
string connectionString = "Server=tcp:yourserver.database.windows.net,1433;Database=DapperTestDB;User ID=yourusername;Password=yourpassword;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
```

## 基本使用

### 1. 连接测试

```csharp
using System.Data.SqlClient;
using Dapper;

public async Task TestConnection()
{
    string connectionString = "Server=localhost\\SQLEXPRESS;Database=DapperTestDB;Integrated Security=true;";
    
    try
    {
        using var connection = new SqlConnection(connectionString);
        await connection.OpenAsync();
        
        var version = await connection.QuerySingleAsync<string>("SELECT @@VERSION");
        Debug.Log($"连接成功！SQL Server 版本: {version.Substring(0, 50)}...");
    }
    catch (Exception ex)
    {
        Debug.LogError($"连接失败: {ex.Message}");
    }
}
```

### 2. 基本 CRUD 操作

```csharp
// 创建表
string createTableSql = @"
    CREATE TABLE Users (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        Email NVARCHAR(255) UNIQUE,
        Age INT,
        CreatedAt DATETIME2 DEFAULT GETDATE()
    )";

await connection.ExecuteAsync(createTableSql);

// 插入数据
var user = new { Name = "张三", Email = "<EMAIL>", Age = 25 };
await connection.ExecuteAsync(
    "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)", 
    user);

// 查询数据
var users = await connection.QueryAsync<User>(
    "SELECT Id, Name, Email, Age, CreatedAt FROM Users WHERE Age > @MinAge", 
    new { MinAge = 18 });
```

## SQL Server 特有功能

### 1. 存储过程

```csharp
// 创建存储过程
string createProcSql = @"
    CREATE PROCEDURE GetUsersByAge
        @MinAge INT,
        @Count INT OUTPUT
    AS
    BEGIN
        SELECT @Count = COUNT(*) FROM Users WHERE Age >= @MinAge;
        SELECT * FROM Users WHERE Age >= @MinAge ORDER BY Age;
    END";

await connection.ExecuteAsync(createProcSql);

// 调用存储过程
var parameters = new DynamicParameters();
parameters.Add("@MinAge", 25);
parameters.Add("@Count", dbType: DbType.Int32, direction: ParameterDirection.Output);

var users = await connection.QueryAsync<User>("GetUsersByAge", parameters, 
    commandType: CommandType.StoredProcedure);

int count = parameters.Get<int>("@Count");
```

### 2. 窗口函数

```csharp
string windowFunctionSql = @"
    SELECT 
        Name,
        Age,
        ROW_NUMBER() OVER (ORDER BY Age DESC) as RowNumber,
        RANK() OVER (ORDER BY Age DESC) as Rank,
        DENSE_RANK() OVER (ORDER BY Age DESC) as DenseRank,
        NTILE(4) OVER (ORDER BY Age DESC) as Quartile,
        LAG(Age, 1) OVER (ORDER BY Age) as PreviousAge,
        LEAD(Age, 1) OVER (ORDER BY Age) as NextAge
    FROM Users
    ORDER BY Age DESC";

var results = await connection.QueryAsync(windowFunctionSql);
```

### 3. CTE 和递归查询

```csharp
string recursiveSql = @"
    WITH NumberSeries AS (
        SELECT 1 as Number
        UNION ALL
        SELECT Number + 1 
        FROM NumberSeries 
        WHERE Number < 10
    )
    SELECT Number FROM NumberSeries";

var numbers = await connection.QueryAsync<int>(recursiveSql);
```

### 4. MERGE 语句

```csharp
string mergeSql = @"
    MERGE Users AS target
    USING (VALUES (@Name, @Email, @Age)) AS source (Name, Email, Age)
    ON target.Email = source.Email
    WHEN MATCHED THEN
        UPDATE SET Name = source.Name, Age = source.Age
    WHEN NOT MATCHED THEN
        INSERT (Name, Email, Age) VALUES (source.Name, source.Email, source.Age)
    OUTPUT $action, inserted.*;";

var result = await connection.QueryAsync(mergeSql, 
    new { Name = "李四", Email = "<EMAIL>", Age = 30 });
```

## 高级功能

### 1. 事务和隔离级别

```csharp
// 使用不同隔离级别
using var transaction = connection.BeginTransaction(IsolationLevel.ReadCommitted);

try
{
    await connection.ExecuteAsync(sql1, parameters1, transaction);
    await connection.ExecuteAsync(sql2, parameters2, transaction);
    
    await transaction.CommitAsync();
}
catch
{
    await transaction.RollbackAsync();
    throw;
}

// 使用保存点
using var transaction = connection.BeginTransaction();
await connection.ExecuteAsync("SAVE TRANSACTION SavePoint1", transaction: transaction);
// ... 执行操作
await connection.ExecuteAsync("ROLLBACK TRANSACTION SavePoint1", transaction: transaction);
```

### 2. 批量操作

```csharp
// 批量插入
var users = new List<object>();
for (int i = 0; i < 1000; i++)
{
    users.Add(new { Name = $"User{i}", Email = $"user{i}@example.com", Age = 20 + i % 40 });
}

using var transaction = connection.BeginTransaction();
await connection.ExecuteAsync(
    "INSERT INTO Users (Name, Email, Age) VALUES (@Name, @Email, @Age)",
    users, transaction);
await transaction.CommitAsync();
```

### 3. 表值参数

```csharp
// 创建用户定义表类型
string createTypeSql = @"
    CREATE TYPE UserTableType AS TABLE (
        Name NVARCHAR(100),
        Email NVARCHAR(255),
        Age INT
    )";

// 创建使用表值参数的存储过程
string createProcSql = @"
    CREATE PROCEDURE BulkInsertUsers
        @Users UserTableType READONLY
    AS
    BEGIN
        INSERT INTO Users (Name, Email, Age)
        SELECT Name, Email, Age FROM @Users;
    END";
```

## 性能优化

### 1. 连接池配置

```csharp
// 在连接字符串中配置连接池
string connectionString = @"
    Server=localhost\SQLEXPRESS;
    Database=DapperTestDB;
    Integrated Security=true;
    Connection Timeout=30;
    Min Pool Size=5;
    Max Pool Size=100;
    Pooling=true;";
```

### 2. 查询优化

```csharp
// 使用索引提示
string optimizedQuery = @"
    SELECT * FROM Users WITH (INDEX(IX_Users_Age))
    WHERE Age > @MinAge
    ORDER BY Age";

// 使用查询计划
string queryWithPlan = @"
    SELECT * FROM Users
    WHERE Age > @MinAge
    OPTION (OPTIMIZE FOR (@MinAge = 25))";
```

### 3. 异步操作

```csharp
// 优先使用异步方法
var users = await connection.QueryAsync<User>(sql, parameters);
var count = await connection.ExecuteAsync(sql, parameters);
var scalar = await connection.QuerySingleAsync<int>(sql, parameters);
```

## 错误处理和调试

### 1. 常见错误处理

```csharp
try
{
    // 数据库操作
}
catch (SqlException ex)
{
    switch (ex.Number)
    {
        case 2:     // 连接超时
        case 53:    // 网络错误
            Debug.LogError($"连接错误: {ex.Message}");
            break;
        case 18456: // 登录失败
            Debug.LogError($"身份验证失败: {ex.Message}");
            break;
        case 2627:  // 主键冲突
        case 2601:  // 唯一约束冲突
            Debug.LogError($"数据冲突: {ex.Message}");
            break;
        default:
            Debug.LogError($"SQL错误 ({ex.Number}): {ex.Message}");
            break;
    }
}
catch (Exception ex)
{
    Debug.LogError($"一般错误: {ex.Message}");
}
```

### 2. 连接字符串调试

```csharp
// 测试不同的连接字符串
string[] connectionStrings = {
    "Server=localhost\\SQLEXPRESS;Database=DapperTestDB;Integrated Security=true;",
    "Server=.\\SQLEXPRESS;Database=DapperTestDB;Integrated Security=true;",
    "Server=(local)\\SQLEXPRESS;Database=DapperTestDB;Integrated Security=true;",
    "Server=localhost;Database=DapperTestDB;Integrated Security=true;"
};

foreach (var connStr in connectionStrings)
{
    try
    {
        using var conn = new SqlConnection(connStr);
        conn.Open();
        Debug.Log($"连接成功: {connStr}");
        break;
    }
    catch (Exception ex)
    {
        Debug.Log($"连接失败: {connStr} - {ex.Message}");
    }
}
```

## 部署注意事项

### 1. 生产环境配置

```csharp
// 使用配置文件管理连接字符串
[CreateAssetMenu(fileName = "DatabaseConfig", menuName = "Database/Config")]
public class DatabaseConfig : ScriptableObject
{
    [Header("SQL Server 配置")]
    public string serverName = "localhost\\SQLEXPRESS";
    public string databaseName = "ProductionDB";
    public string username = "";
    public string password = "";
    public bool useWindowsAuth = true;
    public int connectionTimeout = 30;
    
    public string GetConnectionString()
    {
        if (useWindowsAuth)
        {
            return $"Server={serverName};Database={databaseName};Integrated Security=true;Connection Timeout={connectionTimeout};";
        }
        else
        {
            return $"Server={serverName};Database={databaseName};User Id={username};Password={password};Connection Timeout={connectionTimeout};";
        }
    }
}
```

### 2. 安全考虑

- **不要在代码中硬编码密码**
- **使用 Windows 身份验证**（如果可能）
- **限制数据库用户权限**
- **使用 SSL/TLS 加密连接**
- **定期更新密码**

### 3. 监控和日志

```csharp
public class DatabaseLogger
{
    public static void LogQuery(string sql, object parameters, TimeSpan duration)
    {
        if (duration.TotalMilliseconds > 1000) // 慢查询
        {
            Debug.LogWarning($"慢查询 ({duration.TotalMilliseconds:F0}ms): {sql}");
        }
        else
        {
            Debug.Log($"查询 ({duration.TotalMilliseconds:F0}ms): {sql}");
        }
    }
}
```

## 总结

Dapper + SQL Server 为 Unity 项目提供了强大的企业级数据库解决方案，支持：

- **高性能**：接近原生 ADO.NET 性能
- **丰富功能**：存储过程、窗口函数、CTE 等
- **企业特性**：事务、隔离级别、批量操作
- **可扩展性**：支持大型数据库和高并发

通过合理配置和使用，可以构建稳定、高效的数据库应用程序。
