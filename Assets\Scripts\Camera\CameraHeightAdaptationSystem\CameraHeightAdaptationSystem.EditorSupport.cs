using UnityEngine;

namespace BlastingDesign.Core.Camera
{
    /// <summary>
    /// 相机高度自适应系统 - Unity Editor支持和调试功能
    /// </summary>
    public partial class CameraHeightAdaptationSystem
    {
        #region Unity Editor 支持

        void OnDrawGizmosSelected()
        {
            if (!isInitialized || !enableHeightAdaptation) return;

            // 绘制当前存储的距离
            if (hasValidDistance && lastHitPoint != Vector3.zero)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(lastHitPoint, 0.5f);
                Gizmos.DrawLine(targetCamera.transform.position, lastHitPoint);

#if UNITY_EDITOR
                // 绘制距离文本（在Scene视图中）
                UnityEditor.Handles.Label(lastHitPoint, $"Distance: {storedDistance:F2}m");
#endif
            }

            // 绘制屏幕中心点
            if (useScreenCenter)
            {
                Vector2 screenPoint = new Vector2(
                    Screen.width * screenCenterOffset.x,
                    Screen.height * screenCenterOffset.y
                );

                Ray ray = targetCamera.ScreenPointToRay(screenPoint);
                Gizmos.color = Color.yellow;
                Gizmos.DrawRay(ray.origin, ray.direction * 10f);
            }
        }

        #endregion
    }
}