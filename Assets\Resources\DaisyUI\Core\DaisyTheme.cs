using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.DaisyUI.Core
{
    /// <summary>
    /// DaisyUI主题系统
    /// 管理组件的颜色、字体、间距等样式属性
    /// </summary>
    [CreateAssetMenu(fileName = "DaisyTheme", menuName = "DaisyUI/Theme", order = 1)]
    public class DaisyTheme : ScriptableObject
    {
        #region 静态属性
        
        private static DaisyTheme _current;
        private static DaisyTheme _default;
        private static bool _initialized = false;
        
        /// <summary>
        /// 当前激活的主题
        /// </summary>
        public static DaisyTheme Current 
        { 
            get 
            {
                EnsureInitialized();
                return _current;
            }
            private set => _current = value;
        }
        
        /// <summary>
        /// 默认主题
        /// </summary>
        public static DaisyTheme Default 
        { 
            get 
            {
                EnsureInitialized();
                return _default;
            }
            private set => _default = value;
        }
        
        #endregion
        
        #region 主题配置
        
        [Header("主题配置")]
        [SerializeField] private string _themeName = "light";
        [SerializeField] private bool _isDark = false;
        [SerializeField] private string _description = "";
        
        public string themeName => _themeName;
        public bool isDark => _isDark;
        public string description => _description;
        
        #endregion
        
        #region 颜色调色板
        
        [Header("主要颜色")]
        [SerializeField] private Color _primary = new Color(0.231f, 0.510f, 0.965f, 1f); // #3B82F6
        [SerializeField] private Color _primaryContent = Color.white;
        [SerializeField] private Color _primaryFocus = new Color(0.147f, 0.388f, 0.922f, 1f); // #2563EB
        
        [SerializeField] private Color _secondary = new Color(0.420f, 0.447f, 0.502f, 1f); // #6B7280
        [SerializeField] private Color _secondaryContent = Color.white;
        [SerializeField] private Color _secondaryFocus = new Color(0.294f, 0.333f, 0.388f, 1f); // #4B5563
        
        [SerializeField] private Color _accent = new Color(0.024f, 0.714f, 0.831f, 1f); // #06B6D4
        [SerializeField] private Color _accentContent = Color.white;
        [SerializeField] private Color _accentFocus = new Color(0.035f, 0.569f, 0.698f, 1f); // #0891B2
        
        [Header("中性色")]
        [SerializeField] private Color _neutral = new Color(0.216f, 0.255f, 0.318f, 1f); // #374151
        [SerializeField] private Color _neutralContent = Color.white;
        [SerializeField] private Color _neutralFocus = new Color(0.122f, 0.161f, 0.216f, 1f); // #1F2937
        
        [Header("基础色")]
        [SerializeField] private Color _base100 = Color.white; // #FFFFFF
        [SerializeField] private Color _base200 = new Color(0.953f, 0.957f, 0.965f, 1f); // #F3F4F6
        [SerializeField] private Color _base300 = new Color(0.898f, 0.906f, 0.922f, 1f); // #E5E7EB
        [SerializeField] private Color _baseContent = new Color(0.122f, 0.161f, 0.216f, 1f); // #1F2937
        
        [Header("状态颜色")]
        [SerializeField] private Color _info = new Color(0.055f, 0.647f, 0.914f, 1f); // #0EA5E9
        [SerializeField] private Color _infoContent = Color.white;
        
        [SerializeField] private Color _success = new Color(0.063f, 0.725f, 0.506f, 1f); // #10B981
        [SerializeField] private Color _successContent = Color.white;
        
        [SerializeField] private Color _warning = new Color(0.961f, 0.620f, 0.043f, 1f); // #F59E0B
        [SerializeField] private Color _warningContent = Color.white;
        
        [SerializeField] private Color _error = new Color(0.937f, 0.267f, 0.267f, 1f); // #EF4444
        [SerializeField] private Color _errorContent = Color.white;
        
        // 颜色属性
        public Color primary => _primary;
        public Color primaryContent => _primaryContent;
        public Color primaryFocus => _primaryFocus;
        public Color secondary => _secondary;
        public Color secondaryContent => _secondaryContent;
        public Color secondaryFocus => _secondaryFocus;
        public Color accent => _accent;
        public Color accentContent => _accentContent;
        public Color accentFocus => _accentFocus;
        public Color neutral => _neutral;
        public Color neutralContent => _neutralContent;
        public Color neutralFocus => _neutralFocus;
        public Color base100 => _base100;
        public Color base200 => _base200;
        public Color base300 => _base300;
        public Color baseContent => _baseContent;
        public Color info => _info;
        public Color infoContent => _infoContent;
        public Color success => _success;
        public Color successContent => _successContent;
        public Color warning => _warning;
        public Color warningContent => _warningContent;
        public Color error => _error;
        public Color errorContent => _errorContent;
        
        #endregion
        
        #region 字体设置
        
        [Header("字体设置")]
        [SerializeField] private int _baseFontSize = 14;
        [SerializeField] private FontDefinition _primaryFont;
        [SerializeField] private FontDefinition _secondaryFont;
        [SerializeField] private FontDefinition _monoFont;
        
        public int baseFontSize => _baseFontSize;
        public FontDefinition primaryFont => _primaryFont;
        public FontDefinition secondaryFont => _secondaryFont;
        public FontDefinition monoFont => _monoFont;
        
        #endregion
        
        #region 间距设置
        
        [Header("间距设置")]
        [SerializeField] private float _baseSpacing = 4f;
        [SerializeField] private float[] _spacingScale = { 2, 4, 8, 12, 16, 24, 32, 48, 64 };
        
        public float baseSpacing => _baseSpacing;
        public float[] spacingScale => _spacingScale;
        
        #endregion
        
        #region 边框设置
        
        [Header("边框设置")]
        [SerializeField] private float _borderRadius = 8f;
        [SerializeField] private float _borderRadiusLg = 12f;
        [SerializeField] private float _borderRadiusXl = 16f;
        [SerializeField] private float _borderWidth = 1f;
        
        public float borderRadius => _borderRadius;
        public float borderRadiusLg => _borderRadiusLg;
        public float borderRadiusXl => _borderRadiusXl;
        public float borderWidth => _borderWidth;
        
        #endregion
        
        #region 阴影设置
        
        [Header("阴影设置")]
        [SerializeField] private Color _shadowColor = new Color(0f, 0f, 0f, 0.1f);
        [SerializeField] private Vector2 _shadowOffset = new Vector2(0f, 1f);
        [SerializeField] private float _shadowBlur = 3f;
        [SerializeField] private float _shadowSpread = 0f;
        
        public Color shadowColor => _shadowColor;
        public Vector2 shadowOffset => _shadowOffset;
        public float shadowBlur => _shadowBlur;
        public float shadowSpread => _shadowSpread;
        
        #endregion
        
        #region 动画设置
        
        [Header("动画设置")]
        [SerializeField] private float _transitionDuration = 0.2f;
        [SerializeField] private string _transitionEasing = "ease-in-out";
        
        public float transitionDuration => _transitionDuration;
        public string transitionEasing => _transitionEasing;
        
        #endregion
        
        #region 初始化方法
        
        /// <summary>
        /// 确保主题系统已初始化
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_initialized)
            {
                InitializeThemeSystem();
                _initialized = true;
            }
        }
        
        /// <summary>
        /// 初始化主题系统
        /// </summary>
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        private static void InitializeThemeSystem()
        {
            // 尝试加载默认主题
            LoadDefaultTheme();
        }
        
        /// <summary>
        /// 加载默认主题
        /// </summary>
        private static void LoadDefaultTheme()
        {
            try
            {
                // 尝试从Resources加载默认主题
                var defaultTheme = Resources.Load<DaisyTheme>("DaisyUI/Themes/DefaultTheme");
                if (defaultTheme != null)
                {
                    _default = defaultTheme;
                    _current = defaultTheme;
                    Logging.LogInfo("DaisyTheme", $"加载默认主题: {defaultTheme.themeName}");
                }
                else
                {
                    // 如果没有找到默认主题，创建一个临时的
                    CreateRuntimeDefaultTheme();
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogWarning("DaisyTheme", $"加载默认主题失败: {ex.Message}，将创建运行时默认主题");
                CreateRuntimeDefaultTheme();
            }
        }
        
        /// <summary>
        /// 创建运行时默认主题
        /// </summary>
        private static void CreateRuntimeDefaultTheme()
        {
            var theme = CreateInstance<DaisyTheme>();
            theme._themeName = "default";
            theme._description = "运行时默认主题";
            
            _default = theme;
            _current = theme;
            
            Logging.LogInfo("DaisyTheme", "创建运行时默认主题");
        }
        
        #endregion
        
        #region 主题应用方法
        
        /// <summary>
        /// 应用主题到指定元素
        /// </summary>
        /// <param name="element">目标元素</param>
        public void Apply(VisualElement element)
        {
            if (element == null)
            {
                Logging.LogWarning("DaisyTheme", "目标元素为null，无法应用主题");
                return;
            }
            
            try
            {
                // 应用颜色变量
                ApplyColorVariables(element);
                
                // 应用字体变量
                ApplyFontVariables(element);
                
                // 应用间距变量
                ApplySpacingVariables(element);
                
                // 应用边框变量
                ApplyBorderVariables(element);
                
                // 应用阴影变量
                ApplyShadowVariables(element);
                
                // 应用动画变量
                ApplyAnimationVariables(element);
                
                // 添加主题类名
                element.AddToClassList($"theme-{_themeName}");
                if (_isDark)
                {
                    element.AddToClassList("theme-dark");
                }
                else
                {
                    element.AddToClassList("theme-light");
                }
                
                Logging.LogInfo("DaisyTheme", $"主题 {_themeName} 应用成功");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("DaisyTheme", $"应用主题失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 应用颜色变量
        /// </summary>
        private void ApplyColorVariables(VisualElement element)
        {
            // Unity UI Toolkit 不支持动态设置CSS变量
            // 主题应用通过添加CSS类名来实现
            // 实际的颜色值将通过USS文件中的CSS变量应用
            
            // 添加颜色相关的数据属性，供USS文件使用
            element.style.color = _baseContent;
            element.style.backgroundColor = _base100;
            
            // 为每个颜色添加自定义属性（通过类名传递）
            element.AddToClassList($"daisy-primary-{ColorToSafeClassName(_primary)}");
            element.AddToClassList($"daisy-secondary-{ColorToSafeClassName(_secondary)}");
            element.AddToClassList($"daisy-accent-{ColorToSafeClassName(_accent)}");
        }
        
        /// <summary>
        /// 将颜色转换为安全的CSS类名
        /// </summary>
        private string ColorToSafeClassName(Color color)
        {
            var hex = DaisyUtilities.ColorToHex(color).Replace("#", "");
            return $"c{hex.ToLower()}";
        }
        
        /// <summary>
        /// 应用字体变量
        /// </summary>
        private void ApplyFontVariables(VisualElement element)
        {
            // Unity UI Toolkit 不支持动态设置CSS变量
            // 字体设置通过CSS类名和直接样式应用
            
            if (_primaryFont.fontAsset != null)
            {
                element.style.unityFontDefinition = _primaryFont;
            }
            
            // 字体大小通过类名控制
            element.AddToClassList($"daisy-font-size-{_baseFontSize}");
        }
        
        /// <summary>
        /// 应用间距变量
        /// </summary>
        private void ApplySpacingVariables(VisualElement element)
        {
            // Unity UI Toolkit 不支持动态设置CSS变量
            // 间距通过CSS类名控制，实际值在USS文件中定义
            
            // 添加基础间距类名，供USS文件使用
            element.AddToClassList($"daisy-spacing-base-{_baseSpacing}");
            
            // 为间距比例添加数据属性
            for (int i = 0; i < _spacingScale.Length && i < 9; i++)
            {
                element.AddToClassList($"daisy-spacing-{i}-{_spacingScale[i]}");
            }
        }
        
        /// <summary>
        /// 应用边框变量
        /// </summary>
        private void ApplyBorderVariables(VisualElement element)
        {
            // Unity UI Toolkit 不支持动态设置CSS变量
            // 边框设置通过直接样式属性和CSS类名控制
            
            // 设置基础边框样式
            element.style.borderTopLeftRadius = _borderRadius;
            element.style.borderTopRightRadius = _borderRadius;
            element.style.borderBottomLeftRadius = _borderRadius;
            element.style.borderBottomRightRadius = _borderRadius;
            element.style.borderLeftWidth = _borderWidth;
            element.style.borderRightWidth = _borderWidth;
            element.style.borderTopWidth = _borderWidth;
            element.style.borderBottomWidth = _borderWidth;
            
            // 添加边框样式类名，供USS文件使用
            element.AddToClassList($"daisy-border-radius-{_borderRadius}");
            element.AddToClassList($"daisy-border-width-{_borderWidth}");
        }
        
        /// <summary>
        /// 应用阴影变量
        /// </summary>
        private void ApplyShadowVariables(VisualElement element)
        {
            // Unity UI Toolkit 不完全支持box-shadow
            // 阴影效果通过CSS类名在USS文件中定义
            
            var shadowColorHex = DaisyUtilities.ColorToHex(_shadowColor).Replace("#", "");
            element.AddToClassList($"daisy-shadow-{shadowColorHex.ToLower()}");
            element.AddToClassList($"daisy-shadow-offset-{_shadowOffset.x}-{_shadowOffset.y}");
            element.AddToClassList($"daisy-shadow-blur-{_shadowBlur}");
        }
        
        /// <summary>
        /// 应用动画变量
        /// </summary>
        private void ApplyAnimationVariables(VisualElement element)
        {
            // Unity UI Toolkit 不支持动态设置CSS变量
            // 动画参数通过CSS类名控制，实际值在USS文件中定义
            
            // 将动画持续时间转换为类名友好的格式
            var durationMs = Mathf.RoundToInt(_transitionDuration * 1000);
            element.AddToClassList($"daisy-transition-{durationMs}ms");
            
            // 动画缓动函数通过类名控制
            var easingClass = _transitionEasing.Replace("-", "").ToLower();
            element.AddToClassList($"daisy-transition-{easingClass}");
        }
        
        #endregion
        
        #region 主题管理方法
        
        /// <summary>
        /// 设置为当前主题
        /// </summary>
        public void SetAsCurrent()
        {
            EnsureInitialized();
            _current = this;
            Logging.LogInfo("DaisyTheme", $"切换到主题: {_themeName}");
        }
        
        /// <summary>
        /// 应用到全局根元素
        /// </summary>
        public void ApplyGlobal()
        {
            // 查找所有UIDocument
            var uiDocuments = FindObjectsByType<UIDocument>(FindObjectsSortMode.None);
            
            foreach (var doc in uiDocuments)
            {
                if (doc.rootVisualElement != null)
                {
                    Apply(doc.rootVisualElement);
                }
            }
            
            SetAsCurrent();
        }
        
        /// <summary>
        /// 创建主题副本
        /// </summary>
        /// <param name="newName">新主题名称</param>
        /// <returns>主题副本</returns>
        public DaisyTheme CreateCopy(string newName)
        {
            var copy = CreateInstance<DaisyTheme>();
            
            // 复制所有属性
            copy._themeName = newName;
            copy._isDark = _isDark;
            copy._description = $"基于 {_themeName} 的副本";
            
            // 复制颜色
            copy._primary = _primary;
            copy._primaryContent = _primaryContent;
            copy._primaryFocus = _primaryFocus;
            copy._secondary = _secondary;
            copy._secondaryContent = _secondaryContent;
            copy._secondaryFocus = _secondaryFocus;
            copy._accent = _accent;
            copy._accentContent = _accentContent;
            copy._accentFocus = _accentFocus;
            copy._neutral = _neutral;
            copy._neutralContent = _neutralContent;
            copy._neutralFocus = _neutralFocus;
            copy._base100 = _base100;
            copy._base200 = _base200;
            copy._base300 = _base300;
            copy._baseContent = _baseContent;
            copy._info = _info;
            copy._infoContent = _infoContent;
            copy._success = _success;
            copy._successContent = _successContent;
            copy._warning = _warning;
            copy._warningContent = _warningContent;
            copy._error = _error;
            copy._errorContent = _errorContent;
            
            // 复制其他设置
            copy._baseFontSize = _baseFontSize;
            copy._primaryFont = _primaryFont;
            copy._secondaryFont = _secondaryFont;
            copy._monoFont = _monoFont;
            copy._baseSpacing = _baseSpacing;
            copy._spacingScale = (float[])_spacingScale.Clone();
            copy._borderRadius = _borderRadius;
            copy._borderRadiusLg = _borderRadiusLg;
            copy._borderRadiusXl = _borderRadiusXl;
            copy._borderWidth = _borderWidth;
            copy._shadowColor = _shadowColor;
            copy._shadowOffset = _shadowOffset;
            copy._shadowBlur = _shadowBlur;
            copy._shadowSpread = _shadowSpread;
            copy._transitionDuration = _transitionDuration;
            copy._transitionEasing = _transitionEasing;
            
            return copy;
        }
        
        /// <summary>
        /// 验证主题配置
        /// </summary>
        /// <returns>验证结果</returns>
        public bool ValidateTheme()
        {
            if (string.IsNullOrEmpty(_themeName))
            {
                Logging.LogError("DaisyTheme", "主题名称不能为空");
                return false;
            }
            
            if (_baseFontSize <= 0)
            {
                Logging.LogError("DaisyTheme", "基础字体大小必须大于0");
                return false;
            }
            
            if (_baseSpacing < 0)
            {
                Logging.LogError("DaisyTheme", "基础间距不能为负数");
                return false;
            }
            
            if (_borderRadius < 0)
            {
                Logging.LogError("DaisyTheme", "边框圆角不能为负数");
                return false;
            }
            
            if (_transitionDuration < 0)
            {
                Logging.LogError("DaisyTheme", "过渡持续时间不能为负数");
                return false;
            }
            
            return true;
        }
        
        #endregion
        
        #region Unity生命周期
        
        private void OnValidate()
        {
            // 确保值在合理范围内
            _baseFontSize = Mathf.Max(1, _baseFontSize);
            _baseSpacing = Mathf.Max(0, _baseSpacing);
            _borderRadius = Mathf.Max(0, _borderRadius);
            _borderRadiusLg = Mathf.Max(0, _borderRadiusLg);
            _borderRadiusXl = Mathf.Max(0, _borderRadiusXl);
            _borderWidth = Mathf.Max(0, _borderWidth);
            _transitionDuration = Mathf.Max(0, _transitionDuration);
        }
        
        #endregion
    }
}